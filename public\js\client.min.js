!function(e,i){if("object"==typeof exports&&"object"==typeof module)module.exports=i();else if("function"==typeof define&&define.amd)define([],i);else{var t=i();for(var n in t)("object"==typeof exports?exports:e)[n]=t[n]}}(this,(function(){return function(e){var i={};function t(n){if(i[n])return i[n].exports;var r=i[n]={i:n,l:!1,exports:{}};return e[n].call(r.exports,r,r.exports,t),r.l=!0,r.exports}return t.m=e,t.c=i,t.d=function(e,i,n){t.o(e,i)||Object.defineProperty(e,i,{enumerable:!0,get:n})},t.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},t.t=function(e,i){if(1&i&&(e=t(e)),8&i)return e;if(4&i&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(t.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&i&&"string"!=typeof e)for(var r in e)t.d(n,r,function(i){return e[i]}.bind(null,r));return n},t.n=function(e){var i=e&&e.__esModule?function(){return e["default"]}:function(){return e};return t.d(i,"a",i),i},t.o=function(e,i){return Object.prototype.hasOwnProperty.call(e,i)},t.p="",t(t.s=11)}([function(e,i,t){"use strict";var n,r,o=t(1)(),a=t(3),s=t(4),l=t(5),u=function(){var e=new s;return n=e.getResult(),r=new l,this};u.prototype={getSoftwareVersion:function(){return"0.1.11"},getBrowserData:function(){return n},getFingerprint:function(){var e="|",i=n.ua,t=this.getScreenPrint(),r=this.getPlugins(),o=this.getFonts(),s=this.isLocalStorage(),l=this.isSessionStorage(),u=this.getTimeZone(),c=this.getLanguage(),d=this.getSystemLanguage(),p=this.isCookie(),f=this.getCanvasPrint();return a(i+e+t+e+r+e+o+e+s+e+l+e+u+e+c+e+d+e+p+e+f,256)},getCustomFingerprint:function(){for(var e="|",i="",t=0;t<arguments.length;t++)i+=arguments[t]+e;return a(i,256)},getUserAgent:function(){return n.ua},getUserAgentLowerCase:function(){return n.ua.toLowerCase()},getBrowser:function(){return n.browser.name},getBrowserVersion:function(){return n.browser.version},getBrowserMajorVersion:function(){return n.browser.major},isIE:function(){return/IE/i.test(n.browser.name)},isChrome:function(){return/Chrome/i.test(n.browser.name)},isFirefox:function(){return/Firefox/i.test(n.browser.name)},isSafari:function(){return/Safari/i.test(n.browser.name)},isMobileSafari:function(){return/Mobile\sSafari/i.test(n.browser.name)},isOpera:function(){return/Opera/i.test(n.browser.name)},getEngine:function(){return n.engine.name},getEngineVersion:function(){return n.engine.version},getOS:function(){return n.os.name},getOSVersion:function(){return n.os.version},isWindows:function(){return/Windows/i.test(n.os.name)},isMac:function(){return/Mac/i.test(n.os.name)},isLinux:function(){return/Linux/i.test(n.os.name)},isUbuntu:function(){return/Ubuntu/i.test(n.os.name)},isSolaris:function(){return/Solaris/i.test(n.os.name)},getDevice:function(){return n.device.model},getDeviceType:function(){return n.device.type},getDeviceVendor:function(){return n.device.vendor},getCPU:function(){return n.cpu.architecture},isMobile:function(){var e=n.ua||navigator.vendor||window.opera;return/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows (ce|phone)|xda|xiino/i.test(e)||/1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(e.substr(0,4))},isMobileMajor:function(){return this.isMobileAndroid()||this.isMobileBlackBerry()||this.isMobileIOS()||this.isMobileOpera()||this.isMobileWindows()},isMobileAndroid:function(){return!!n.ua.match(/Android/i)},isMobileOpera:function(){return!!n.ua.match(/Opera Mini/i)},isMobileWindows:function(){return!!n.ua.match(/IEMobile/i)},isMobileBlackBerry:function(){return!!n.ua.match(/BlackBerry/i)},isMobileIOS:function(){return!!n.ua.match(/iPhone|iPad|iPod/i)},isIphone:function(){return!!n.ua.match(/iPhone/i)},isIpad:function(){return!!n.ua.match(/iPad/i)},isIpod:function(){return!!n.ua.match(/iPod/i)},getScreenPrint:function(){return"Current Resolution: "+this.getCurrentResolution()+", Available Resolution: "+this.getAvailableResolution()+", Color Depth: "+this.getColorDepth()+", Device XDPI: "+this.getDeviceXDPI()+", Device YDPI: "+this.getDeviceYDPI()},getColorDepth:function(){return screen.colorDepth},getCurrentResolution:function(){return screen.width+"x"+screen.height},getAvailableResolution:function(){return screen.availWidth+"x"+screen.availHeight},getDeviceXDPI:function(){return screen.deviceXDPI},getDeviceYDPI:function(){return screen.deviceYDPI},getPlugins:function(){for(var e="",i=0;i<navigator.plugins.length;i++)i==navigator.plugins.length-1?e+=navigator.plugins[i].name:e+=navigator.plugins[i].name+", ";return e},isJava:function(){return navigator.javaEnabled()},getJavaVersion:function(){throw new Error("Please use client.java.js or client.js if you need this functionality!")},isFlash:function(){return!!navigator.plugins["Shockwave Flash"]},getFlashVersion:function(){throw new Error("Please use client.flash.js or client.js if you need this functionality!")},isSilverlight:function(){return!!navigator.plugins["Silverlight Plug-In"]},getSilverlightVersion:function(){return this.isSilverlight()?navigator.plugins["Silverlight Plug-In"].description:""},isMimeTypes:function(){return!(!navigator.mimeTypes||!navigator.mimeTypes.length)},getMimeTypes:function(){var e="";if(navigator.mimeTypes)for(var i=0;i<navigator.mimeTypes.length;i++)i==navigator.mimeTypes.length-1?e+=navigator.mimeTypes[i].description:e+=navigator.mimeTypes[i].description+", ";return e},isFont:function(e){return r.detect(e)},getFonts:function(){for(var e=["Abadi MT Condensed Light","Adobe Fangsong Std","Adobe Hebrew","Adobe Ming Std","Agency FB","Aharoni","Andalus","Angsana New","AngsanaUPC","Aparajita","Arab","Arabic Transparent","Arabic Typesetting","Arial Baltic","Arial Black","Arial CE","Arial CYR","Arial Greek","Arial TUR","Arial","Batang","BatangChe","Bauhaus 93","Bell MT","Bitstream Vera Serif","Bodoni MT","Bookman Old Style","Braggadocio","Broadway","Browallia New","BrowalliaUPC","Calibri Light","Calibri","Californian FB","Cambria Math","Cambria","Candara","Castellar","Casual","Centaur","Century Gothic","Chalkduster","Colonna MT","Comic Sans MS","Consolas","Constantia","Copperplate Gothic Light","Corbel","Cordia New","CordiaUPC","Courier New Baltic","Courier New CE","Courier New CYR","Courier New Greek","Courier New TUR","Courier New","DFKai-SB","DaunPenh","David","DejaVu LGC Sans Mono","Desdemona","DilleniaUPC","DokChampa","Dotum","DotumChe","Ebrima","Engravers MT","Eras Bold ITC","Estrangelo Edessa","EucrosiaUPC","Euphemia","Eurostile","FangSong","Forte","FrankRuehl","Franklin Gothic Heavy","Franklin Gothic Medium","FreesiaUPC","French Script MT","Gabriola","Gautami","Georgia","Gigi","Gisha","Goudy Old Style","Gulim","GulimChe","GungSeo","Gungsuh","GungsuhChe","Haettenschweiler","Harrington","Hei S","HeiT","Heisei Kaku Gothic","Hiragino Sans GB","Impact","Informal Roman","IrisUPC","Iskoola Pota","JasmineUPC","KacstOne","KaiTi","Kalinga","Kartika","Khmer UI","Kino MT","KodchiangUPC","Kokila","Kozuka Gothic Pr6N","Lao UI","Latha","Leelawadee","Levenim MT","LilyUPC","Lohit Gujarati","Loma","Lucida Bright","Lucida Console","Lucida Fax","Lucida Sans Unicode","MS Gothic","MS Mincho","MS PGothic","MS PMincho","MS Reference Sans Serif","MS UI Gothic","MV Boli","Magneto","Malgun Gothic","Mangal","Marlett","Matura MT Script Capitals","Meiryo UI","Meiryo","Menlo","Microsoft Himalaya","Microsoft JhengHei","Microsoft New Tai Lue","Microsoft PhagsPa","Microsoft Sans Serif","Microsoft Tai Le","Microsoft Uighur","Microsoft YaHei","Microsoft Yi Baiti","MingLiU","MingLiU-ExtB","MingLiU_HKSCS","MingLiU_HKSCS-ExtB","Miriam Fixed","Miriam","Mongolian Baiti","MoolBoran","NSimSun","Narkisim","News Gothic MT","Niagara Solid","Nyala","PMingLiU","PMingLiU-ExtB","Palace Script MT","Palatino Linotype","Papyrus","Perpetua","Plantagenet Cherokee","Playbill","Prelude Bold","Prelude Condensed Bold","Prelude Condensed Medium","Prelude Medium","PreludeCompressedWGL Black","PreludeCompressedWGL Bold","PreludeCompressedWGL Light","PreludeCompressedWGL Medium","PreludeCondensedWGL Black","PreludeCondensedWGL Bold","PreludeCondensedWGL Light","PreludeCondensedWGL Medium","PreludeWGL Black","PreludeWGL Bold","PreludeWGL Light","PreludeWGL Medium","Raavi","Rachana","Rockwell","Rod","Sakkal Majalla","Sawasdee","Script MT Bold","Segoe Print","Segoe Script","Segoe UI Light","Segoe UI Semibold","Segoe UI Symbol","Segoe UI","Shonar Bangla","Showcard Gothic","Shruti","SimHei","SimSun","SimSun-ExtB","Simplified Arabic Fixed","Simplified Arabic","Snap ITC","Sylfaen","Symbol","Tahoma","Times New Roman Baltic","Times New Roman CE","Times New Roman CYR","Times New Roman Greek","Times New Roman TUR","Times New Roman","TlwgMono","Traditional Arabic","Trebuchet MS","Tunga","Tw Cen MT Condensed Extra Bold","Ubuntu","Umpush","Univers","Utopia","Utsaah","Vani","Verdana","Vijaya","Vladimir Script","Vrinda","Webdings","Wide Latin","Wingdings"],i="",t=0;t<e.length;t++)r.detect(e[t])&&(i+=t==e.length-1?e[t]:e[t]+", ");return i},isLocalStorage:function(){try{return!!o.localStorage}catch(e){return!0}},isSessionStorage:function(){try{return!!o.sessionStorage}catch(e){return!0}},isCookie:function(){return navigator.cookieEnabled},getTimeZone:function(){var e,i;return e=new Date,(i=String(-e.getTimezoneOffset()/60))<0?"-"+("0"+(i*=-1)).slice(-2):"+"+("0"+i).slice(-2)},getLanguage:function(){return navigator.language},getSystemLanguage:function(){return navigator.systemLanguage||window.navigator.language},isCanvas:function(){var e=document.createElement("canvas");try{return!(!e.getContext||!e.getContext("2d"))}catch(i){return!1}},getCanvasPrint:function(){var e,i=document.createElement("canvas");try{e=i.getContext("2d")}catch(n){return""}var t="ClientJS,org <canvas> 1.0";return e.textBaseline="top",e.font="14px 'Arial'",e.textBaseline="alphabetic",e.fillStyle="#f60",e.fillRect(125,1,62,20),e.fillStyle="#069",e.fillText(t,2,15),e.fillStyle="rgba(102, 204, 0, 0.7)",e.fillText(t,4,17),i.toDataURL()}},i.ClientJS=u},function(e,i,t){"use strict";var n=t(2);e.exports=function(){return"object"==typeof global&&global&&global.Math===Math&&global.Array===Array?global:n}},function(e,i,t){"use strict";"undefined"!=typeof self?e.exports=self:"undefined"!=typeof window?e.exports=window:e.exports=Function("return this")()},function(e,i,t){e.exports=function(e,i){var t,n,r,o,a,s,l,u;for(t=3&e.length,n=e.length-t,r=i,a=3432918353,s=461845907,u=0;u<n;)l=255&e.charCodeAt(u)|(255&e.charCodeAt(++u))<<8|(255&e.charCodeAt(++u))<<16|(255&e.charCodeAt(++u))<<24,++u,r=27492+(65535&(o=5*(65535&(r=(r^=l=(65535&(l=(l=(65535&l)*a+(((l>>>16)*a&65535)<<16)&**********)<<15|l>>>17))*s+(((l>>>16)*s&65535)<<16)&**********)<<13|r>>>19))+((5*(r>>>16)&65535)<<16)&**********))+((58964+(o>>>16)&65535)<<16);switch(l=0,t){case 3:l^=(255&e.charCodeAt(u+2))<<16;case 2:l^=(255&e.charCodeAt(u+1))<<8;case 1:r^=l=(65535&(l=(l=(65535&(l^=255&e.charCodeAt(u)))*a+(((l>>>16)*a&65535)<<16)&**********)<<15|l>>>17))*s+(((l>>>16)*s&65535)<<16)&**********}return r^=e.length,r=***********(65535&(r^=r>>>16))+((***********(r>>>16)&65535)<<16)&**********,r=***********(65535&(r^=r>>>13))+((***********(r>>>16)&65535)<<16)&**********,(r^=r>>>16)>>>0}},function(e,i,t){var n;
/*!@license
 * UAParser.js v0.7.28
 * Lightweight JavaScript-based User-Agent string parser
 * https://github.com/faisalman/ua-parser-js
 *
 * Copyright © 2012-2021 Faisal Salman <<EMAIL>>
 * Licensed under MIT License
 */!function(r,o){"use strict";var a="function",s="undefined",l="object",u="string",c="model",d="name",p="type",f="vendor",h="version",m="architecture",g="console",w="mobile",v="tablet",b="smarttv",y="wearable",x="embedded",S={extend:function(e,i){var t={};for(var n in e)i[n]&&i[n].length%2==0?t[n]=i[n].concat(e[n]):t[n]=e[n];return t},has:function(e,i){return typeof e===u&&-1!==i.toLowerCase().indexOf(e.toLowerCase())},lowerize:function(e){return e.toLowerCase()},major:function(e){return typeof e===u?e.replace(/[^\d\.]/g,"").split(".")[0]:o},trim:function(e,i){return e=e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),typeof i===s?e:e.substring(0,255)}},C={rgx:function(e,i){for(var t,n,r,s,u,c,d=0;d<i.length&&!u;){var p=i[d],f=i[d+1];for(t=n=0;t<p.length&&!u;)if(u=p[t++].exec(e))for(r=0;r<f.length;r++)c=u[++n],typeof(s=f[r])===l&&s.length>0?2==s.length?typeof s[1]==a?this[s[0]]=s[1].call(this,c):this[s[0]]=s[1]:3==s.length?typeof s[1]!==a||s[1].exec&&s[1].test?this[s[0]]=c?c.replace(s[1],s[2]):o:this[s[0]]=c?s[1].call(this,c,s[2]):o:4==s.length&&(this[s[0]]=c?s[3].call(this,c.replace(s[1],s[2])):o):this[s]=c||o;d+=2}},str:function(e,i){for(var t in i)if(typeof i[t]===l&&i[t].length>0){for(var n=0;n<i[t].length;n++)if(S.has(i[t][n],e))return"?"===t?o:t}else if(S.has(i[t],e))return"?"===t?o:t;return e}},T={browser:{oldSafari:{version:{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}},oldEdge:{version:{.1:"12.",21:"13.",31:"14.",39:"15.",41:"16.",42:"17.",44:"18."}}},os:{windows:{version:{ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"}}}},k={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[h,[d,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[h,[d,"Edge"]],[/(opera\smini)\/([\w\.-]+)/i,/(opera\s[mobiletab]{3,6})\b.+version\/([\w\.-]+)/i,/(opera)(?:.+version\/|[\/\s]+)([\w\.]+)/i],[d,h],[/opios[\/\s]+([\w\.]+)/i],[h,[d,"Opera Mini"]],[/\sopr\/([\w\.]+)/i],[h,[d,"Opera"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/\s]?([\w\.]*)/i,/(avant\s|iemobile|slim)(?:browser)?[\/\s]?([\w\.]*)/i,/(ba?idubrowser)[\/\s]?([\w\.]+)/i,/(?:ms|\()(ie)\s([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon)\/([\w\.-]+)/i,/(rekonq|puffin|brave|whale|qqbrowserlite|qq)\/([\w\.]+)/i,/(weibo)__([\d\.]+)/i],[d,h],[/(?:[\s\/]uc?\s?browser|(?:juc.+)ucweb)[\/\s]?([\w\.]+)/i],[h,[d,"UCBrowser"]],[/(?:windowswechat)?\sqbcore\/([\w\.]+)\b.*(?:windowswechat)?/i],[h,[d,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[h,[d,"WeChat"]],[/konqueror\/([\w\.]+)/i],[h,[d,"Konqueror"]],[/trident.+rv[:\s]([\w\.]{1,9})\b.+like\sgecko/i],[h,[d,"IE"]],[/yabrowser\/([\w\.]+)/i],[h,[d,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[d,/(.+)/,"$1 Secure Browser"],h],[/focus\/([\w\.]+)/i],[h,[d,"Firefox Focus"]],[/opt\/([\w\.]+)/i],[h,[d,"Opera Touch"]],[/coc_coc_browser\/([\w\.]+)/i],[h,[d,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[h,[d,"Dolphin"]],[/coast\/([\w\.]+)/i],[h,[d,"Opera Coast"]],[/xiaomi\/miuibrowser\/([\w\.]+)/i],[h,[d,"MIUI Browser"]],[/fxios\/([\w\.-]+)/i],[h,[d,"Firefox"]],[/(qihu|qhbrowser|qihoobrowser|360browser)/i],[[d,"360 Browser"]],[/(oculus|samsung|sailfish)browser\/([\w\.]+)/i],[[d,/(.+)/,"$1 Browser"],h],[/(comodo_dragon)\/([\w\.]+)/i],[[d,/_/g," "],h],[/\s(electron)\/([\w\.]+)\ssafari/i,/(tesla)(?:\sqtcarbrowser|\/(20[12]\d\.[\w\.-]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/\s]?([\w\.]+)/i],[d,h],[/(MetaSr)[\/\s]?([\w\.]+)/i,/(LBBROWSER)/i],[d],[/;fbav\/([\w\.]+);/i],[h,[d,"Facebook"]],[/FBAN\/FBIOS|FB_IAB\/FB4A/i],[[d,"Facebook"]],[/safari\s(line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/\s]([\w\.-]+)/i],[d,h],[/\bgsa\/([\w\.]+)\s.*safari\//i],[h,[d,"GSA"]],[/headlesschrome(?:\/([\w\.]+)|\s)/i],[h,[d,"Chrome Headless"]],[/\swv\).+(chrome)\/([\w\.]+)/i],[[d,"Chrome WebView"],h],[/droid.+\sversion\/([\w\.]+)\b.+(?:mobile\ssafari|safari)/i],[h,[d,"Android Browser"]],[/(chrome|omniweb|arora|[tizenoka]{5}\s?browser)\/v?([\w\.]+)/i],[d,h],[/version\/([\w\.]+)\s.*mobile\/\w+\s(safari)/i],[h,[d,"Mobile Safari"]],[/version\/([\w\.]+)\s.*(mobile\s?safari|safari)/i],[h,d],[/webkit.+?(mobile\s?safari|safari)(\/[\w\.]+)/i],[d,[h,C.str,T.browser.oldSafari.version]],[/(webkit|khtml)\/([\w\.]+)/i],[d,h],[/(navigator|netscape)\/([\w\.-]+)/i],[[d,"Netscape"],h],[/ile\svr;\srv:([\w\.]+)\).+firefox/i],[h,[d,"Firefox Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo\sbrowser|minimo|conkeror)[\/\s]?([\w\.\+]+)/i,/(firefox|seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([\w\.-]+)$/i,/(firefox)\/([\w\.]+)\s[\w\s\-]+\/[\w\.]+$/i,/(mozilla)\/([\w\.]+)\s.+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir)[\/\s]?([\w\.]+)/i,/(links)\s\(([\w\.]+)/i,/(gobrowser)\/?([\w\.]*)/i,/(ice\s?browser)\/v?([\w\._]+)/i,/(mosaic)[\/\s]([\w\.]+)/i],[d,h]],cpu:[[/(?:(amd|x(?:(?:86|64)[_-])?|wow|win)64)[;\)]/i],[[m,"amd64"]],[/(ia32(?=;))/i],[[m,S.lowerize]],[/((?:i[346]|x)86)[;\)]/i],[[m,"ia32"]],[/\b(aarch64|armv?8e?l?)\b/i],[[m,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[m,"armhf"]],[/windows\s(ce|mobile);\sppc;/i],[[m,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?:\smac|;|\))/i],[[m,/ower/,"",S.lowerize]],[/(sun4\w)[;\)]/i],[[m,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?:64|(?=v(?:[1-7]|[5-7]1)l?|;|eabi))|(?=atmel\s)avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[m,S.lowerize]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[pt]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus\s10)/i],[c,[f,"Samsung"],[p,v]],[/\b((?:s[cgp]h|gt|sm)-\w+|galaxy\snexus)/i,/\ssamsung[\s-]([\w-]+)/i,/sec-(sgh\w+)/i],[c,[f,"Samsung"],[p,w]],[/\((ip(?:hone|od)[\s\w]*);/i],[c,[f,"Apple"],[p,w]],[/\((ipad);[\w\s\),;-]+apple/i,/applecoremedia\/[\w\.]+\s\((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[c,[f,"Apple"],[p,v]],[/\b((?:agr|ags[23]|bah2?|sht?)-a?[lw]\d{2})/i],[c,[f,"Huawei"],[p,v]],[/d\/huawei([\w\s-]+)[;\)]/i,/\b(nexus\s6p|vog-[at]?l\d\d|ane-[at]?l[x\d]\d|eml-a?l\d\da?|lya-[at]?l\d[\dc]|clt-a?l\d\di?|ele-l\d\d)/i,/\b(\w{2,4}-[atu][ln][01259][019])[;\)\s]/i],[c,[f,"Huawei"],[p,w]],[/\b(poco[\s\w]+)(?:\sbuild|\))/i,/\b;\s(\w+)\sbuild\/hm\1/i,/\b(hm[\s\-_]?note?[\s_]?(?:\d\w)?)\sbuild/i,/\b(redmi[\s\-_]?(?:note|k)?[\w\s_]+)(?:\sbuild|\))/i,/\b(mi[\s\-_]?(?:a\d|one|one[\s_]plus|note lte)?[\s_]?(?:\d?\w?)[\s_]?(?:plus)?)\sbuild/i],[[c,/_/g," "],[f,"Xiaomi"],[p,w]],[/\b(mi[\s\-_]?(?:pad)(?:[\w\s_]+))(?:\sbuild|\))/i],[[c,/_/g," "],[f,"Xiaomi"],[p,v]],[/;\s(\w+)\sbuild.+\soppo/i,/\s(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007)\b/i],[c,[f,"OPPO"],[p,w]],[/\svivo\s(\w+)(?:\sbuild|\))/i,/\s(v[12]\d{3}\w?[at])(?:\sbuild|;)/i],[c,[f,"Vivo"],[p,w]],[/\s(rmx[12]\d{3})(?:\sbuild|;)/i],[c,[f,"Realme"],[p,w]],[/\s(milestone|droid(?:[2-4x]|\s(?:bionic|x2|pro|razr))?:?(\s4g)?)\b[\w\s]+build\//i,/\smot(?:orola)?[\s-](\w*)/i,/((?:moto[\s\w\(\)]+|xt\d{3,4}|nexus\s6)(?=\sbuild|\)))/i],[c,[f,"Motorola"],[p,w]],[/\s(mz60\d|xoom[\s2]{0,2})\sbuild\//i],[c,[f,"Motorola"],[p,v]],[/((?=lg)?[vl]k\-?\d{3})\sbuild|\s3\.[\s\w;-]{10}lg?-([06cv9]{3,4})/i],[c,[f,"LG"],[p,v]],[/(lm-?f100[nv]?|nexus\s[45])/i,/lg[e;\s\/-]+((?!browser|netcast)\w+)/i,/\blg(\-?[\d\w]+)\sbuild/i],[c,[f,"LG"],[p,w]],[/(ideatab[\w\-\s]+)/i,/lenovo\s?(s(?:5000|6000)(?:[\w-]+)|tab(?:[\s\w]+)|yt[\d\w-]{6}|tb[\d\w-]{6})/i],[c,[f,"Lenovo"],[p,v]],[/(?:maemo|nokia).*(n900|lumia\s\d+)/i,/nokia[\s_-]?([\w\.-]*)/i],[[c,/_/g," "],[f,"Nokia"],[p,w]],[/droid.+;\s(pixel\sc)[\s)]/i],[c,[f,"Google"],[p,v]],[/droid.+;\s(pixel[\s\daxl]{0,6})(?:\sbuild|\))/i],[c,[f,"Google"],[p,w]],[/droid.+\s([c-g]\d{4}|so[-l]\w+|xq-a\w[4-7][12])(?=\sbuild\/|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[c,[f,"Sony"],[p,w]],[/sony\stablet\s[ps]\sbuild\//i,/(?:sony)?sgp\w+(?:\sbuild\/|\))/i],[[c,"Xperia Tablet"],[f,"Sony"],[p,v]],[/\s(kb2005|in20[12]5|be20[12][59])\b/i,/\ba000(1)\sbuild/i,/\boneplus\s(a\d{4})[\s)]/i],[c,[f,"OnePlus"],[p,w]],[/(alexa)webm/i,/(kf[a-z]{2}wi)(\sbuild\/|\))/i,/(kf[a-z]+)(\sbuild\/|\)).+silk\//i],[c,[f,"Amazon"],[p,v]],[/(sd|kf)[0349hijorstuw]+(\sbuild\/|\)).+silk\//i],[[c,"Fire Phone"],[f,"Amazon"],[p,w]],[/\((playbook);[\w\s\),;-]+(rim)/i],[c,f,[p,v]],[/((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10;\s(\w+)/i],[c,[f,"BlackBerry"],[p,w]],[/(?:\b|asus_)(transfo[prime\s]{4,10}\s\w+|eeepc|slider\s\w+|nexus\s7|padfone|p00[cj])/i],[c,[f,"ASUS"],[p,v]],[/\s(z[es]6[027][01][km][ls]|zenfone\s\d\w?)\b/i],[c,[f,"ASUS"],[p,w]],[/(nexus\s9)/i],[c,[f,"HTC"],[p,v]],[/(htc)[;_\s-]{1,2}([\w\s]+(?=\)|\sbuild)|\w+)/i,/(zte)-(\w*)/i,/(alcatel|geeksphone|nexian|panasonic|(?=;\s)sony)[_\s-]?([\w-]*)/i],[f,[c,/_/g," "],[p,w]],[/droid[x\d\.\s;]+\s([ab][1-7]\-?[0178a]\d\d?)/i],[c,[f,"Acer"],[p,v]],[/droid.+;\s(m[1-5]\snote)\sbuild/i,/\bmz-([\w-]{2,})/i],[c,[f,"Meizu"],[p,w]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[\s_-]?([\w-]*)/i,/(hp)\s([\w\s]+\w)/i,/(asus)-?(\w+)/i,/(microsoft);\s(lumia[\s\w]+)/i,/(lenovo)[_\s-]?([\w-]+)/i,/linux;.+(jolla);/i,/droid.+;\s(oppo)\s?([\w\s]+)\sbuild/i],[f,c,[p,w]],[/(archos)\s(gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/\s(nook)[\w\s]+build\/(\w+)/i,/(dell)\s(strea[kpr\s\d]*[\dko])/i,/[;\/]\s?(le[\s\-]+pan)[\s\-]+(\w{1,9})\sbuild/i,/[;\/]\s?(trinity)[\-\s]*(t\d{3})\sbuild/i,/\b(gigaset)[\s\-]+(q\w{1,9})\sbuild/i,/\b(vodafone)\s([\w\s]+)(?:\)|\sbuild)/i],[f,c,[p,v]],[/\s(surface\sduo)\s/i],[c,[f,"Microsoft"],[p,v]],[/droid\s[\d\.]+;\s(fp\du?)\sbuild/i],[c,[f,"Fairphone"],[p,w]],[/\s(u304aa)\sbuild/i],[c,[f,"AT&T"],[p,w]],[/sie-(\w*)/i],[c,[f,"Siemens"],[p,w]],[/[;\/]\s?(rct\w+)\sbuild/i],[c,[f,"RCA"],[p,v]],[/[;\/\s](venue[\d\s]{2,7})\sbuild/i],[c,[f,"Dell"],[p,v]],[/[;\/]\s?(q(?:mv|ta)\w+)\sbuild/i],[c,[f,"Verizon"],[p,v]],[/[;\/]\s(?:barnes[&\s]+noble\s|bn[rt])([\w\s\+]*)\sbuild/i],[c,[f,"Barnes & Noble"],[p,v]],[/[;\/]\s(tm\d{3}\w+)\sbuild/i],[c,[f,"NuVision"],[p,v]],[/;\s(k88)\sbuild/i],[c,[f,"ZTE"],[p,v]],[/;\s(nx\d{3}j)\sbuild/i],[c,[f,"ZTE"],[p,w]],[/[;\/]\s?(gen\d{3})\sbuild.*49h/i],[c,[f,"Swiss"],[p,w]],[/[;\/]\s?(zur\d{3})\sbuild/i],[c,[f,"Swiss"],[p,v]],[/[;\/]\s?((zeki)?tb.*\b)\sbuild/i],[c,[f,"Zeki"],[p,v]],[/[;\/]\s([yr]\d{2})\sbuild/i,/[;\/]\s(dragon[\-\s]+touch\s|dt)(\w{5})\sbuild/i],[[f,"Dragon Touch"],c,[p,v]],[/[;\/]\s?(ns-?\w{0,9})\sbuild/i],[c,[f,"Insignia"],[p,v]],[/[;\/]\s?((nxa|Next)-?\w{0,9})\sbuild/i],[c,[f,"NextBook"],[p,v]],[/[;\/]\s?(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05]))\sbuild/i],[[f,"Voice"],c,[p,w]],[/[;\/]\s?(lvtel\-)?(v1[12])\sbuild/i],[[f,"LvTel"],c,[p,w]],[/;\s(ph-1)\s/i],[c,[f,"Essential"],[p,w]],[/[;\/]\s?(v(100md|700na|7011|917g).*\b)\sbuild/i],[c,[f,"Envizen"],[p,v]],[/[;\/]\s?(trio[\s\w\-\.]+)\sbuild/i],[c,[f,"MachSpeed"],[p,v]],[/[;\/]\s?tu_(1491)\sbuild/i],[c,[f,"Rotor"],[p,v]],[/(shield[\w\s]+)\sbuild/i],[c,[f,"Nvidia"],[p,v]],[/(sprint)\s(\w+)/i],[f,c,[p,w]],[/(kin\.[onetw]{3})/i],[[c,/\./g," "],[f,"Microsoft"],[p,w]],[/droid\s[\d\.]+;\s(cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[c,[f,"Zebra"],[p,v]],[/droid\s[\d\.]+;\s(ec30|ps20|tc[2-8]\d[kx])\)/i],[c,[f,"Zebra"],[p,w]],[/\s(ouya)\s/i,/(nintendo)\s([wids3utch]+)/i],[f,c,[p,g]],[/droid.+;\s(shield)\sbuild/i],[c,[f,"Nvidia"],[p,g]],[/(playstation\s[345portablevi]+)/i],[c,[f,"Sony"],[p,g]],[/[\s\(;](xbox(?:\sone)?(?!;\sxbox))[\s\);]/i],[c,[f,"Microsoft"],[p,g]],[/smart-tv.+(samsung)/i],[f,[p,b]],[/hbbtv.+maple;(\d+)/i],[[c,/^/,"SmartTV"],[f,"Samsung"],[p,b]],[/(?:linux;\snetcast.+smarttv|lg\snetcast\.tv-201\d)/i],[[f,"LG"],[p,b]],[/(apple)\s?tv/i],[f,[c,"Apple TV"],[p,b]],[/crkey/i],[[c,"Chromecast"],[f,"Google"],[p,b]],[/droid.+aft([\w])(\sbuild\/|\))/i],[c,[f,"Amazon"],[p,b]],[/\(dtv[\);].+(aquos)/i],[c,[f,"Sharp"],[p,b]],[/hbbtv\/\d+\.\d+\.\d+\s+\([\w\s]*;\s*(\w[^;]*);([^;]*)/i],[[f,S.trim],[c,S.trim],[p,b]],[/[\s\/\(](android\s|smart[-\s]?|opera\s)tv[;\)\s]/i],[[p,b]],[/((pebble))app\/[\d\.]+\s/i],[f,c,[p,y]],[/droid.+;\s(glass)\s\d/i],[c,[f,"Google"],[p,y]],[/droid\s[\d\.]+;\s(wt63?0{2,3})\)/i],[c,[f,"Zebra"],[p,y]],[/(tesla)(?:\sqtcarbrowser|\/20[12]\d\.[\w\.-]+)/i],[f,[p,x]],[/droid .+?; ([^;]+?)(?: build|\) applewebkit).+? mobile safari/i],[c,[p,w]],[/droid .+?;\s([^;]+?)(?: build|\) applewebkit).+?(?! mobile) safari/i],[c,[p,v]],[/\s(tablet|tab)[;\/]/i,/\s(mobile)(?:[;\/]|\ssafari)/i],[[p,S.lowerize]],[/(android[\w\.\s\-]{0,9});.+build/i],[c,[f,"Generic"]],[/(phone)/i],[[p,w]]],engine:[[/windows.+\sedge\/([\w\.]+)/i],[h,[d,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[h,[d,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/\s]\(?([\w\.]+)/i,/(icab)[\/\s]([23]\.[\d\.]+)/i],[d,h],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[h,d]],os:[[/microsoft\s(windows)\s(vista|xp)/i],[d,h],[/(windows)\snt\s6\.2;\s(arm)/i,/(windows\sphone(?:\sos)*)[\s\/]?([\d\.\s\w]*)/i,/(windows\smobile|windows)[\s\/]?([ntce\d\.\s]+\w)(?!.+xbox)/i],[d,[h,C.str,T.os.windows.version]],[/(win(?=3|9|n)|win\s9x\s)([nt\d\.]+)/i],[[d,"Windows"],[h,C.str,T.os.windows.version]],[/ip[honead]{2,4}\b(?:.*os\s([\w]+)\slike\smac|;\sopera)/i,/cfnetwork\/.+darwin/i],[[h,/_/g,"."],[d,"iOS"]],[/(mac\sos\sx)\s?([\w\s\.]*)/i,/(macintosh|mac(?=_powerpc)\s)(?!.+haiku)/i],[[d,"Mac OS"],[h,/_/g,"."]],[/(android|webos|palm\sos|qnx|bada|rim\stablet\sos|meego|sailfish|contiki)[\/\s-]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/\s]([\w\.]+)/i,/\((series40);/i],[d,h],[/\(bb(10);/i],[h,[d,"BlackBerry"]],[/(?:symbian\s?os|symbos|s60(?=;)|series60)[\/\s-]?([\w\.]*)/i],[h,[d,"Symbian"]],[/mozilla.+\(mobile;.+gecko.+firefox/i],[[d,"Firefox OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[h,[d,"webOS"]],[/crkey\/([\d\.]+)/i],[h,[d,"Chromecast"]],[/(cros)\s[\w]+\s([\w\.]+\w)/i],[[d,"Chromium OS"],h],[/(nintendo|playstation)\s([wids345portablevuch]+)/i,/(xbox);\s+xbox\s([^\);]+)/i,/(mint)[\/\s\(\)]?(\w*)/i,/(mageia|vectorlinux)[;\s]/i,/(joli|[kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?=\slinux)|slackware|fedora|mandriva|centos|pclinuxos|redhat|zenwalk|linpus|raspbian)(?:\sgnu\/linux)?(?:\slinux)?[\/\s-]?(?!chrom|package)([\w\.-]*)/i,/(hurd|linux)\s?([\w\.]*)/i,/(gnu)\s?([\w\.]*)/i,/\s([frentopc-]{0,4}bsd|dragonfly)\s?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku)\s(\w+)/i],[d,h],[/(sunos)\s?([\w\.\d]*)/i],[[d,"Solaris"],h],[/((?:open)?solaris)[\/\s-]?([\w\.]*)/i,/(aix)\s((\d)(?=\.|\)|\s)[\w\.])*/i,/(plan\s9|minix|beos|os\/2|amigaos|morphos|risc\sos|openvms|fuchsia)/i,/(unix)\s?([\w\.]*)/i],[d,h]]},E=function(e,i){if("object"==typeof e&&(i=e,e=o),!(this instanceof E))return new E(e,i).getResult();var t=e||(void 0!==r&&r.navigator&&r.navigator.userAgent?r.navigator.userAgent:""),n=i?S.extend(k,i):k;return this.getBrowser=function(){var e={name:o,version:o};return C.rgx.call(e,t,n.browser),e.major=S.major(e.version),e},this.getCPU=function(){var e={architecture:o};return C.rgx.call(e,t,n.cpu),e},this.getDevice=function(){var e={vendor:o,model:o,type:o};return C.rgx.call(e,t,n.device),e},this.getEngine=function(){var e={name:o,version:o};return C.rgx.call(e,t,n.engine),e},this.getOS=function(){var e={name:o,version:o};return C.rgx.call(e,t,n.os),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return t},this.setUA=function(e){return t=typeof e===u&&e.length>255?S.trim(e,255):e,this},this.setUA(t),this};E.VERSION="0.7.28",E.BROWSER={NAME:d,MAJOR:"major",VERSION:h},E.CPU={ARCHITECTURE:m},E.DEVICE={MODEL:c,VENDOR:f,TYPE:p,CONSOLE:g,MOBILE:w,SMARTTV:b,TABLET:v,WEARABLE:y,EMBEDDED:x},E.ENGINE={NAME:d,VERSION:h},E.OS={NAME:d,VERSION:h},typeof i!==s?(typeof e!==s&&e.exports&&(i=e.exports=E),i.UAParser=E):(n=function(){return E}.call(i,t,i,e))===o||(e.exports=n);var A=void 0!==r&&(r.jQuery||r.Zepto);if(A&&!A.ua){var M=new E;A.ua=M.getResult(),A.ua.get=function(){return M.getUA()},A.ua.set=function(e){M.setUA(e);var i=M.getResult();for(var t in i)A.ua[t]=i[t]}}}("object"==typeof window?window:this)},function(e,i){e.exports=function(){var e=["monospace","sans-serif","serif"],i=document.getElementsByTagName("body")[0],t=document.createElement("span");t.style.fontSize="72px",t.innerHTML="mmmmmmmmmmlli";var n={},r={};for(var o in e)t.style.fontFamily=e[o],i.appendChild(t),n[e[o]]=t.offsetWidth,r[e[o]]=t.offsetHeight,i.removeChild(t);this.detect=function(o){var a=!1;for(var s in e){t.style.fontFamily=o+","+e[s],i.appendChild(t);var l=t.offsetWidth!=n[e[s]]||t.offsetHeight!=r[e[s]];i.removeChild(t),a=a||l}return a}}},function(e,i){"function"==typeof Object.create?e.exports=function(e,i){i&&(e.super_=i,e.prototype=Object.create(i.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:e.exports=function(e,i){if(i){e.super_=i;var t=function(){};t.prototype=i.prototype,e.prototype=new t,e.prototype.constructor=e}}},function(e,i,t){"use strict";var n=t(8);e.exports=function(){return n.getJREs().toString()}},function(e,i,t){"use strict";var n="^(\\d+)(?:\\.(\\d+)(?:\\.(\\d+)(?:[_\\.](\\d+))?)?)?",r=n+"$",o=n+"(\\*|\\+)?$",a={core:["id","class","title","style"],applet:["codebase","code","name","archive","object","width","height","alt","align","hspace","vspace"]},s=a.applet.concat(a.core);function l(e){f.debug&&(console.log?console.log(e):alert(e))}function u(e,i){var t=0,n=e.match(o);if(null!=n){if(i)return!0;for(var r=!1,a=!1,s=new Array,u=1;u<n.length;++u)"string"==typeof n[u]&&""!=n[u]&&(s[t]=n[u],t++);"+"==s[s.length-1]?(a=!0,r=!1,s.length--):"*"==s[s.length-1]?(a=!1,r=!0,s.length--):s.length<4&&(a=!1,r=!0);for(var c=f.getJREs(),d=0;d<c.length;++d)if(f.compareVersionToPattern(c[d],s,r,a))return!0;return!1}var p="Invalid versionPattern passed to versionCheck: "+e;return l("[versionCheck()] "+p),alert(p),!1}function c(e){document.location="jnlp:"+function(e){var i;if(function(e){for(var i=["http://","https://","file://"],t=0;t<i.length;t++)if(e.toLowerCase().startsWith(i[t]))return!0;return!1}(e))i=e;else{var t=window.location.href,n=t.lastIndexOf("/");i=(n>-1?t.substring(0,n+1):t+"/")+e}return i}(e)}function d(){return"Edge"==f.getBrowser()||"Chrome"==f.browserName2||"FirefoxNoPlugin"==f.browserName2&&!u("1.8*",!1)||"NoActiveX"==f.browserName2}function p(e){var i="https://java.com/dt-redirect";return null==e||0==e.length?i:("&"==e.charAt(0)&&(e=e.substring(1,e.length)),i+"?"+e)}"function"!=typeof String.prototype.startsWith&&(String.prototype.startsWith=function(e,i){return i=i||0,this.indexOf(e,i)===i});var f={debug:null,version:"20120801",firefoxJavaVersion:null,useStaticMimeType:!1,myInterval:null,preInstallJREList:null,brand:null,locale:null,installType:null,EAInstallEnabled:!1,EarlyAccessURL:null,oldMimeType:"application/npruntime-scriptable-plugin;DeploymentToolkit",mimeType:"application/java-deployment-toolkit",launchButtonPNG:function(){var e="//java.com/js/webstart.png";try{return-1!=document.location.protocol.indexOf("http")?e:"https:"+e}catch(i){return"https:"+e}}(),browserName:null,browserName2:null,getJREs:function(){var e=new Array;if(this.isPluginInstalled())for(var i=this.getPlugin().jvms,t=0;t<i.getLength();t++)e[t]=i.get(t).version;else{var n=this.getBrowser();"MSIE"==n?this.testUsingActiveX("9")?e[0]="9":this.testUsingActiveX("1.8.0")?e[0]="1.8.0":this.testUsingActiveX("1.7.0")?e[0]="1.7.0":this.testUsingActiveX("1.6.0")?e[0]="1.6.0":this.testUsingActiveX("1.5.0")?e[0]="1.5.0":this.testUsingActiveX("1.4.2")?e[0]="1.4.2":this.testForMSVM()&&(e[0]="1.1"):"Netscape Family"==n&&(this.getJPIVersionUsingMimeType(),null!=this.firefoxJavaVersion?e[0]=this.firefoxJavaVersion:this.testUsingMimeTypes("9")?e[0]="9":this.testUsingMimeTypes("1.8")?e[0]="1.8.0":this.testUsingMimeTypes("1.7")?e[0]="1.7.0":this.testUsingMimeTypes("1.6")?e[0]="1.6.0":this.testUsingMimeTypes("1.5")?e[0]="1.5.0":this.testUsingMimeTypes("1.4.2")?e[0]="1.4.2":"Safari"==this.browserName2&&(this.testUsingPluginsArray("9")?e[0]="9":this.testUsingPluginsArray("1.8")?e[0]="1.8.0":this.testUsingPluginsArray("1.7")?e[0]="1.7.0":this.testUsingPluginsArray("1.6")?e[0]="1.6.0":this.testUsingPluginsArray("1.5")?e[0]="1.5.0":this.testUsingPluginsArray("1.4.2")&&(e[0]="1.4.2")))}if(this.debug)for(var r=0;r<e.length;++r)l("[getJREs()] We claim to have detected Java SE "+e[r]);return e},installJRE:function(e){return l("The Deployment Toolkit installJRE()  method no longer installs JRE. It just checks if the requested version of JRE is installed and calls installLatestJRE() otherwise. More Information on usage of the Deployment Toolkit can be found in the Deployment Guide at https://docs.oracle.com/javase/8/docs/technotes/guides/deploy/"),"undefined"!=e&&null!=e||(e="1.1"),null==e.match(o)&&(l("Invalid requestVersion argument to installJRE(): "+e),e="1.6"),!!this.versionCheck(e)||this.installLatestJRE()},isAutoInstallEnabled:function(e){return!!this.isPluginInstalled()&&(void 0===e&&(e=null),function(e){return"MSIE"!=f.browserName||!!f.compareVersionToPattern(f.getPlugin().version,["10","0","0"],!1,!0)||null!=e&&!function(e,i){if(null==e||0==e.length)return!0;var t=e.charAt(e.length-1);if("+"!=t&&"*"!=t&&-1!=e.indexOf("_")&&"_"!=t&&(e+="*",t="*"),(e=e.substring(0,e.length-1)).length>0){var n=e.charAt(e.length-1);"."!=n&&"_"!=n||(e=e.substring(0,e.length-1))}return"*"==t?0==i.indexOf(e):"+"==t&&e<=i}("1.6.0_33+",e)}(e))},isCallbackSupported:function(){return this.isPluginInstalled()&&this.compareVersionToPattern(this.getPlugin().version,["10","2","0"],!1,!0)},installLatestJRE:function(){if(l("The Deployment Toolkit installLatestJRE() method no longer installs JRE. If user's version of Java is below the security baseline it redirects user to java.com to get an updated JRE. More Information on usage of the Deployment Toolkit can be found in the Deployment Guide at ://docs.oracle.com/javase/8/docs/technotes/guides/deploy/"),!this.isPluginInstalled()||!this.getPlugin().installLatestJRE()){var e=this.getBrowser(),i=navigator.platform.toLowerCase();return"MSIE"==e?this.IEInstall():"Netscape Family"==e&&-1!=i.indexOf("win32")?this.FFInstall():(location.href=p((null!=this.locale?"&locale="+this.locale:"")+(null!=this.brand?"&brand="+this.brand:"")),!1)}return!0},runApplet:function(e,i,t){if("undefined"!=t&&null!=t||(t="1.1"),null!=t.match(r))if("?"!=this.getBrowser()){if(d()){var n=setInterval((function(){var e;"complete"==document.readyState&&(clearInterval(n),(e=document.createElement("div")).id="messagebox",e.setAttribute("style","background-color: #ffffce;text-align: left;border: solid 1px #f0c000; padding: 1.65em 1.65em .75em 0.5em; font-family: Helvetica, Arial, sans-serif; font-size: 75%; bottom:0; left:0; right:0; position:fixed; margin:auto; opacity:0.9; width:400px;"),e.innerHTML='<span style="margin-left:10px;font-weight:bold;float:right;font-size:22px;line-height:20px;cursor:pointer;color:red;" onclick="this.parentElement.style.display=\'none\';">&times;</span><img src="https://java.com/js/alert_16.png"><div style="border: .85px; margin:-2.2em 0 0.55em 2.5em;"><p>Java Plug-in is not supported by this browser. <a href="https://java.com/dt-redirect">More info</a></p>',document.body.appendChild(e))}),15);return void l("[runApplet()] Java Plug-in is not supported by this browser")}(this.versionCheck(t+"+")||this.installJRE(t+"+"))&&this.writeAppletTag(e,i)}else this.writeAppletTag(e,i);else l("[runApplet()] Invalid minimumVersion argument to runApplet():"+t)},writeAppletTag:function(e,i){var t="<applet ",n="",r=!0;for(var o in null!=i&&"object"==typeof i||(i=new Object),e)(function(e,i){for(var t=e.length,n=0;n<t;n++)if(e[n]===i)return!0;return!1})(s,o.toLowerCase())?(t+=" "+o+'="'+e[o]+'"',"code"==o&&(r=!1)):i[o]=e[o];var a=!1;for(var l in i)"codebase_lookup"==l&&(a=!0),"object"!=l&&"java_object"!=l&&"java_code"!=l||(r=!1),n+='<param name="'+l+'" value="'+i[l]+'"/>';a||(n+='<param name="codebase_lookup" value="false"/>'),r&&(t+=' code="dummy"'),t+=">",document.write(t+"\n"+n+"\n</applet>")},versionCheck:function(e){return u(e,d())},isWebStartInstalled:function(e){if(d())return!0;if("?"==this.getBrowser())return!0;"undefined"!=e&&null!=e||(e="1.4.2");var i=!1;return null!=e.match(r)?i=this.versionCheck(e+"+"):(l("[isWebStartInstaller()] Invalid minimumVersion argument to isWebStartInstalled(): "+e),i=this.versionCheck("1.4.2+")),i},getJPIVersionUsingMimeType:function(){var e,i;for(e=0;e<navigator.mimeTypes.length;++e)if(null!=(i=navigator.mimeTypes[e].type.match(/^application\/x-java-applet;jpi-version=(.*)$/)))return this.firefoxJavaVersion=i[1],void(this.useStaticMimeType=!0);for(e=0;e<navigator.mimeTypes.length;++e)null!=(i=navigator.mimeTypes[e].type.match(/^application\/x-java-applet;version=(.*)$/))&&(null==this.firefoxJavaVersion||this.compareVersions(i[1],this.firefoxJavaVersion))&&(this.firefoxJavaVersion=i[1])},launchWebStartApplication:function(e){if(this.getJPIVersionUsingMimeType(),0==u("1.7.0+",!1))if(d())c(e);else if(0==this.installJRE("1.7.0+")||0==this.isWebStartInstalled("1.7.0"))return!1;var i=null;document.documentURI&&(i=document.documentURI),null==i&&(i=document.URL);var t,n=this.getBrowser();if("MSIE"==n?t='<object classid="clsid:8AD9C840-044E-11D1-B3E9-00805F499D93" width="0" height="0"><PARAM name="launchjnlp" value="'+e+'"><PARAM name="docbase" value="'+encodeURIComponent(i)+'"></object>':"Netscape Family"==n&&(t='<embed type="'+(this.useStaticMimeType?"application/x-java-applet;jpi-version=":"application/x-java-applet;version=")+this.firefoxJavaVersion+'" width="0" height="0" launchjnlp="'+e+'"docbase="'+encodeURIComponent(i)+'" />'),"undefined"==document.body||null==document.body)document.write(t),document.location=i;else{var r=document.createElement("div");r.id="div1",r.style.position="relative",r.style.left="-10000px",r.style.margin="0px auto",r.className="dynamicDiv",r.innerHTML=t,document.body.appendChild(r)}},createWebStartLaunchButtonEx:function(e){var i="javascript:deployJava.launchWebStartApplication('"+e+"');";document.write('<a href="'+i+'" onMouseOver="window.status=\'\'; return true;"><img src="'+this.launchButtonPNG+'" border="0" /></a>')},createWebStartLaunchButton:function(e,i){var t="javascript:if (!deployJava.isWebStartInstalled(&quot;"+i+"&quot;)) {if (deployJava.installLatestJRE()) {if (deployJava.launch(&quot;"+e+"&quot;)) {}}} else {if (deployJava.launch(&quot;"+e+"&quot;)) {}}";document.write('<a href="'+t+'" onMouseOver="window.status=\'\'; return true;"><img src="'+this.launchButtonPNG+'" border="0" /></a>')},launch:function(e){return document.location=e,!0},launchEx:function(e){return c(e),!0},isPluginInstalled:function(){var e=this.getPlugin();return!(!e||!e.jvms)},isAutoUpdateEnabled:function(){return!!this.isPluginInstalled()&&this.getPlugin().isAutoUpdateEnabled()},setAutoUpdateEnabled:function(){return!!this.isPluginInstalled()&&this.getPlugin().setAutoUpdateEnabled()},setInstallerType:function(e){return l("The Deployment Toolkit no longer installs JRE. Method setInstallerType() is no-op. More Information on usage of the Deployment Toolkit can be found in the Deployment Guide at ://docs.oracle.com/javase/8/docs/technotes/guides/deploy/"),!1},setAdditionalPackages:function(e){return l("The Deployment Toolkit no longer installs JRE. Method setAdditionalPackages() is no-op. More Information on usage of the Deployment Toolkit can be found in the Deployment Guide at ://docs.oracle.com/javase/8/docs/technotes/guides/deploy/"),!1},setEarlyAccess:function(e){this.EAInstallEnabled=e},isPlugin2:function(){if(this.isPluginInstalled()&&this.versionCheck("1.6.0_10+"))try{return this.getPlugin().isPlugin2()}catch(e){}return!1},allowPlugin:function(){return this.getBrowser(),"Safari"!=this.browserName2&&"Opera"!=this.browserName2},getPlugin:function(){this.refresh();var e=null;return this.allowPlugin()&&(e=document.getElementById("deployJavaPlugin")),e},compareVersionToPattern:function(e,i,t,n){if(e==undefined||i==undefined)return!1;var o=e.match(r);if(null!=o){for(var a=0,s=new Array,l=1;l<o.length;++l)"string"==typeof o[l]&&""!=o[l]&&(s[a]=o[l],a++);var u=Math.min(s.length,i.length);if(n){for(var c=0;c<u;++c){var d=parseInt(s[c]),p=parseInt(i[c]);if(d<p)return!1;if(d>p)return!0}return!0}for(var f=0;f<u;++f)if(s[f]!=i[f])return!1;return!!t||s.length==i.length}return!1},getBrowser:function(){if(null==this.browserName){var e=navigator.userAgent.toLowerCase();if(l("[getBrowser()] navigator.userAgent.toLowerCase() -> "+e),-1!=e.indexOf("edge"))this.browserName="Edge",this.browserName2="Edge";else if(-1!=e.indexOf("msie")&&-1==e.indexOf("opera"))this.browserName="MSIE",this.browserName2="MSIE";else if(-1!=e.indexOf("trident")||-1!=e.indexOf("Trident")){if(this.browserName="MSIE",this.browserName2="MSIE",-1!=e.indexOf("windows nt 6.3")||-1!=e.indexOf("windows nt 6.2"))try{new ActiveXObject("htmlfile")}catch(i){this.browserName2="NoActiveX"}}else-1!=e.indexOf("iphone")?(this.browserName="Netscape Family",this.browserName2="iPhone"):-1!=e.indexOf("firefox")&&-1==e.indexOf("opera")?(this.browserName="Netscape Family",this.isPluginInstalled()?this.browserName2="Firefox":this.browserName2="FirefoxNoPlugin"):-1!=e.indexOf("chrome")?(this.browserName="Netscape Family",this.browserName2="Chrome"):-1!=e.indexOf("safari")?(this.browserName="Netscape Family",this.browserName2="Safari"):-1!=e.indexOf("mozilla")&&-1==e.indexOf("opera")?(this.browserName="Netscape Family",this.browserName2="Other"):-1!=e.indexOf("opera")?(this.browserName="Netscape Family",this.browserName2="Opera"):(this.browserName="?",this.browserName2="unknown");l("[getBrowser()] Detected browser name:"+this.browserName+", "+this.browserName2)}return this.browserName},testUsingActiveX:function(e){var i="JavaWebStart.isInstalled."+e+".0";if("undefined"==typeof ActiveXObject||!ActiveXObject)return l("[testUsingActiveX()] Browser claims to be IE, but no ActiveXObject object?"),!1;try{return null!=new ActiveXObject(i)}catch(t){return!1}},testForMSVM:function(){if("undefined"!=typeof oClientCaps){var e=oClientCaps.getComponentVersion("{08B0E5C0-4FCB-11CF-AAA5-00401C608500}","ComponentID");return""!=e&&"5,0,5000,0"!=e}return!1},testUsingMimeTypes:function(e){if(!navigator.mimeTypes)return l("[testUsingMimeTypes()] Browser claims to be Netscape family, but no mimeTypes[] array?"),!1;for(var i=0;i<navigator.mimeTypes.length;++i){var t=navigator.mimeTypes[i].type.match(/^application\/x-java-applet\x3Bversion=(1\.8|1\.7|1\.6|1\.5|1\.4\.2)$/);if(null!=t&&this.compareVersions(t[1],e))return!0}return!1},testUsingPluginsArray:function(e){if(!navigator.plugins||!navigator.plugins.length)return!1;for(var i=navigator.platform.toLowerCase(),t=0;t<navigator.plugins.length;++t){var n=navigator.plugins[t].description;if(-1!=n.search(/^Java Switchable Plug-in (Cocoa)/)){if(this.compareVersions("1.5.0",e))return!0}else if(-1!=n.search(/^Java/)&&-1!=i.indexOf("win")&&(this.compareVersions("1.5.0",e)||this.compareVersions("1.6.0",e)))return!0}return!!this.compareVersions("1.5.0",e)},IEInstall:function(){return location.href=p((null!=this.locale?"&locale="+this.locale:"")+(null!=this.brand?"&brand="+this.brand:"")),!1},done:function(e,i){},FFInstall:function(){return location.href=p((null!=this.locale?"&locale="+this.locale:"")+(null!=this.brand?"&brand="+this.brand:"")+(null!=this.installType?"&type="+this.installType:"")),!1},compareVersions:function(e,i){for(var t=e.split("."),n=i.split("."),r=0;r<t.length;++r)t[r]=Number(t[r]);for(var o=0;o<n.length;++o)n[o]=Number(n[o]);return 2==t.length&&(t[2]=0),t[0]>n[0]||!(t[0]<n[0])&&(t[1]>n[1]||!(t[1]<n[1])&&(t[2]>n[2]||!(t[2]<n[2])))},enableAlerts:function(){this.browserName=null,this.debug=!0},poll:function(){this.refresh();var e=this.getJREs();0==this.preInstallJREList.length&&0!=e.length&&clearInterval(this.myInterval),0!=this.preInstallJREList.length&&0!=e.length&&this.preInstallJREList[0]!=e[0]&&clearInterval(this.myInterval)},writePluginTag:function(){var e=this.getBrowser();"MSIE"==e?document.write('<object classid="clsid:CAFEEFAC-DEC7-0000-0001-ABCDEFFEDCBA" id="deployJavaPlugin" width="0" height="0"></object>'):"Netscape Family"==e&&this.allowPlugin()&&this.writeEmbedTag()},refresh:function(){(navigator.plugins.refresh(!1),"Netscape Family"==this.getBrowser()&&this.allowPlugin())&&(null==document.getElementById("deployJavaPlugin")&&this.writeEmbedTag())},writeEmbedTag:function(){var e=!1;if(null!=navigator.mimeTypes){for(var i=0;i<navigator.mimeTypes.length;i++)navigator.mimeTypes[i].type==this.mimeType&&navigator.mimeTypes[i].enabledPlugin&&(document.write('<embed id="deployJavaPlugin" type="'+this.mimeType+'" hidden="true" />'),e=!0);if(!e)for(var t=0;t<navigator.mimeTypes.length;t++)navigator.mimeTypes[t].type==this.oldMimeType&&navigator.mimeTypes[t].enabledPlugin&&document.write('<embed id="deployJavaPlugin" type="'+this.oldMimeType+'" hidden="true" />')}}};if(f.writePluginTag(),null==f.locale){var h=null;if(null==h)try{h=navigator.userLanguage}catch(m){}if(null==h)try{h=navigator.systemLanguage}catch(m){}if(null==h)try{h=navigator.language}catch(m){}null!=h&&(h.replace("-","_"),f.locale=h)}e.exports=f},function(e,i,t){"use strict";var n=t(10);e.exports=function(){if(this.isFlash()){var e=n.getFlashPlayerVersion();return e.major+"."+e.minor+"."+e.release}return""}},function(e,i){
/*!    SWFObject v2.3.20130521 <http://github.com/swfobject/swfobject>
    is released under the MIT License <http://www.opensource.org/licenses/mit-license.php>
*/
var t,n,r,o,a,s,l="undefined",u="object",c="Shockwave Flash",d="application/x-shockwave-flash",p="SWFObjectExprInst",f="onreadystatechange",h=window,m=document,g=navigator,w=!1,v=[],b=[],y=[],x=[],S=!1,C=!1,T=!0,k=!1,E=function(){var e=typeof m.getElementById!==l&&typeof m.getElementsByTagName!==l&&typeof m.createElement!==l,i=g.userAgent.toLowerCase(),t=g.platform.toLowerCase(),n=/win/.test(t||i),r=/mac/.test(t||i),o=!!/webkit/.test(i)&&parseFloat(i.replace(/^.*webkit\/(\d+(\.\d+)?).*$/,"$1")),a="Microsoft Internet Explorer"===g.appName,s=[0,0,0],p=null;if(typeof g.plugins!==l&&typeof g.plugins[c]===u)(p=g.plugins[c].description)&&typeof g.mimeTypes!==l&&g.mimeTypes[d]&&g.mimeTypes[d].enabledPlugin&&(w=!0,a=!1,p=p.replace(/^.*\s+(\S+\s+\S+$)/,"$1"),s[0]=G(p.replace(/^(.*)\..*$/,"$1")),s[1]=G(p.replace(/^.*\.(.*)\s.*$/,"$1")),s[2]=/[a-zA-Z]/.test(p)?G(p.replace(/^.*[a-zA-Z]+(.*)$/,"$1")):0);else if(typeof h.ActiveXObject!==l)try{var f=new ActiveXObject("ShockwaveFlash.ShockwaveFlash");f&&(p=f.GetVariable("$version"))&&(a=!0,s=[G((p=p.split(" ")[1].split(","))[0]),G(p[1]),G(p[2])])}catch(v){}return{w3:e,pv:s,wk:o,ie:a,win:n,mac:r}}();function A(){if(!S&&document.getElementsByTagName("body")[0]){try{var e,i=J("span");i.style.display="none",(e=m.getElementsByTagName("body")[0].appendChild(i)).parentNode.removeChild(e),e=null,i=null}catch(r){return}S=!0;for(var t=v.length,n=0;n<t;n++)v[n]()}}function M(e){S?e():v[v.length]=e}function P(){var e=b.length;if(e>0)for(var i=0;i<e;i++){var t=b[i].id,n=b[i].callbackFn,r={success:!1,id:t};if(E.pv[0]>0){var o=D(t);if(o)if(!_(b[i].swfVersion)||E.wk&&E.wk<312)if(b[i].expressInstall&&I()){var a={};a.data=b[i].expressInstall,a.width=o.getAttribute("width")||"0",a.height=o.getAttribute("height")||"0",o.getAttribute("class")&&(a.styleclass=o.getAttribute("class")),o.getAttribute("align")&&(a.align=o.getAttribute("align"));for(var s={},u=o.getElementsByTagName("param"),c=u.length,d=0;d<c;d++)"movie"!==u[d].getAttribute("name").toLowerCase()&&(s[u[d].getAttribute("name")]=u[d].getAttribute("value"));B(a,s,t,n)}else L(o),n&&n(r);else z(t,!0),n&&(r.success=!0,r.ref=N(t),r.id=t,n(r))}else if(z(t,!0),n){var p=N(t);p&&typeof p.SetVariable!==l&&(r.success=!0,r.ref=p,r.id=p.id),n(r)}}}function N(e){var i=null,t=D(e);return t&&"OBJECT"===t.nodeName.toUpperCase()&&(i=typeof t.SetVariable!==l?t:t.getElementsByTagName(u)[0]||t),i}function I(){return!C&&_("6.0.65")&&(E.win||E.mac)&&!(E.wk&&E.wk<312)}function B(e,i,a,s){var u=D(a);if(a=V(a),C=!0,r=s||null,o={success:!1,id:a},u){"OBJECT"===u.nodeName.toUpperCase()?(t=j(u),n=null):(t=u,n=a),e.id=p,(typeof e.width===l||!/%$/.test(e.width)&&G(e.width)<310)&&(e.width="310"),(typeof e.height===l||!/%$/.test(e.height)&&G(e.height)<137)&&(e.height="137");var c=E.ie?"ActiveX":"PlugIn",d="MMredirectURL="+encodeURIComponent(h.location.toString().replace(/&/g,"%26"))+"&MMplayerType="+c+"&MMdoctitle="+encodeURIComponent(m.title.slice(0,47)+" - Flash Player Installation");if(typeof i.flashvars!==l?i.flashvars+="&"+d:i.flashvars=d,E.ie&&4!=u.readyState){var f=J("div");a+="SWFObjectNew",f.setAttribute("id",a),u.parentNode.insertBefore(f,u),u.style.display="none",R(u)}O(e,i,a)}}function L(e){if(E.ie&&4!=e.readyState){e.style.display="none";var i=J("div");e.parentNode.insertBefore(i,e),i.parentNode.replaceChild(j(e),i),R(e)}else e.parentNode.replaceChild(j(e),e)}function j(e){var i=J("div");if(E.win&&E.ie)i.innerHTML=e.innerHTML;else{var t=e.getElementsByTagName(u)[0];if(t){var n=t.childNodes;if(n)for(var r=n.length,o=0;o<r;o++)1==n[o].nodeType&&"PARAM"===n[o].nodeName||8==n[o].nodeType||i.appendChild(n[o].cloneNode(!0))}}return i}function O(e,i,t){var n,r,o,a,s=D(t);if(t=V(t),E.wk&&E.wk<312)return n;if(s){var c,p,f,h=E.ie?J("div"):J(u);for(f in typeof e.id===l&&(e.id=t),i)Object.prototype.hasOwnProperty.call(i,f)&&"movie"!==f.toLowerCase()&&U(h,f,i[f]);for(c in E.ie&&(r=e.data,o=h.innerHTML,(a=J("div")).innerHTML="<object classid='clsid:D27CDB6E-AE6D-11cf-96B8-444553540000'><param name='movie' value='"+r+"'>"+o+"</object>",h=a.firstChild),e)Object.prototype.hasOwnProperty.call(e,c)&&("styleclass"===(p=c.toLowerCase())?h.setAttribute("class",e[c]):"classid"!==p&&"data"!==p&&h.setAttribute(c,e[c]));E.ie?y[y.length]=e.id:(h.setAttribute("type",d),h.setAttribute("data",e.data)),s.parentNode.replaceChild(h,s),n=h}return n}function U(e,i,t){var n=J("param");n.setAttribute("name",i),n.setAttribute("value",t),e.appendChild(n)}function R(e){var i=D(e);i&&"OBJECT"===i.nodeName.toUpperCase()&&(E.ie?(i.style.display="none",function t(){if(4==i.readyState){for(var e in i)"function"==typeof i[e]&&(i[e]=null);i.parentNode.removeChild(i)}else setTimeout(t,10)}()):i.parentNode.removeChild(i))}function F(e){return e&&e.nodeType&&1===e.nodeType}function V(e){return F(e)?e.id:e}function D(e){if(F(e))return e;var i=null;try{i=m.getElementById(e)}catch(t){}return i}function J(e){return m.createElement(e)}function G(e){return parseInt(e,10)}function _(e){e+="";var i=E.pv,t=e.split(".");return t[0]=G(t[0]),t[1]=G(t[1])||0,t[2]=G(t[2])||0,i[0]>t[0]||i[0]==t[0]&&i[1]>t[1]||i[0]==t[0]&&i[1]==t[1]&&i[2]>=t[2]}function W(e,i,t,n){var r=m.getElementsByTagName("head")[0];if(r){var o="string"==typeof t?t:"screen";if(n&&(a=null,s=null),!a||s!=o){var u=J("style");u.setAttribute("type","text/css"),u.setAttribute("media",o),a=r.appendChild(u),E.ie&&typeof m.styleSheets!==l&&m.styleSheets.length>0&&(a=m.styleSheets[m.styleSheets.length-1]),s=o}a&&(typeof a.addRule!==l?a.addRule(e,i):typeof m.createTextNode!==l&&a.appendChild(m.createTextNode(e+" {"+i+"}")))}}function z(e,i){if(T){var t=i?"visible":"hidden",n=D(e);S&&n?n.style.visibility=t:"string"==typeof e&&W("#"+e,"visibility:"+t)}}function q(e){return null!==/[\\"<>.;]/.exec(e)&&typeof encodeURIComponent!==l?encodeURIComponent(e):e}E.w3&&((typeof m.readyState!==l&&("complete"===m.readyState||"interactive"===m.readyState)||typeof m.readyState===l&&(m.getElementsByTagName("body")[0]||m.body))&&A(),S||(typeof m.addEventListener!==l&&m.addEventListener("DOMContentLoaded",A,!1),E.ie&&(m.attachEvent(f,(function X(){"complete"===m.readyState&&(m.detachEvent(f,X),A())})),h==top&&function $(){if(!S){try{m.documentElement.doScroll("left")}catch(e){return void setTimeout($,0)}A()}}()),E.wk&&function K(){S||(/loaded|complete/.test(m.readyState)?A():setTimeout(K,0))}())),v[0]=function(){w?function(){var e=m.getElementsByTagName("body")[0],i=J(u);i.setAttribute("style","visibility: hidden;"),i.setAttribute("type",d);var t=e.appendChild(i);if(t){var n=0;!function r(){if(typeof t.GetVariable!==l)try{var o=t.GetVariable("$version");o&&(o=o.split(" ")[1].split(","),E.pv=[G(o[0]),G(o[1]),G(o[2])])}catch(a){E.pv=[8,0,0]}else if(n<10)return n++,void setTimeout(r,10);e.removeChild(i),t=null,P()}()}else P()}():P()},E.ie&&window.attachEvent("onunload",(function(){for(var e=x.length,i=0;i<e;i++)x[i][0].detachEvent(x[i][1],x[i][2]);for(var t=y.length,n=0;n<t;n++)R(y[n]);for(var r in E)E[r]=null;for(var o in E=null,H)H[o]=null;H=null}));var H={registerObject:function(e,i,t,n){if(E.w3&&e&&i){var r={};r.id=e,r.swfVersion=i,r.expressInstall=t,r.callbackFn=n,b[b.length]=r,z(e,!1)}else n&&n({success:!1,id:e})},getObjectById:function(e){if(E.w3)return N(e)},embedSWF:function(e,i,t,n,r,o,a,s,c,d){var p=V(i),f={success:!1,id:p};E.w3&&!(E.wk&&E.wk<312)&&e&&i&&t&&n&&r?(z(p,!1),M((function(){t+="",n+="";var h={};if(c&&typeof c===u)for(var m in c)h[m]=c[m];h.data=e,h.width=t,h.height=n;var g={};if(s&&typeof s===u)for(var w in s)g[w]=s[w];if(a&&typeof a===u)for(var v in a)if(Object.prototype.hasOwnProperty.call(a,v)){var b=k?encodeURIComponent(v):v,y=k?encodeURIComponent(a[v]):a[v];typeof g.flashvars!==l?g.flashvars+="&"+b+"="+y:g.flashvars=b+"="+y}if(_(r)){var x=O(h,g,i);h.id==p&&z(p,!0),f.success=!0,f.ref=x,f.id=x.id}else{if(o&&I())return h.data=o,void B(h,g,i,d);z(p,!0)}d&&d(f)}))):d&&d(f)},switchOffAutoHideShow:function(){T=!1},enableUriEncoding:function(e){k=typeof e===l||e},ua:E,getFlashPlayerVersion:function(){return{major:E.pv[0],minor:E.pv[1],release:E.pv[2]}},hasFlashPlayerVersion:_,createSWF:function(e,i,t){return E.w3?O(e,i,t):undefined},showExpressInstall:function(e,i,t,n){E.w3&&I()&&B(e,i,t,n)},removeSWF:function(e){E.w3&&R(e)},createCSS:function(e,i,t,n){E.w3&&W(e,i,t,n)},addDomLoadEvent:M,addLoadEvent:function(e){if(typeof h.addEventListener!==l)h.addEventListener("load",e,!1);else if(typeof m.addEventListener!==l)m.addEventListener("load",e,!1);else if(typeof h.attachEvent!==l)!function(e,i,t){e.attachEvent(i,t),x[x.length]=[e,i,t]}(h,"onload",e);else if("function"==typeof h.onload){var i=h.onload;h.onload=function(){i(),e()}}else h.onload=e},getQueryParamValue:function(e){var i=m.location.search||m.location.hash;if(i){if(/\?/.test(i)&&(i=i.split("?")[1]),!e)return q(i);for(var t=i.split("&"),n=0;n<t.length;n++)if(t[n].substring(0,t[n].indexOf("="))==e)return q(t[n].substring(t[n].indexOf("=")+1))}return""},expressInstallCallback:function(){if(C){var e=D(p);e&&t&&(e.parentNode.replaceChild(t,e),n&&(z(n,!0),E.ie&&(t.style.display="block")),r&&r(o)),C=!1}},version:"2.3"};e.exports=H},function(e,i,t){"use strict";var n=t(6),r=t(0).ClientJS,o=t(7),a=t(9);function s(){r.apply(this,arguments)}n(s,r),s.prototype.getJavaVersion=o,s.prototype.getFlashVersion=a,i.ClientJS=s}])}));
//# sourceMappingURL=client.min.js.map