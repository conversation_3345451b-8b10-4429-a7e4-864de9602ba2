/* تصميم لوحة التحكم - تم التحديث */

:root {
  --primary-color: #3498db;
  --secondary-color: #2c3e50;
  --accent-color: #e74c3c;
  --success-color: #2ecc71;
  --warning-color: #f39c12;
  --info-color: #1abc9c;
  --light-color: #ecf0f1;
  --dark-color: #34495e;
  --border-radius: 8px;
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f7fa;
  color: #333;
  margin: 0;
  padding: 0;
  direction: ltr;
  min-height: 100vh;
  overflow-x: hidden;
  font-size: 16px;
  line-height: 1.5;
  box-sizing: border-box;
}

*, *:before, *:after {
  box-sizing: inherit;
}

/* تنسيق العناصر الرئيسية */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 15px;
}

/* تنسيق الأزرار */
.btn {
  border-radius: var(--border-radius);
  transition: var(--transition);
  font-weight: 600;
  padding: 8px 16px;
  border: none;
  cursor: pointer;
  outline: none;
  display: inline-block;
  text-align: center;
  text-decoration: none;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  line-height: 1.5;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.btn:active {
  transform: translateY(0);
  box-shadow: none;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-success {
  background-color: var(--success-color);
  color: white;
}

.btn-danger {
  background-color: var(--accent-color);
  color: white;
}

.btn-warning {
  background-color: var(--warning-color);
  color: white;
}

.btn-info {
  background-color: var(--info-color);
  color: white;
}

/* تنسيق الجداول */
table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
  background-color: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  table-layout: auto;
}

table th {
  background-color: var(--secondary-color);
  color: white;
  padding: 12px 15px;
  text-align: left;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 1;
}

table td {
  padding: 10px 15px;
  border-bottom: 1px solid #ddd;
  word-break: break-word;
}

table tr:last-child td {
  border-bottom: none;
}

table tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  max-width: 100%;
  margin-bottom: 1rem;
}

/* تنسيق النماذج */
input[type="text"],
input[type="password"],
input[type="email"],
input[type="number"],
select,
textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  margin-bottom: 15px;
  transition: var(--transition);
  font-size: 14px;
}

input:focus,
select:focus,
textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
  outline: none;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--dark-color);
}

/* تنسيق مربعات الاختيار */
.checkbox-inline {
  display: inline-flex;
  align-items: center;
  margin-bottom: 10px;
  cursor: pointer;
  font-weight: normal;
}

.checkbox-inline input[type="checkbox"] {
  margin-right: 8px;
  cursor: pointer;
}

/* تنسيق البطاقات */
.card {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin-bottom: 20px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.card-header {
  background-color: var(--secondary-color);
  color: white;
  padding: 15px 20px;
  font-weight: 600;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-body {
  padding: 20px;
  flex: 1 1 auto;
}

/* تنسيق التنبيهات */
.alert {
  padding: 15px 20px;
  border-radius: var(--border-radius);
  margin-bottom: 20px;
  border-left: 4px solid transparent;
}

.alert-success {
  background-color: rgba(46, 204, 113, 0.1);
  border-left-color: var(--success-color);
  color: #27ae60;
}

.alert-danger {
  background-color: rgba(231, 76, 60, 0.1);
  border-left-color: var(--accent-color);
  color: #c0392b;
}

.alert-warning {
  background-color: rgba(243, 156, 18, 0.1);
  border-left-color: var(--warning-color);
  color: #d35400;
}

.alert-info {
  background-color: rgba(52, 152, 219, 0.1);
  border-left-color: var(--primary-color);
  color: #2980b9;
}

/* تنسيق الشارات */
.label {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.label-primary {
  background-color: var(--primary-color);
  color: white;
}

.label-success {
  background-color: var(--success-color);
  color: white;
}

.label-danger {
  background-color: var(--accent-color);
  color: white;
}

.label-warning {
  background-color: var(--warning-color);
  color: white;
}

.label-info {
  background-color: var(--info-color);
  color: white;
}

/* تنسيق الأقسام */
#system, #browser {
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  padding: 15px;
  margin-bottom: 20px;
  background-color: white;
  box-shadow: var(--box-shadow);
}

/* تنسيق الفواصل الأفقية */
hr {
  border: 0;
  height: 1px;
  background-color: #ddd;
  margin: 15px 0;
}

/* تنسيق متجاوب */
/* للأجهزة الكبيرة (أجهزة الكمبيوتر المكتبية) */
@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

/* تنسيق القائمة الجانبية */
.sidebar {
  width: 250px;
  background-color: var(--secondary-color);
  color: white;
  height: 100%;
  position: fixed;
  z-index: 1000;
  overflow-y: auto;
  transition: var(--transition);
}

.sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-menu li {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-menu a {
  display: block;
  padding: 12px 15px;
  color: white;
  text-decoration: none;
  transition: var(--transition);
}

.sidebar-menu a:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* للأجهزة المتوسطة (أجهزة الكمبيوتر المحمولة) */
@media (min-width: 992px) and (max-width: 1199px) {
  .container {
    max-width: 960px;
  }
}

/* للأجهزة الصغيرة (الأجهزة اللوحية) */
@media (min-width: 768px) and (max-width: 991px) {
  .container {
    max-width: 720px;
  }
  
  .card-body {
    padding: 15px;
  }
  
  input[type="text"],
  input[type="password"],
  input[type="email"],
  input[type="number"],
  select,
  textarea {
    padding: 8px 10px;
  }
}

/* للأجهزة الصغيرة جدًا (الهواتف الذكية) */
@media (max-width: 767px) {
  .container {
    padding: 8px;
    width: 100%;
    max-width: 100%;
  }
  
  .checkbox-inline {
    width: 100% !important;
    margin: 5px 0 !important;
  }
  
  table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
    font-size: 12px;
    border-radius: 4px;
    margin-bottom: 15px;
  }
  
  table th,
  table td {
    padding: 8px 6px;
    vertical-align: middle;
  }
  
  /* تحسين عرض الجداول على الهواتف */
  .table-responsive {
    -webkit-overflow-scrolling: touch;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .btn {
    width: auto;
    margin-bottom: 8px;
    padding: 6px 10px;
    font-size: 13px;
  }
  
  .card-header {
    padding: 10px;
    font-size: 13px;
    flex-direction: row;
  }
  
  .card-body {
    padding: 10px;
  }
  
  .alert {
    padding: 10px;
  }
  
  .label {
    font-size: 10px;
    padding: 3px 6px;
  }
  
  #system, #browser {
    padding: 10px;
  }
  
  .row {
    margin-left: -5px;
    margin-right: -5px;
  }
  
  .col {
    padding-left: 5px;
    padding-right: 5px;
  }
  
  /* تحسينات القائمة الجانبية للهواتف */
  .sidebar {
    width: 100%;
    height: auto;
    position: relative;
    max-height: none;
    overflow-y: visible;
    -webkit-overflow-scrolling: touch;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    margin-bottom: 15px;
  }
  
  .sidebar-menu {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
  }
  
  .sidebar-menu li {
    flex: 0 0 auto;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    border-right: none;
  }
  
  .sidebar-menu a {
    padding: 8px 6px;
    font-size: 12px;
    text-align: right;
    display: flex;
    align-items: center;
    min-height: 38px; /* تحسين إمكانية النقر على الأجهزة المحمولة */
  }
}

/* للهواتف الصغيرة */
@media (max-width: 576px) {
  body {
    font-size: 14px;
  }
  
  .container {
    padding: 8px;
  }
  
  table th,
  table td {
    padding: 6px 8px;
    font-size: 12px;
  }
  
  .card-header {
    padding: 10px 12px;
    font-size: 13px;
  }
  
  .card-body {
    padding: 10px;
  }
  
  input[type="text"],
  input[type="password"],
  input[type="email"],
  input[type="number"],
  select,
  textarea {
    padding: 8px;
    font-size: 13px;
    margin-bottom: 10px;
  }
  
  /* تحسين القائمة الجانبية للهواتف الصغيرة */
  .sidebar {
    max-height: none;
    overflow-y: visible;
    position: relative;
  }
  
  .sidebar-menu {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
  }
  
  .sidebar-menu li {
    flex: 0 0 auto;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .sidebar-menu a {
    padding: 8px 5px;
    min-height: 36px;
    font-size: 12px;
    line-height: 1.3;
    display: flex;
    align-items: center;
  }
}

/* تنسيقات إضافية للعناصر المخصصة */
.st_view {
  background-color: rgba(0, 0, 0, 0.5);
  padding: 5px 10px;
  border-radius: 4px;
}

/* تأثيرات حركية */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* تنسيق الأيقونات */
.fa {
  margin-right: 5px;
}

/* تنسيق الصفوف المرنة */
.row {
  display: flex;
  flex-wrap: wrap;
  margin-left: -15px;
  margin-right: -15px;
}

.col {
  flex-basis: 0;
  flex-grow: 1;
  max-width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}

/* تنسيقات للأعمدة بأحجام مختلفة */
.col-12 { flex: 0 0 100%; max-width: 100%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }

@media (max-width: 768px) {
  .col-md-12 { flex: 0 0 100%; max-width: 100%; }
  .col-md-6 { flex: 0 0 50%; max-width: 50%; }
  .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
}

@media (max-width: 576px) {
  .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
  .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
}

/* تنسيقات إضافية للتوافق مع الأجهزة المحمولة */
.d-flex { display: flex; }
.flex-wrap { flex-wrap: wrap; }
.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }
.text-center { text-align: center; }
.img-fluid { max-width: 100%; height: auto; }
.w-100 { width: 100%; }
.mt-3 { margin-top: 15px; }
.mb-3 { margin-bottom: 15px; }
.p-2 { padding: 10px; }

/* تحسينات للتنقل على الأجهزة المحمولة */
@media (max-width: 768px) {
  .mobile-hidden { display: none !important; }
  .mobile-only { display: block !important; }
  .mobile-text-center { text-align: center !important; }
  .mobile-mt-2 { margin-top: 8px !important; }
  .mobile-p-1 { padding: 4px !important; }
  .mobile-flex-column { flex-direction: column !important; }
  .mobile-w-100 { width: 100% !important; }
  .mobile-mb-2 { margin-bottom: 8px !important; }
  .mobile-text-left { text-align: left !important; }
  .mobile-justify-center { justify-content: center !important; }
  
  /* تحسينات إضافية للقائمة على الأجهزة المحمولة */
  .mobile-menu-item { padding: 6px !important; font-size: 13px !important; }
  .mobile-menu-compact { max-height: 85vh; overflow-y: auto; }
  .mobile-font-sm { font-size: 12px !important; }
  .mobile-icon-sm { font-size: 14px !important; }
  .mobile-gap-1 { gap: 4px !important; }
  .mobile-compact-table { font-size: 11px !important; }
  .mobile-compact-table th, .mobile-compact-table td { padding: 4px !important; }
}

/* تحسينات إضافية للأجهزة المحمولة الصغيرة */
@media (max-width: 480px) {
  body {
    font-size: 12px;
  }
  
  .container {
    padding: 4px;
  }
  
  .card-header {
    padding: 6px 8px;
    font-size: 11px;
  }
  
  .card-body {
    padding: 8px;
  }
  
  .btn {
    padding: 5px 8px;
    font-size: 12px;
    margin-bottom: 5px;
    min-height: 36px; /* تحسين إمكانية النقر على الأزرار */
  }
  
  /* إصلاح مشكلة تداخل العمود cl1 مع القائمة الجانبية */
  .cl1 {
    clear: both;
    display: block;
    width: 100%;
    margin-top: 15px;
    margin-right: 0;
    margin-left: 0;
    z-index: 1;
    position: relative;
    float: none;
    overflow: visible;
    padding-right: 5px;
    padding-left: 5px;
    order: 2; /* لضمان ظهوره بعد القائمة الجانبية */
    box-sizing: border-box;
    background-color: transparent;
  }
  
  table th,
  table td {
    padding: 4px 6px;
    font-size: 11px;
  }
  
  /* تحسينات إضافية للقائمة على الهواتف الصغيرة جداً */
  .mobile-menu-item { padding: 3px !important; font-size: 10px !important; }
  .mobile-font-xs { font-size: 9px !important; }
  .mobile-p-0 { padding: 1px !important; }
  .mobile-gap-0 { gap: 1px !important; }
  .mobile-compact-list { line-height: 1.1 !important; }
  
  /* إضافة فئات جديدة لتصغير القائمة العمودية */
  .sidebar-compact { padding: 0 !important; margin: 0 !important; }
  .sidebar-menu-compact a { padding: 4px 3px !important; min-height: 28px !important; font-size: 9px !important; }
  .sidebar-menu-compact .fa { font-size: 10px !important; margin-right: 3px !important; }
  .sidebar-menu-compact li { margin-bottom: 0 !important; }
  
  /* تحسين عرض الجداول على الشاشات الصغيرة جداً */
  .table-responsive {
    max-width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin-bottom: 10px;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  /* تحسين عرض الجداول على الشاشات الصغيرة جداً */
  table {
    font-size: 10px;
    margin-bottom: 10px;
  }
  
  table th {
    position: sticky;
    top: 0;
    z-index: 2;
    white-space: normal;
  }
  
  /* تقليص حجم القائمة الجانبية */
  .sidebar {
    max-height: none;
    overflow-y: visible;
    position: relative;
    display: block;
  }
  
  .sidebar-menu {
    font-size: 11px;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
  }
  
  .sidebar-menu li {
    flex: 0 0 auto;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .sidebar-menu a {
    padding: 6px 4px;
    min-height: 32px;
    font-size: 10px;
    line-height: 1.2;
    display: flex;
    align-items: center;
  }
  
  /* تحسين عرض الأزرار المتجاورة */
  .btn-group .btn {
    padding: 4px 6px;
    font-size: 11px;
  }
  
  /* تحسين النماذج للشاشات الصغيرة جداً */
  input[type="text"],
  input[type="password"],
  input[type="email"],
  input[type="number"],
  select,
  textarea {
    padding: 6px;
    font-size: 12px;
    margin-bottom: 8px;
    min-height: 36px; /* تحسين إمكانية النقر على الحقول */
  }
  
  label {
    font-size: 12px;
    margin-bottom: 4px;
  }
}