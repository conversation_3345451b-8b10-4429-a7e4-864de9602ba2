var Config = {
  Port: 9631,
  Finished: false,
  maxRooms: 500,
  maxPower: 1000,
  MaxBc: 30,
  MaxState: 150,
  MaxLogs: 300,
  MaxFilter: 100,
  maxbot: 20,
  TimeOffline: 400, // 20 ساعة
  hostnm:
  '<div class="fr borderg minix" style="padding:0px;background-color:white;z-index:1;color:lightslategrey;font-size: small!important;height:23px;">Copyright © 2023 <a title="mobail.host" class="mini" href="https://mobail.host/"> موبايل هوست </a>. All Rights Reserved</div>',
  ListDomin: ["e4ex.flhtime.com"],
  MaxComment: 50, // عدد العليقات
  MaxEktisar: 3, // عدد الاختصارا5
  timeLike: 1, // دقيقة
  AccountUserName: "gochat",
  AccountPassword: "123aaaa",
  HostDB: "localhost",
  UserDB: "root",
  PassDB: "smfsajjadsmf003",
  DBDB: "ddsss",
  tokenScreen: "016s87a9d587r4f52gt00369s7a0",
  Backup: 60000 * 60 * 5, //5 ساعات
  MaxUpload: 15 * 1000 * 1000, // 15 مجيا
  PowerNon: {
    rank: 0,
    name: "",
    ico: "",
    kick: 0,
    delbc: 0,
    alert: 0,
    mynick: 0,
    unick: 0,
    ban: 0,
    publicmsg: 0,
    forcepm: 0,
    bootedit: 0,
    roomowner: 0,
    createroom: 0,
    rooms: 0,
    edituser: 0,
    setpower: 0,
    upgrades: 0,
    history: 0,
    cp: 0,
    stealth: 0,
    owner: 0,
    meiut: 0,
    loveu: 0,
    ulike: 0,
    flter: 0,
    subs: 0,
    shrt: 0,
    msgs: 0,
    broadcast: 0,
    camera: 0,
    grupes: 0,
    delcover: 0,
    report: 0,
    delmsg: 0,
    delpic: 0,
  },
  CountryVPN: [
    "AS",
    "NL",
    "AF",
    "AL",
    "AD",
    "AO",
    "AQ",
    "AG",
    "AR",
    "AM",
    "AW",
    "AU",
    "AI",
    "AZ",
    "AT",
    "BB",
    "BJ",
    "BW",
    "BS",
    "BD",
    "BY",
    "BE",
    "US",
    "BZ",
    "BM",
    "BT",
    "BG",
    "BF",
    "BI",
    "KH",
    "A1",
    "BO",
    "BA",
    "BV",
    "BR",
    "CA",
    "CM",
    "ZA",
    "HR",
    "KR",
    "LV",
    "NO",
    "NZ",
    "SI",
    "TD",
    "CN",
    "CL",
    "CO",
    "KM",
    "CR",
    "CG",
    "CY",
    "CZ",
    "DK",
    "DJ",
    "DM",
    "DO",
    "SV",
    "EE",
    "ER",
    "FJ",
    "FI",
    "FR",
    "ET",
    "FJ",
    "GA",
    "GM",
    "DE",
    "KP",
    "GR",
    "GG",
    "GN",
    "HT",
    "HN",
    "HU",
    "IS",
    "IN",
    "ID",
    "IR",
    "IE",
    "IT",
    "JM",
    "JE",
    "KE",
    "KZ",
    "LA",
    "LA",
    "MY",
    "ES",
    "LU",
    "MD",
    "MG",
    "MV",
    "ML",
    "MT",
    "MR",
    "MX",
    "MC",
    "MZ",
    "NG",
    "PK",
    "PE",
    "PA",
    "PH",
    "PL",
    "PT",
    "RO",
    "RU",
    "SN",
    "SG",
    "SO",
    "SD",
    "ES",
    "JP",
    "RS",
    "CH",
    "TW",
    "TZ",
    "TH",
    "TG",
    "UG",
    "UA",
    "UAE",
    "GB",
    "UY",
    "VE",
    "VN",
    "ZM",
    "ZW",
  ],
  BrowserList: [
    "chrome",
    "firefox",
    "safari",
    "opera",
    "gsa",
    "internet explorer",
    "edge",
    "Samsung browser",
    "Samsung internet",
    "Chrome WebView",
    "android webview",
  ],
  PlatformList: [
    "win",
    "windows",
    "linux",
    "android",
    "ios",
    "windows phone",
    "mac",
    "chromium os",
    "mac os",
  ],
  TypeFileImage: {
    "image/gif": "gif",
    "image/x-icon": "ico",
    "image/jpeg": "jpeg",
    "image/jpeg": "jpg",
    "image/png": "png",
    "image/svg+xml": "svg",
    "image/tiff": "tif",
    "image/tiff": "tiff",
    "image/webp": "webp",
  },
  TypeFile: {
    "video/mov": "mov",
    "video/mov": "MOV",
    "video/quicktime": "mov",
    "video/quicktime": "MOV",
    "audio/aac": "aac",
    "audio/m4a": "m4a",
    "audio/x-m4a": "m4a",
    "video/x-msvideo": "avi",
    "image/gif": "gif",
    "image/x-icon": "ico",
    "image/jpeg": "jpeg",
    "image/jpeg": "jpg",
    "audio/midi": "mid",
    "audio/midi": "midi",
    "audio/mpeg": "mp2",
    "audio/mpeg": "mp3",
    "video/mp4": "mp4",
    "video/mpeg": "mpa",
    "video/mpeg": "mpe",
    "video/mpeg": "mpeg",
    "audio/ogg": "oga",
    "video/ogg": "ogv",
    "image/png": "png",
    "image/svg+xml": "svg",
    "image/tiff": "tif",
    "image/tiff": "tiff",
    "audio/x-wav": "wav",
    "audio/webm": "weba",
    "video/webm": "webm",
    "image/webp": "webp",
    "video/3gpp": "3gp",
    "video/3gpp2": "3gp2",
  },
};
module.exports = Config;
