class GetMicLock {
    constructor(dao) {
        this.dao = dao
    }

    createTable() {
        const sql = `
    CREATE TABLE IF NOT EXISTS mic_locks (
      id INTEGER PRIMARY KEY AUTO_INCREMENT,
      mic_id INTEGER,
      is_locked BOOLEAN DEFAULT 0,
      locked_by VARCHAR(255),
      topic VARCHAR(255),
      date TIMESTAMP DEFAULT CURRENT_TIMESTAMP)`;
        return this.dao.run(sql)
    }

    create(data) {
        if(typeof data === 'object') {
            return this.dao.run(
                'INSERT INTO mic_locks (mic_id, is_locked, locked_by, topic) VALUES (?, ?, ?, ?)',
                [data['mic_id'], data['is_locked'], data['locked_by'], data['topic']]
            );
        }
    }

    updateLockStatus(data) {
        if(typeof data === 'object') {
            return this.dao.run(
                'UPDATE mic_locks SET is_locked = ?, locked_by = ? WHERE mic_id = ? AND topic = ?',
                [data['is_locked'], data['locked_by'], data['mic_id'], data['topic']]
            );
        }
    }

    getMicStatus(data) {
        if(typeof data === 'object') {
            return this.dao.get(
                'SELECT * FROM mic_locks WHERE mic_id = ? AND topic = ?',
                [data['mic_id'], data['topic']]
            );
        }
    }

    getAllMicsStatus(topic) {
        return this.dao.all('SELECT * FROM mic_locks WHERE topic = ?', [topic]);
    }

    deleteMicLock(data) {
        if(typeof data === 'object') {
            return this.dao.run(
                'DELETE FROM mic_locks WHERE mic_id = ? AND topic = ?',
                [data['mic_id'], data['topic']]
            );
        }
    }
}

module.exports = GetMicLock;