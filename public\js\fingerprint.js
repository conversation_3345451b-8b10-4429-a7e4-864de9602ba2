/**
 * Device Fingerprinting Module
 * 
 * This module uses FingerprintJS to generate a unique device fingerprint
 * based on various browser and hardware characteristics.
 */

// Use the existing global deviceFingerprint variable
// No need to redeclare it as it's already defined in x2.js

/**
 * Generate a device fingerprint using FingerprintJS
 * @returns {Promise<string>} A promise that resolves to the device fingerprint hash
 */
async function generateFingerprint() {
    return new Promise((resolve, reject) => {
        try {
            // Check if FingerprintJS is available
            if (typeof Fingerprint2 === 'undefined') {
                console.error('FingerprintJS library not loaded');
                reject('FingerprintJS library not loaded');
                return;
            }

            // Wait for page to fully load to ensure accurate fingerprinting
            if (window.requestIdleCallback) {
                requestIdleCallback(() => getFingerprintComponents().then(resolve).catch(reject));
            } else {
                setTimeout(() => getFingerprintComponents().then(resolve).catch(reject), 500);
            }
        } catch (error) {
            console.error('Error generating fingerprint:', error);
            reject(error);
        }
    });
}

/**
 * Get fingerprint components and generate a hash
 * @returns {Promise<string>} A promise that resolves to the fingerprint hash
 */
async function getFingerprintComponents() {
    return new Promise((resolve, reject) => {
        try {
            Fingerprint2.get({ excludes: { webgl: false, canvas: false, fonts: false } }, (components) => {
                // Extract the most stable components for fingerprinting
                const stableComponents = components.filter(component => {
                    return [
                        'canvas', 'webgl', 'webglVendorAndRenderer',
                        'fonts', 'fontsFlash', 'colorDepth',
                        'pixelRatio', 'deviceMemory', 'screenResolution',
                        'availableScreenResolution', 'hardwareConcurrency',
                        'cpuClass', 'platform', 'userAgent',
                        'language', 'timezone', 'sessionStorage',
                        'localStorage', 'indexedDb', 'addBehavior',
                        'openDatabase', 'plugins', 'touchSupport',
                        'audio'
                    ].includes(component.key);
                });

                // Create values string from components
                const values = stableComponents.map(component => component.value).join('|');
                
                // Generate SHA-256 hash from the values
                const fingerprintHash = sha256(values);
                
                // Store the fingerprint for later use
                deviceFingerprint = fingerprintHash;
                
                resolve(fingerprintHash);
            });
        } catch (error) {
            console.error('Error getting fingerprint components:', error);
            reject(error);
        }
    });
}

/**
 * Simple SHA-256 implementation for browser
 * @param {string} str - The string to hash
 * @returns {string} The SHA-256 hash
 */
function sha256(str) {
    // If browser has native crypto support, use it
    if (window.crypto && window.crypto.subtle) {
        // This is an async function but we're in a promise chain already
        // so we'll just return the promise
        const encoder = new TextEncoder();
        const data = encoder.encode(str);
        return window.crypto.subtle.digest('SHA-256', data)
            .then(buffer => {
                // Convert buffer to hex string
                return Array.from(new Uint8Array(buffer))
                    .map(b => b.toString(16).padStart(2, '0'))
                    .join('');
            });
    } else {
        // Fallback to a simple hash function if crypto is not available
        // This is not as secure but better than nothing
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        return Math.abs(hash).toString(16);
    }
}

/**
 * Get the current device fingerprint
 * If not already generated, it will generate one
 * @returns {Promise<string>} A promise that resolves to the device fingerprint
 */
async function getDeviceFingerprint() {
    if (deviceFingerprint) {
        return deviceFingerprint;
    }
    return generateFingerprint();
}

/**
 * Send the fingerprint to the server
 * This should be called when the page loads
 */
async function sendFingerprintToServer() {
    try {
        const fingerprint = await getDeviceFingerprint();
        
        // If socket.io is available, send the fingerprint through socket
        if (typeof socket !== 'undefined' && socket) {
            socket.emit('device_fingerprint', {
                fingerprint: fingerprint
            });
            console.log('Fingerprint sent to server via socket');
        } else {
            console.error('Socket not available to send fingerprint');
        }
    } catch (error) {
        console.error('Error sending fingerprint to server:', error);
    }
}

// Export functions for use in other files
window.DeviceFingerprint = {
    generate: generateFingerprint,
    get: getDeviceFingerprint,
    send: sendFingerprintToServer
};