/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: atars
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `atars` (
  `id` int NOT NULL AUTO_INCREMENT,
  `path` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 26 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: back
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `back` (
  `id` int NOT NULL AUTO_INCREMENT,
  `path` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 23 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: band
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `band` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name_band` varchar(255) DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL,
  `reponse` varchar(255) DEFAULT NULL,
  `device` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `country` varchar(2) DEFAULT NULL,
  `date` varchar(255) DEFAULT 'دائم',
  `created` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: bars
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `bars` (
  `id` int NOT NULL AUTO_INCREMENT,
  `bcc` text DEFAULT (_utf8mb4 ''),
  `likes` text DEFAULT (_utf8mb4 ''),
  `bg` varchar(255) DEFAULT NULL,
  `copic` varchar(255) DEFAULT NULL,
  `ucol` varchar(255) DEFAULT NULL,
  `mcol` varchar(255) DEFAULT NULL,
  `bid` varchar(255) DEFAULT NULL,
  `owner` varchar(255) DEFAULT NULL,
  `msg` varchar(255) DEFAULT NULL,
  `pic` varchar(255) DEFAULT NULL,
  `data` varchar(255) DEFAULT NULL,
  `topic` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: bots
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `bots` (
  `idreg` int NOT NULL AUTO_INCREMENT,
  `msg` varchar(255) DEFAULT '(عضو جديد)',
  `pic` varchar(255) DEFAULT 'pic.png',
  `power` varchar(255) DEFAULT '',
  `country` varchar(255) DEFAULT '',
  `room` varchar(255) DEFAULT '',
  `ip` varchar(255) DEFAULT '',
  `id` varchar(255) DEFAULT '',
  `stat` int DEFAULT '0',
  `likebot` int DEFAULT '0',
  `timestart` int DEFAULT '0',
  `timestop` int DEFAULT '0',
  `autostart` int DEFAULT '0',
  `bg` varchar(255) DEFAULT '#FFFFFF',
  `mcol` varchar(255) DEFAULT '#000000',
  `ucol` varchar(255) DEFAULT '#000000',
  `topic` varchar(255) DEFAULT '',
  PRIMARY KEY (`idreg`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: bsb
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `bsb` (
  `id` int NOT NULL AUTO_INCREMENT,
  `browsers` text DEFAULT (_utf8mb4 ''),
  `systems` varchar(255) DEFAULT (_utf8mb4 ''),
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 2 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: cuts
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `cuts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `msg` varchar(255) DEFAULT NULL,
  `reponse` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: dro3s
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `dro3s` (
  `id` int NOT NULL AUTO_INCREMENT,
  `path` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 50 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: emos
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `emos` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` varchar(255) DEFAULT NULL,
  `path` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 42 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: histletter
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `histletter` (
  `id` int NOT NULL AUTO_INCREMENT,
  `ip` varchar(255) DEFAULT NULL,
  `msg` varchar(255) DEFAULT NULL,
  `topic` varchar(255) DEFAULT NULL,
  `v` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: intromsg
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `intromsg` (
  `id` int NOT NULL AUTO_INCREMENT,
  `category` varchar(255) DEFAULT NULL,
  `adresse` varchar(255) DEFAULT NULL,
  `msg` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: logs
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `state` varchar(255) DEFAULT NULL,
  `topic` varchar(255) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `country` varchar(255) DEFAULT NULL,
  `device` varchar(255) DEFAULT NULL,
  `isin` varchar(255) DEFAULT NULL,
  `date` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 2 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: msgtletter
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `msgtletter` (
  `id` int NOT NULL AUTO_INCREMENT,
  `v` varchar(255) DEFAULT NULL,
  `msg` varchar(255) DEFAULT NULL,
  `topic` varchar(255) DEFAULT NULL,
  `topic2` varchar(255) DEFAULT NULL,
  `joinuser` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: names
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `names` (
  `id` int NOT NULL AUTO_INCREMENT,
  `device` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `topic` varchar(255) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 3 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: nonames
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `nonames` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: notext
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `notext` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` varchar(255) DEFAULT NULL,
  `path` varchar(255) DEFAULT NULL,
  `v` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: powers
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `powers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `powers` text,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 3 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: rooms
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `rooms` (
  `idroom` int NOT NULL AUTO_INCREMENT,
  `about` varchar(255) DEFAULT NULL,
  `user` varchar(255) DEFAULT NULL,
  `pass` varchar(255) DEFAULT NULL,
  `id` varchar(255) DEFAULT NULL,
  `owner` varchar(255) DEFAULT NULL,
  `topic` varchar(255) DEFAULT NULL,
  `color` varchar(255) DEFAULT NULL,
  `colorpicroom` varchar(255) DEFAULT NULL,
  `colormsgroom` varchar(255) DEFAULT NULL,
  `baccolor` varchar(255) DEFAULT NULL,
  `pic` varchar(255) DEFAULT NULL,
  `rmli` int DEFAULT '0',
  `welcome` varchar(255) DEFAULT NULL,
  `broadcast` tinyint(1) DEFAULT '0',
  `nohide` tinyint(1) DEFAULT '0',
  `camera` tinyint(1) DEFAULT '0',
  `deleted` tinyint(1) DEFAULT '0',
  `needpass` tinyint(1) DEFAULT '0',
  `max` int DEFAULT '0',
  `has` int DEFAULT '1',
  `created` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`idroom`)
) ENGINE = InnoDB AUTO_INCREMENT = 2 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: settings
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `logo` varchar(255) DEFAULT 'logo.png',
  `roompic` varchar(255) DEFAULT 'room.png',
  `sitepic` varchar(255) DEFAULT 'site.png',
  `userpic` varchar(255) DEFAULT 'user.png',
  `bg` varchar(255) DEFAULT '8f8589',
  `background` varchar(255) DEFAULT 'FFFFFF',
  `buttons` varchar(255) DEFAULT '8f8589',
  `hostname` varchar(255) DEFAULT NULL,
  `register` tinyint(1) DEFAULT '0',
  `gust` tinyint(1) DEFAULT '0',
  `bars` tinyint(1) DEFAULT '0',
  `replaybc` tinyint(1) DEFAULT '0',
  `likebc` tinyint(1) DEFAULT '1',
  `vpn` tinyint(1) DEFAULT '0',
  `datafinish` datetime DEFAULT CURRENT_TIMESTAMP,
  `maxrep` int DEFAULT '10',
  `maxlogin` int DEFAULT '3',
  `maxek` int DEFAULT '3',
  `callmic` tinyint(1) DEFAULT '0',
  `callsot` tinyint(1) DEFAULT '0',
  `showsto` tinyint(1) DEFAULT '0',
  `showtop` tinyint(1) DEFAULT '0',
  `showyot` tinyint(1) DEFAULT '0',
  `room` varchar(255) DEFAULT NULL,
  `script` varchar(255) DEFAULT 'script.txt',
  `maxdaymsg` int DEFAULT '3',
  `maxlikeroom` int DEFAULT '0',
  `maxlikebc` int DEFAULT '100',
  `maxuploadfile` int DEFAULT '100',
  `bctime` int DEFAULT '0',
  `maxlikename` int DEFAULT '10',
  `maxlikepic` int DEFAULT '10',
  `maxlikeyot` int DEFAULT '10',
  `maxlikecover` int DEFAULT '10',
  `maxlikecam` int DEFAULT '2000',
  `maxlikemic` int DEFAULT '2000',
  `maxlikestory` int DEFAULT '2000',
  `maxlikepm` int DEFAULT '50',
  `maxlikealert` int DEFAULT '50',
  `maxlikesendpicpm` int DEFAULT '100',
  `lengthroom` int DEFAULT '255',
  `lengthpm` int DEFAULT '255',
  `lengthbc` int DEFAULT '255',
  `registermin` int DEFAULT '5',
  `gustmin` int DEFAULT '5',
  `replay` tinyint(1) DEFAULT '0',
  `isbanner` tinyint(1) DEFAULT '0',
  `reconnect` tinyint(1) DEFAULT '0',
  `offline` tinyint(1) DEFAULT '0',
  `banner` varchar(255) DEFAULT 'banner.png',
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 2 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: sicos
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `sicos` (
  `id` int NOT NULL AUTO_INCREMENT,
  `path` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 39 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: stats
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `stats` (
  `id` int NOT NULL AUTO_INCREMENT,
  `state` varchar(255) DEFAULT NULL,
  `topic` varchar(255) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `room` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `time` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: story
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `story` (
  `id` int NOT NULL AUTO_INCREMENT,
  `owner` varchar(255) DEFAULT NULL,
  `topic` varchar(255) DEFAULT NULL,
  `pic` varchar(255) DEFAULT NULL,
  `views` text DEFAULT (_utf8mb4 ''),
  `type` varchar(255) DEFAULT NULL,
  `time` int DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: sub
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `sub` (
  `id` int NOT NULL AUTO_INCREMENT,
  `sub` varchar(255) DEFAULT NULL,
  `topic` varchar(255) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `timestart` varchar(255) DEFAULT NULL,
  `timefinish` varchar(255) DEFAULT NULL,
  `timeis` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: users
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `users` (
  `idreg` int NOT NULL AUTO_INCREMENT,
  `bg` varchar(255) DEFAULT '#FFFFFF',
  `copic` varchar(255) DEFAULT '#FFFFFF',
  `mscol` varchar(255) DEFAULT '#000000',
  `mcol` varchar(255) DEFAULT '#000000',
  `ucol` varchar(255) DEFAULT '#000000',
  `evaluation` int DEFAULT '0',
  `ico` varchar(255) DEFAULT '',
  `ip` varchar(255) DEFAULT '',
  `device` varchar(255) DEFAULT '',
  `id` varchar(255) DEFAULT NULL,
  `lid` varchar(255) DEFAULT NULL,
  `uid` varchar(255) DEFAULT NULL,
  `msg` varchar(255) DEFAULT '(عضو جديد)',
  `youtube` varchar(255) DEFAULT '',
  `pic` varchar(255) DEFAULT 'site/pic.png',
  `power` varchar(255) DEFAULT '',
  `atar` varchar(255) DEFAULT '',
  `back` varchar(255) DEFAULT '',
  `rep` bigint DEFAULT '0',
  `topic` varchar(255) NOT NULL,
  `username` text,
  `password` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `loginG` tinyint(1) DEFAULT '0',
  `muted` tinyint(1) DEFAULT '0',
  `verification` int DEFAULT '0',
  `ifedit` int DEFAULT '0',
  `lastssen` text,
  `joinuser` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`idreg`)
) ENGINE = InnoDB AUTO_INCREMENT = 2 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: atars
# ------------------------------------------------------------

INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (1, '12.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (2, '13.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (3, '16.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (4, '18.gif');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (5, '19.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (6, '21.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (7, '23.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (8, '25.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (9, '26.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (10, '27.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (11, '28.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (12, '29.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (13, '3.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (14, '30.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (15, '33.gif');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (16, '35.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (17, '38.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (18, '4.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (19, '40.gif');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (20, '5.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (21, '50.gif');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (22, '51.gif');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (23, '57.gif');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (24, '6.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (25, '8.png');

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: back
# ------------------------------------------------------------

INSERT INTO
  `back` (`id`, `path`)
VALUES
  (1, '1.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (2, '11.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (3, '15.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (4, '16.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (5, '18.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (6, '19.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (7, '2.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (8, '20.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (9, '27.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (10, '29.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (11, '3.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (12, '32.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (13, '35.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (14, '36.gif');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (15, '4.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (16, '41.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (17, '46.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (18, '5.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (19, '6.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (20, '7.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (21, '8.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (22, '9.png');

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: band
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: bars
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: bots
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: bsb
# ------------------------------------------------------------

INSERT INTO
  `bsb` (`id`, `browsers`, `systems`)
VALUES
  (
    1,
    '{\"browser1\":false,\"browser2\":false,\"browser3\":false,\"browser4\":false,\"browser5\":false,\"browser6\":false,\"browser7\":false,\"browser8\":false,\"browser9\":true}',
    '{\"system1\":false,\"system2\":false,\"system3\":false,\"system4\":false,\"system5\":false,\"system6\":false,\"system7\":true}'
  );

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: cuts
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: dro3s
# ------------------------------------------------------------

INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (1, '1604251747557.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (2, '1604251749625.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (3, '1604251751888.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (4, '1604251754495.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (5, '1604251758520.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (6, '1604251760700.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (7, '1604251763307.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (8, '1604251765529.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (9, '1604251767731.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (10, '1604251769909.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (11, '1604251774614.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (12, '1604251779064.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (13, '1604251782799.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (14, '1604251794339.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (15, '1604251798073.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (16, '1604251802309.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (17, '1604251806907.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (18, '1604251810741.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (19, '1604251814784.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (20, '1604251819379.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (21, '1604251823185.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (22, '1604251827989.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (23, '1604251831990.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (24, '1604251838469.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (25, '1604251846871.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (26, '1604252083138.png');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (27, '1604252116597.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (28, '1604252120799.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (29, '1604252133633.png');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (30, '1604252141485.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (31, '1604252149866.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (32, '1604252158673.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (33, '1604294734475.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (34, '1604700014464.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (35, '1605225797973.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (36, '1605225831200.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (37, '1605225854545.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (38, '1605225861851.png');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (39, '1605244532324.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (40, '1605436560458.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (41, '1605436585380.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (42, '1605594079264.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (43, '1605594102933.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (44, '1605594112839.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (45, '1605594631277.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (46, '1652400785136.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (47, '1652400919112.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (48, '1652400960854.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (49, '1652400964320.gif');

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: emos
# ------------------------------------------------------------

INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (1, '2', '1681448049462.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (2, '3', '1681448077767.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (3, '4', '1681448580550.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (4, '5', '1681448606532.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (5, '6', '1681448613293.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (6, '7', '1681448618687.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (7, '8', '1681448644767.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (8, '9', '1681448655555.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (9, '10', '1681448689138.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (10, '11', '1681448784731.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (11, '12', '1681448793002.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (12, '13', '1681448801490.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (13, '14', '1681448815931.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (14, '15', '1681448828449.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (15, '16', '1681448837129.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (16, '17', '1681448911731.jpg');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (17, '18', '1681448959579.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (18, '19', '1681448977202.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (19, '20', '1681448987309.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (20, '21', '1681449030565.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (21, '22', '1681449047418.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (22, '23', '1681449054639.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (23, '24', '1681449086904.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (24, '25', '1681449172123.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (25, '26', '1681449182922.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (26, '27', '1681449268216.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (27, '28', '1681449282029.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (28, '29', '1681449321066.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (29, '30', '1681449349147.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (30, '31', '1681449382139.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (31, '32', '1681449447357.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (32, '33', '1681449457685.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (33, '34', '1681449474935.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (34, '35', '1681449553556.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (35, '36', '1681449577940.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (36, '37', '1681449602778.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (37, '38', '1681449613070.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (38, '39', '1681449683776.jpg');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (39, '40', '1681449856175.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (40, '41', '1681449872008.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (41, '42', '1681459932020.gif');

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: histletter
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: intromsg
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: logs
# ------------------------------------------------------------

INSERT INTO
  `logs` (
    `id`,
    `state`,
    `topic`,
    `username`,
    `ip`,
    `country`,
    `device`,
    `isin`,
    `date`
  )
VALUES
  (
    1,
    'عضو',
    'gochat',
    'gochat',
    '***************',
    'IQ',
    'Windows.10.8v3xxa.s6ksl.z0rua4.1hb3a0f.153hnf8.6x1881x6.Chrome',
    '',
    '1742838521478'
  );

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: msgtletter
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: names
# ------------------------------------------------------------

INSERT INTO
  `names` (`id`, `device`, `ip`, `topic`, `username`)
VALUES
  (
    1,
    'Windows.10.8v3xxa.s6ksl.z0rua4.1hb3a0f.153hnf8.6x0rr0x6.Chrome',
    '***************',
    'gochat',
    'gochat'
  );
INSERT INTO
  `names` (`id`, `device`, `ip`, `topic`, `username`)
VALUES
  (
    2,
    'Windows.10.8v3xxa.s6ksl.z0rua4.1hb3a0f.153hnf8.6x1881x6.Chrome',
    '***************',
    'gochat',
    'gochat'
  );

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: nonames
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: notext
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: powers
# ------------------------------------------------------------

INSERT INTO
  `powers` (`id`, `name`, `powers`)
VALUES
  (
    1,
    'gochat',
    '{\"rank\":9999,\"name\":\"gochat\",\"ico\":\"\",\"kick\":1,\"delbc\":1,\"alert\":1,\"mynick\":1,\"unick\":1,\"ban\":1,\"publicmsg\":1,\"forcepm\":1,\"roomowner\":1,\"createroom\":1,\"rooms\":1,\"edituser\":1,\"setpower\":1,\"upgrades\":1,\"history\":1,\"cp\":1,\"stealth\":1,\"owner\":1,\"meiut\":1,\"loveu\":1,\"ulike\":1,\"bootedit\":1,\"flter\":1,\"subs\":1,\"shrt\":1,\"msgs\":1,\"broadcast\":1,\"camera\":1,\"grupes\":1,\"delmsg\":1,\"delpic\":1,\"delcover\":1,\"report\":1,\"ureport\":1}'
  );
INSERT INTO
  `powers` (`id`, `name`, `powers`)
VALUES
  (
    2,
    'admin',
    '{\"rank\":9000,\"name\":\"admin\",\"ico\":\"\",\"kick\":1,\"delbc\":1,\"alert\":1,\"mynick\":1,\"unick\":1,\"ban\":1,\"publicmsg\":1,\"forcepm\":1,\"roomowner\":1,\"createroom\":1,\"rooms\":1,\"edituser\":1,\"setpower\":1,\"upgrades\":1,\"history\":1,\"cp\":1,\"stealth\":1,\"owner\":1,\"meiut\":1,\"loveu\":1,\"ulike\":1,\"bootedit\":1,\"flter\":1,\"subs\":1,\"shrt\":1,\"msgs\":1,\"broadcast\":1,\"camera\":1,\"grupes\":1,\"delmsg\":1,\"delpic\":1,\"delcover\":1,\"report\":1,\"ureport\":1}'
  );

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: rooms
# ------------------------------------------------------------

INSERT INTO
  `rooms` (
    `idroom`,
    `about`,
    `user`,
    `pass`,
    `id`,
    `owner`,
    `topic`,
    `color`,
    `colorpicroom`,
    `colormsgroom`,
    `baccolor`,
    `pic`,
    `rmli`,
    `welcome`,
    `broadcast`,
    `nohide`,
    `camera`,
    `deleted`,
    `needpass`,
    `max`,
    `has`,
    `created`
  )
VALUES
  (
    1,
    'غرفه عامة',
    'gochat',
    '',
    '3ihxjl18it',
    '#1',
    'الغرفة العامة',
    '#000000',
    '#000000',
    '#fff000',
    '#fff',
    '/site/room.png',
    0,
    'مرحبا بيكم في الغرفة العامة',
    0,
    0,
    0,
    1,
    0,
    40,
    1,
    '2025-03-23 15:53:37'
  );

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: settings
# ------------------------------------------------------------

INSERT INTO
  `settings` (
    `id`,
    `logo`,
    `roompic`,
    `sitepic`,
    `userpic`,
    `bg`,
    `background`,
    `buttons`,
    `hostname`,
    `register`,
    `gust`,
    `bars`,
    `replaybc`,
    `likebc`,
    `vpn`,
    `datafinish`,
    `maxrep`,
    `maxlogin`,
    `maxek`,
    `callmic`,
    `callsot`,
    `showsto`,
    `showtop`,
    `showyot`,
    `room`,
    `script`,
    `maxdaymsg`,
    `maxlikeroom`,
    `maxlikebc`,
    `maxuploadfile`,
    `bctime`,
    `maxlikename`,
    `maxlikepic`,
    `maxlikeyot`,
    `maxlikecover`,
    `maxlikecam`,
    `maxlikemic`,
    `maxlikestory`,
    `maxlikepm`,
    `maxlikealert`,
    `maxlikesendpicpm`,
    `lengthroom`,
    `lengthpm`,
    `lengthbc`,
    `registermin`,
    `gustmin`,
    `replay`,
    `isbanner`,
    `reconnect`,
    `offline`,
    `banner`
  )
VALUES
  (
    1,
    'e4ex.flhtime.comlogo.png',
    'e4ex.flhtime.comroom.png',
    'e4ex.flhtime.comsite.png',
    'e4ex.flhtime.comuser.png',
    '8f8589',
    'FFFFFF',
    '8f8589',
    'e4ex.flhtime.com',
    0,
    0,
    0,
    0,
    1,
    0,
    '2025-03-23 15:53:37',
    10,
    3,
    3,
    0,
    0,
    0,
    0,
    0,
    '3ihxjl18it',
    'e4ex.flhtime.com.txt',
    3,
    0,
    100,
    100,
    0,
    10,
    10,
    10,
    10,
    2000,
    2000,
    2000,
    50,
    50,
    100,
    255,
    255,
    255,
    5,
    5,
    0,
    0,
    0,
    0,
    'banner.png'
  );

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: sicos
# ------------------------------------------------------------

INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (1, '1684337525494.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (2, '1684337539727.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (3, '1684337558145.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (4, '1684337571744.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (5, '1684337587089.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (6, '1684337612101.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (7, '1684337626286.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (8, '1684337646847.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (9, '1684337679771.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (10, '1684337709315.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (11, '1684337733110.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (12, '1684342082397.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (13, '1684342095906.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (14, '1684342106324.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (15, '1684342138237.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (16, '1684343212458.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (17, '1684363917102.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (18, '1684363969719.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (19, '1684365676293.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (20, '1684464495933.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (21, '1684464509371.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (22, '1684464548626.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (23, '1684465379618.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (24, '1684532100461.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (25, '1684532112281.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (26, '1684532122225.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (27, '1684532160388.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (28, '1684532202036.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (29, '1684532241280.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (30, '1684532259045.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (31, '1684532282306.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (32, '1685394072310.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (33, '1685398669485.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (34, '1685405066342.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (35, '1685800393373.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (36, '1695108634571.jpg');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (37, '1695108636965.jpg');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (38, '1695656492912.gif');

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: stats
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: story
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: sub
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: users
# ------------------------------------------------------------

INSERT INTO
  `users` (
    `idreg`,
    `bg`,
    `copic`,
    `mscol`,
    `mcol`,
    `ucol`,
    `evaluation`,
    `ico`,
    `ip`,
    `device`,
    `id`,
    `lid`,
    `uid`,
    `msg`,
    `youtube`,
    `pic`,
    `power`,
    `atar`,
    `back`,
    `rep`,
    `topic`,
    `username`,
    `password`,
    `token`,
    `loginG`,
    `muted`,
    `verification`,
    `ifedit`,
    `lastssen`,
    `joinuser`
  )
VALUES
  (
    1,
    '#FFFFFF',
    '#FFFFFF',
    '#000000',
    '#000000',
    '#000000',
    0,
    '',
    '***************',
    'Windows.10.8v3xxa.s6ksl.z0rua4.1hb3a0f.153hnf8.6x1881x6.Chrome',
    'oV_evyFuTj9IaAKaAAAA',
    'd150hoklu197jeh5pashdib1t3oy0zr',
    '58n9sx8hbvx19hat8x9hli',
    '(عضو جديد)',
    '',
    'site/pic.png',
    'gochat',
    NULL,
    NULL,
    0,
    'gochat',
    'gochat',
    'sha1$b5203b38$1$baaeb92b6bc3a33aac2a578bb128c0818a45f688',
    'j9xdr1eqxeq27rh1tqi0ak1eqq3m3a650aw1t1qoo1o5q1ozoogpbiygmh5aocbzovinn47ly4d06k7st8tusb6mtuawz52embg6v4l6yo8poszki8qbh96wmbdjkxwxhvhrk5vjurdtln4kc8x9q05wb695fn61w7r8a2rjv8vw4du44',
    0,
    0,
    1,
    NULL,
    '1742841628748',
    '2025-03-23 15:53:37'
  );

/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
