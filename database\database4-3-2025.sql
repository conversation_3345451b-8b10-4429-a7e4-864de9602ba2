/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: atars
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `atars` (
  `id` int NOT NULL AUTO_INCREMENT,
  `path` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 27 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: back
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `back` (
  `id` int NOT NULL AUTO_INCREMENT,
  `path` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 23 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: band
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `band` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name_band` varchar(255) DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL,
  `reponse` varchar(255) DEFAULT NULL,
  `device` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `country` varchar(2) DEFAULT NULL,
  `date` varchar(255) DEFAULT 'دائم',
  `created` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: bars
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `bars` (
  `id` int NOT NULL AUTO_INCREMENT,
  `bcc` text DEFAULT (_utf8mb4 ''),
  `likes` text DEFAULT (_utf8mb4 ''),
  `bg` varchar(255) DEFAULT NULL,
  `copic` varchar(255) DEFAULT NULL,
  `ucol` varchar(255) DEFAULT NULL,
  `mcol` varchar(255) DEFAULT NULL,
  `bid` varchar(255) DEFAULT NULL,
  `owner` varchar(255) DEFAULT NULL,
  `msg` varchar(255) DEFAULT NULL,
  `pic` varchar(255) DEFAULT NULL,
  `data` varchar(255) DEFAULT NULL,
  `topic` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 2 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: bots
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `bots` (
  `idreg` int NOT NULL AUTO_INCREMENT,
  `msg` varchar(255) DEFAULT '(عضو جديد)',
  `pic` varchar(255) DEFAULT 'pic.png',
  `power` varchar(255) DEFAULT '',
  `country` varchar(255) DEFAULT '',
  `room` varchar(255) DEFAULT '',
  `ip` varchar(255) DEFAULT '',
  `id` varchar(255) DEFAULT '',
  `stat` int DEFAULT '0',
  `likebot` int DEFAULT '0',
  `timestart` int DEFAULT '0',
  `timestop` int DEFAULT '0',
  `autostart` int DEFAULT '0',
  `bg` varchar(255) DEFAULT '#FFFFFF',
  `mcol` varchar(255) DEFAULT '#000000',
  `ucol` varchar(255) DEFAULT '#000000',
  `topic` varchar(255) DEFAULT '',
  PRIMARY KEY (`idreg`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: bsb
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `bsb` (
  `id` int NOT NULL AUTO_INCREMENT,
  `browsers` text DEFAULT (_utf8mb4 ''),
  `systems` varchar(255) DEFAULT (_utf8mb4 ''),
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 2 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: cuts
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `cuts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `msg` varchar(255) DEFAULT NULL,
  `reponse` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: dro3s
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `dro3s` (
  `id` int NOT NULL AUTO_INCREMENT,
  `path` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 77 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: emos
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `emos` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` varchar(255) DEFAULT NULL,
  `path` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 145 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: histletter
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `histletter` (
  `id` int NOT NULL AUTO_INCREMENT,
  `ip` varchar(255) DEFAULT NULL,
  `msg` varchar(255) DEFAULT NULL,
  `topic` varchar(255) DEFAULT NULL,
  `v` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: intromsg
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `intromsg` (
  `id` int NOT NULL AUTO_INCREMENT,
  `category` varchar(255) DEFAULT NULL,
  `adresse` varchar(255) DEFAULT NULL,
  `msg` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: logs
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `state` varchar(255) DEFAULT NULL,
  `topic` varchar(255) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `country` varchar(255) DEFAULT NULL,
  `device` varchar(255) DEFAULT NULL,
  `isin` varchar(255) DEFAULT NULL,
  `date` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 2 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: msgtletter
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `msgtletter` (
  `id` int NOT NULL AUTO_INCREMENT,
  `v` varchar(255) DEFAULT NULL,
  `msg` varchar(255) DEFAULT NULL,
  `topic` varchar(255) DEFAULT NULL,
  `topic2` varchar(255) DEFAULT NULL,
  `joinuser` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: names
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `names` (
  `id` int NOT NULL AUTO_INCREMENT,
  `device` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `topic` varchar(255) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 2 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: nonames
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `nonames` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: notext
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `notext` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` varchar(255) DEFAULT NULL,
  `path` varchar(255) DEFAULT NULL,
  `v` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: powers
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `powers` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `powers` text,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 3 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: rooms
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `rooms` (
  `idroom` int NOT NULL AUTO_INCREMENT,
  `about` varchar(255) DEFAULT NULL,
  `user` varchar(255) DEFAULT NULL,
  `pass` varchar(255) DEFAULT NULL,
  `id` varchar(255) DEFAULT NULL,
  `owner` varchar(255) DEFAULT NULL,
  `topic` varchar(255) DEFAULT NULL,
  `color` varchar(255) DEFAULT NULL,
  `colorpicroom` varchar(255) DEFAULT NULL,
  `colormsgroom` varchar(255) DEFAULT NULL,
  `baccolor` varchar(255) DEFAULT NULL,
  `pic` varchar(255) DEFAULT NULL,
  `rmli` int DEFAULT '0',
  `welcome` varchar(255) DEFAULT NULL,
  `broadcast` tinyint(1) DEFAULT '0',
  `nohide` tinyint(1) DEFAULT '0',
  `camera` tinyint(1) DEFAULT '0',
  `deleted` tinyint(1) DEFAULT '0',
  `needpass` tinyint(1) DEFAULT '0',
  `max` int DEFAULT '0',
  `has` int DEFAULT '1',
  `created` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`idroom`)
) ENGINE = InnoDB AUTO_INCREMENT = 2 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: settings
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `logo` varchar(255) DEFAULT 'logo.png',
  `roompic` varchar(255) DEFAULT 'room.png',
  `sitepic` varchar(255) DEFAULT 'site.png',
  `userpic` varchar(255) DEFAULT 'user.png',
  `bg` varchar(255) DEFAULT '8f8589',
  `background` varchar(255) DEFAULT 'FFFFFF',
  `buttons` varchar(255) DEFAULT '8f8589',
  `hostname` varchar(255) DEFAULT NULL,
  `register` tinyint(1) DEFAULT '0',
  `gust` tinyint(1) DEFAULT '0',
  `bars` tinyint(1) DEFAULT '0',
  `replaybc` tinyint(1) DEFAULT '0',
  `likebc` tinyint(1) DEFAULT '1',
  `vpn` tinyint(1) DEFAULT '0',
  `datafinish` datetime DEFAULT CURRENT_TIMESTAMP,
  `maxrep` int DEFAULT '10',
  `maxlogin` int DEFAULT '3',
  `maxek` int DEFAULT '3',
  `callmic` tinyint(1) DEFAULT '0',
  `callsot` tinyint(1) DEFAULT '0',
  `showsto` tinyint(1) DEFAULT '0',
  `showtop` tinyint(1) DEFAULT '0',
  `showyot` tinyint(1) DEFAULT '0',
  `room` varchar(255) DEFAULT NULL,
  `script` varchar(255) DEFAULT 'script.txt',
  `maxdaymsg` int DEFAULT '3',
  `maxlikeroom` int DEFAULT '0',
  `maxlikebc` int DEFAULT '100',
  `maxuploadfile` int DEFAULT '100',
  `bctime` int DEFAULT '0',
  `maxlikename` int DEFAULT '10',
  `maxlikepic` int DEFAULT '10',
  `maxlikeyot` int DEFAULT '10',
  `maxlikecover` int DEFAULT '10',
  `maxlikecam` int DEFAULT '2000',
  `maxlikemic` int DEFAULT '2000',
  `maxlikestory` int DEFAULT '2000',
  `maxlikepm` int DEFAULT '50',
  `maxlikealert` int DEFAULT '50',
  `maxlikesendpicpm` int DEFAULT '100',
  `lengthroom` int DEFAULT '255',
  `lengthpm` int DEFAULT '255',
  `lengthbc` int DEFAULT '255',
  `registermin` int DEFAULT '5',
  `gustmin` int DEFAULT '5',
  `replay` tinyint(1) DEFAULT '0',
  `isbanner` tinyint(1) DEFAULT '0',
  `reconnect` tinyint(1) DEFAULT '0',
  `offline` tinyint(1) DEFAULT '0',
  `banner` varchar(255) DEFAULT 'banner.png',
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 2 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: sicos
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `sicos` (
  `id` int NOT NULL AUTO_INCREMENT,
  `path` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 39 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: stats
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `stats` (
  `id` int NOT NULL AUTO_INCREMENT,
  `state` varchar(255) DEFAULT NULL,
  `topic` varchar(255) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `room` varchar(255) DEFAULT NULL,
  `ip` varchar(255) DEFAULT NULL,
  `time` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB AUTO_INCREMENT = 2 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: story
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `story` (
  `id` int NOT NULL AUTO_INCREMENT,
  `owner` varchar(255) DEFAULT NULL,
  `topic` varchar(255) DEFAULT NULL,
  `pic` varchar(255) DEFAULT NULL,
  `views` text DEFAULT (_utf8mb4 ''),
  `type` varchar(255) DEFAULT NULL,
  `time` int DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `date` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: sub
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `sub` (
  `id` int NOT NULL AUTO_INCREMENT,
  `sub` varchar(255) DEFAULT NULL,
  `topic` varchar(255) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `timestart` varchar(255) DEFAULT NULL,
  `timefinish` varchar(255) DEFAULT NULL,
  `timeis` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# SCHEMA DUMP FOR TABLE: users
# ------------------------------------------------------------

CREATE TABLE IF NOT EXISTS `users` (
  `idreg` int NOT NULL AUTO_INCREMENT,
  `bg` varchar(255) DEFAULT '#FFFFFF',
  `copic` varchar(255) DEFAULT '#FFFFFF',
  `mscol` varchar(255) DEFAULT '#000000',
  `mcol` varchar(255) DEFAULT '#000000',
  `ucol` varchar(255) DEFAULT '#000000',
  `evaluation` int DEFAULT '0',
  `ico` varchar(255) DEFAULT '',
  `ip` varchar(255) DEFAULT '',
  `device` varchar(255) DEFAULT '',
  `id` varchar(255) DEFAULT NULL,
  `lid` varchar(255) DEFAULT NULL,
  `uid` varchar(255) DEFAULT NULL,
  `msg` varchar(255) DEFAULT '(عضو جديد)',
  `youtube` varchar(255) DEFAULT '',
  `pic` varchar(255) DEFAULT 'site/pic.png',
  `power` varchar(255) DEFAULT '',
  `atar` varchar(255) DEFAULT '',
  `back` varchar(255) DEFAULT '',
  `rep` bigint DEFAULT '0',
  `topic` varchar(255) NOT NULL,
  `username` text,
  `password` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `loginG` tinyint(1) DEFAULT '0',
  `muted` tinyint(1) DEFAULT '0',
  `verification` int DEFAULT '0',
  `ifedit` int DEFAULT '0',
  `lastssen` text,
  `joinuser` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`idreg`)
) ENGINE = InnoDB AUTO_INCREMENT = 2 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci;

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: atars
# ------------------------------------------------------------

INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (1, '10.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (2, '12.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (3, '13.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (4, '16.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (5, '18.gif');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (6, '19.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (7, '21.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (8, '23.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (9, '25.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (10, '26.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (11, '27.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (12, '28.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (13, '29.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (14, '3.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (15, '30.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (16, '33.gif');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (17, '35.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (18, '38.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (19, '4.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (20, '40.gif');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (21, '5.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (22, '50.gif');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (23, '51.gif');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (24, '57.gif');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (25, '6.png');
INSERT INTO
  `atars` (`id`, `path`)
VALUES
  (26, '8.png');

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: back
# ------------------------------------------------------------

INSERT INTO
  `back` (`id`, `path`)
VALUES
  (1, '1.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (2, '11.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (3, '15.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (4, '16.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (5, '18.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (6, '19.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (7, '2.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (8, '20.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (9, '27.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (10, '29.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (11, '3.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (12, '32.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (13, '35.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (14, '36.gif');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (15, '4.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (16, '41.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (17, '46.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (18, '5.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (19, '6.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (20, '7.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (21, '8.png');
INSERT INTO
  `back` (`id`, `path`)
VALUES
  (22, '9.png');

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: band
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: bars
# ------------------------------------------------------------

INSERT INTO
  `bars` (
    `id`,
    `bcc`,
    `likes`,
    `bg`,
    `copic`,
    `ucol`,
    `mcol`,
    `bid`,
    `owner`,
    `msg`,
    `pic`,
    `data`,
    `topic`
  )
VALUES
  (
    1,
    '',
    '',
    '#FFFFFF',
    '#FFFFFF',
    '#000000',
    '#000000',
    'btdbc4rhf3',
    'ki6hmel6ebd2xext67zvs3h3szmdbl5',
    'wregr\n',
    'site/pic.png',
    '1743206249728',
    'gochat'
  );

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: bots
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: bsb
# ------------------------------------------------------------

INSERT INTO
  `bsb` (`id`, `browsers`, `systems`)
VALUES
  (
    1,
    '{\"browser1\":false,\"browser2\":false,\"browser3\":false,\"browser4\":false,\"browser5\":false,\"browser6\":false,\"browser7\":false,\"browser8\":false,\"browser9\":true}',
    '{\"system1\":false,\"system2\":false,\"system3\":false,\"system4\":false,\"system5\":false,\"system6\":false,\"system7\":true}'
  );

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: cuts
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: dro3s
# ------------------------------------------------------------

INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (1, '1604251745029.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (2, '1604251747557.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (3, '1604251749625.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (4, '1604251751888.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (5, '1604251754495.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (6, '1604251758520.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (7, '1604251760700.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (8, '1604251763307.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (9, '1604251765529.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (10, '1604251767731.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (11, '1604251769909.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (12, '1604251774614.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (13, '1604251779064.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (14, '1604251782799.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (15, '1604251794339.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (16, '1604251798073.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (17, '1604251802309.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (18, '1604251806907.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (19, '1604251810741.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (20, '1604251814784.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (21, '1604251819379.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (22, '1604251823185.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (23, '1604251827989.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (24, '1604251831990.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (25, '1604251838469.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (26, '1604251846871.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (27, '1604252083138.png');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (28, '1604252116597.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (29, '1604252120799.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (30, '1604252133633.png');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (31, '1604252141485.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (32, '1604252149866.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (33, '1604252158673.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (34, '1604294734475.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (35, '1604700014464.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (36, '1605225797973.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (37, '1605225831200.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (38, '1605225854545.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (39, '1605225861851.png');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (40, '1605244532324.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (41, '1605436560458.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (42, '1605436585380.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (43, '1605594079264.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (44, '1605594102933.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (45, '1605594112839.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (46, '1605594631277.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (47, '1684341173986.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (48, '1684341182280.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (49, '1684341993900.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (50, '1684342012678.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (51, '1684342024916.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (52, '1684342033217.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (53, '1684342057442.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (54, '1684361254851.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (55, '1684361270745.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (56, '1684361326248.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (57, '1684361361256.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (58, '1684361375143.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (59, '1684361428149.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (60, '1684361443784.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (61, '1684538522818.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (62, '1684538536140.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (63, '1684538549171.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (64, '1684538556434.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (65, '1684538565082.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (66, '1684538585321.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (67, '1684538613843.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (68, '1684538663464.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (69, '1684538678370.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (70, '1684538736835.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (71, '1684538761246.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (72, '1684538811418.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (73, '1684538873112.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (74, '1684538911371.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (75, '1684538941658.gif');
INSERT INTO
  `dro3s` (`id`, `path`)
VALUES
  (76, '1684538980386.gif');

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: emos
# ------------------------------------------------------------

INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (1, '2', '1681448038549.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (2, '3', '1681448044983.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (3, '4', '1681448049462.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (4, '5', '1681448053227.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (5, '6', '1681448057433.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (6, '7', '1681448063550.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (7, '8', '1681448077767.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (8, '9', '1681448450781.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (9, '10', '1681448463837.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (10, '11', '1681448567448.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (11, '12', '1681448575302.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (12, '13', '1681448580550.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (13, '14', '1681448588315.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (14, '15', '1681448594356.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (15, '16', '1681448599209.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (16, '17', '1681448606532.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (17, '18', '1681448613293.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (18, '19', '1681448618687.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (19, '20', '1681448628673.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (20, '21', '1681448636177.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (21, '22', '1681448644767.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (22, '23', '1681448655555.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (23, '24', '1681448689138.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (24, '25', '1681448695304.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (25, '26', '1681448707916.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (26, '27', '1681448714524.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (27, '28', '1681448731631.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (28, '29', '1681448752107.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (29, '30', '1681448756434.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (30, '31', '1681448765561.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (31, '32', '1681448784731.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (32, '33', '1681448793002.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (33, '34', '1681448801490.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (34, '35', '1681448815931.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (35, '36', '1681448828449.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (36, '37', '1681448837129.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (37, '38', '1681448847775.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (38, '39', '1681448861848.jpg');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (39, '40', '1681448871317.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (40, '41', '1681448911731.jpg');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (41, '42', '1681448931657.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (42, '43', '1681448940018.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (43, '44', '1681448959579.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (44, '45', '1681448977202.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (45, '46', '1681448987309.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (46, '47', '1681449000971.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (47, '48', '1681449008683.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (48, '49', '1681449017014.jpg');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (49, '50', '1681449030565.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (50, '51', '1681449040535.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (51, '52', '1681449047418.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (52, '53', '1681449054639.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (53, '54', '1681449064223.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (54, '55', '1681449086904.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (55, '56', '1681449123828.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (56, '57', '1681449138088.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (57, '58', '1681449154155.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (58, '59', '1681449172123.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (59, '60', '1681449182922.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (60, '61', '1681449194423.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (61, '62', '1681449223612.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (62, '63', '1681449241955.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (63, '64', '1681449258020.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (64, '65', '1681449268216.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (65, '66', '1681449282029.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (66, '67', '1681449304537.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (67, '68', '1681449321066.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (68, '69', '1681449335103.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (69, '70', '1681449349147.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (70, '71', '1681449368105.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (71, '72', '1681449382139.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (72, '73', '1681449396714.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (73, '74', '1681449419010.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (74, '75', '1681449447357.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (75, '76', '1681449457685.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (76, '77', '1681449474935.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (77, '78', '1681449494477.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (78, '79', '1681449504440.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (79, '80', '1681449511648.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (80, '81', '1681449526909.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (81, '82', '1681449553556.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (82, '83', '1681449566356.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (83, '84', '1681449577940.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (84, '85', '1681449589238.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (85, '86', '1681449602778.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (86, '87', '1681449613070.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (87, '88', '1681449622325.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (88, '89', '1681449630268.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (89, '90', '1681449640680.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (90, '91', '1681449683776.jpg');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (91, '92', '1681449703709.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (92, '93', '1681449856175.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (93, '94', '1681449872008.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (94, '95', '1681449880304.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (95, '96', '1681449892105.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (96, '97', '1681459921901.jpg');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (97, '98', '1681459932020.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (98, '99', '1681460165319.jpg');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (99, '100', '1681460170513.jpg');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (100, '101', '1684284218272.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (101, '102', '1684284337679.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (102, '103', '1684284398213.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (103, '104', '1684284977029.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (104, '105', '1684288522743.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (105, '106', '1684288553921.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (106, '107', '1684288580359.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (107, '108', '1684288655227.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (108, '109', '1684288693185.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (109, '110', '1684288730210.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (110, '111', '1684288773874.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (111, '112', '1684288793369.jpg');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (112, '113', '1684288849003.jpg');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (113, '114', '1684288895202.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (114, '115', '1684288928684.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (115, '116', '1684289308262.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (116, '117', '1684289332695.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (117, '118', '1684289351093.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (118, '119', '1684289368595.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (119, '120', '1684289381077.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (120, '121', '1684289412984.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (121, '122', '1684289430712.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (122, '123', '1684289454417.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (123, '124', '1684289470695.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (124, '125', '1684289497415.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (125, '126', '1684289513827.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (126, '127', '1684289531821.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (127, '128', '1684289557383.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (128, '129', '1684289571135.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (129, '130', '1684289623488.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (130, '131', '1684289707758.jpg');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (131, '132', '1684289747959.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (132, '133', '1684289825966.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (133, '134', '1684289859659.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (134, '135', '1684289928454.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (135, '136', '1684289971678.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (136, '137', '1684293305463.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (137, '138', '1684293470685.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (138, '139', '1684293499460.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (139, '140', '1684293583415.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (140, '141', '1684369888789.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (141, '142', '1684369905027.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (142, '143', '1684369918976.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (143, '144', '1684946339748.gif');
INSERT INTO
  `emos` (`id`, `type`, `path`)
VALUES
  (144, '145', '1685800811163.gif');

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: histletter
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: intromsg
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: logs
# ------------------------------------------------------------

INSERT INTO
  `logs` (
    `id`,
    `state`,
    `topic`,
    `username`,
    `ip`,
    `country`,
    `device`,
    `isin`,
    `date`
  )
VALUES
  (
    1,
    'عضو',
    'gochat',
    'gochat',
    '***************',
    'IQ',
    'win10|.8v.Chr.3xx|d3c.887.23a|e83.ea1.ba4|m',
    '',
    '1743206233633'
  );

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: msgtletter
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: names
# ------------------------------------------------------------

INSERT INTO
  `names` (`id`, `device`, `ip`, `topic`, `username`)
VALUES
  (
    1,
    'win10|.8v.Chr.3xx|d3c.887.23a|e83.ea1.ba4|m',
    '***************',
    'gochat',
    'gochat'
  );

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: nonames
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: notext
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: powers
# ------------------------------------------------------------

INSERT INTO
  `powers` (`id`, `name`, `powers`)
VALUES
  (
    1,
    'gochat',
    '{\"rank\":9999,\"name\":\"gochat\",\"ico\":\"\",\"kick\":1,\"delbc\":1,\"alert\":1,\"mynick\":1,\"unick\":1,\"ban\":1,\"publicmsg\":1,\"forcepm\":1,\"roomowner\":1,\"createroom\":1,\"rooms\":1,\"edituser\":1,\"setpower\":1,\"upgrades\":1,\"history\":1,\"cp\":1,\"stealth\":1,\"owner\":1,\"meiut\":1,\"loveu\":1,\"ulike\":1,\"bootedit\":1,\"flter\":1,\"subs\":1,\"shrt\":1,\"msgs\":1,\"broadcast\":1,\"camera\":1,\"grupes\":1,\"delmsg\":1,\"delpic\":1,\"delcover\":1,\"report\":1,\"ureport\":1}'
  );
INSERT INTO
  `powers` (`id`, `name`, `powers`)
VALUES
  (
    2,
    'admin',
    '{\"rank\":9000,\"name\":\"admin\",\"ico\":\"\",\"kick\":1,\"delbc\":1,\"alert\":1,\"mynick\":1,\"unick\":1,\"ban\":1,\"publicmsg\":1,\"forcepm\":1,\"roomowner\":1,\"createroom\":1,\"rooms\":1,\"edituser\":1,\"setpower\":1,\"upgrades\":1,\"history\":1,\"cp\":1,\"stealth\":1,\"owner\":1,\"meiut\":1,\"loveu\":1,\"ulike\":1,\"bootedit\":1,\"flter\":1,\"subs\":1,\"shrt\":1,\"msgs\":1,\"broadcast\":1,\"camera\":1,\"grupes\":1,\"delmsg\":1,\"delpic\":1,\"delcover\":1,\"report\":1,\"ureport\":1}'
  );

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: rooms
# ------------------------------------------------------------

INSERT INTO
  `rooms` (
    `idroom`,
    `about`,
    `user`,
    `pass`,
    `id`,
    `owner`,
    `topic`,
    `color`,
    `colorpicroom`,
    `colormsgroom`,
    `baccolor`,
    `pic`,
    `rmli`,
    `welcome`,
    `broadcast`,
    `nohide`,
    `camera`,
    `deleted`,
    `needpass`,
    `max`,
    `has`,
    `created`
  )
VALUES
  (
    1,
    'غرفه عامة',
    'gochat',
    '',
    '3ihxjl18it',
    '#1',
    'الغرفة العامة',
    '#000000',
    '#000000',
    '#fff000',
    '#fff',
    '/site/room.png',
    0,
    'مرحبا بيكم في الغرفة العامة',
    0,
    0,
    0,
    1,
    0,
    40,
    1,
    '2025-03-29 00:57:03'
  );

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: settings
# ------------------------------------------------------------

INSERT INTO
  `settings` (
    `id`,
    `logo`,
    `roompic`,
    `sitepic`,
    `userpic`,
    `bg`,
    `background`,
    `buttons`,
    `hostname`,
    `register`,
    `gust`,
    `bars`,
    `replaybc`,
    `likebc`,
    `vpn`,
    `datafinish`,
    `maxrep`,
    `maxlogin`,
    `maxek`,
    `callmic`,
    `callsot`,
    `showsto`,
    `showtop`,
    `showyot`,
    `room`,
    `script`,
    `maxdaymsg`,
    `maxlikeroom`,
    `maxlikebc`,
    `maxuploadfile`,
    `bctime`,
    `maxlikename`,
    `maxlikepic`,
    `maxlikeyot`,
    `maxlikecover`,
    `maxlikecam`,
    `maxlikemic`,
    `maxlikestory`,
    `maxlikepm`,
    `maxlikealert`,
    `maxlikesendpicpm`,
    `lengthroom`,
    `lengthpm`,
    `lengthbc`,
    `registermin`,
    `gustmin`,
    `replay`,
    `isbanner`,
    `reconnect`,
    `offline`,
    `banner`
  )
VALUES
  (
    1,
    'e4ex.flhtime.comlogo.png',
    'e4ex.flhtime.comroom.png',
    'e4ex.flhtime.comsite.png',
    'e4ex.flhtime.comuser.png',
    '8f8589',
    'FFFFFF',
    '8f8589',
    'e4ex.flhtime.com',
    0,
    0,
    0,
    0,
    1,
    0,
    '2025-03-29 00:57:03',
    10,
    3,
    3,
    0,
    0,
    0,
    0,
    0,
    '3ihxjl18it',
    'e4ex.flhtime.com.txt',
    3,
    0,
    100,
    100,
    0,
    10,
    10,
    10,
    10,
    2000,
    2000,
    2000,
    50,
    50,
    100,
    255,
    255,
    255,
    5,
    5,
    0,
    0,
    0,
    0,
    'banner.png'
  );

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: sicos
# ------------------------------------------------------------

INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (1, '1684337525494.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (2, '1684337539727.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (3, '1684337558145.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (4, '1684337571744.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (5, '1684337587089.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (6, '1684337612101.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (7, '1684337626286.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (8, '1684337646847.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (9, '1684337679771.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (10, '1684337709315.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (11, '1684337733110.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (12, '1684342082397.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (13, '1684342095906.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (14, '1684342106324.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (15, '1684342138237.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (16, '1684343212458.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (17, '1684363917102.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (18, '1684363969719.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (19, '1684365676293.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (20, '1684464495933.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (21, '1684464509371.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (22, '1684464548626.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (23, '1684465379618.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (24, '1684532100461.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (25, '1684532112281.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (26, '1684532122225.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (27, '1684532160388.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (28, '1684532202036.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (29, '1684532241280.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (30, '1684532259045.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (31, '1684532282306.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (32, '1685394072310.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (33, '1685398669485.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (34, '1685405066342.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (35, '1685800393373.gif');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (36, '1695108634571.jpg');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (37, '1695108636965.jpg');
INSERT INTO
  `sicos` (`id`, `path`)
VALUES
  (38, '1695656492912.gif');

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: stats
# ------------------------------------------------------------

INSERT INTO
  `stats` (
    `id`,
    `state`,
    `topic`,
    `username`,
    `room`,
    `ip`,
    `time`
  )
VALUES
  (
    1,
    'تعديل اعجابات',
    'gochat',
    'gochat',
    'الغرفة العامة',
    '***************',
    '1743206246650'
  );

# ------------------------------------------------------------
# DATA DUMP FOR TABLE: story
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: sub
# ------------------------------------------------------------


# ------------------------------------------------------------
# DATA DUMP FOR TABLE: users
# ------------------------------------------------------------

INSERT INTO
  `users` (
    `idreg`,
    `bg`,
    `copic`,
    `mscol`,
    `mcol`,
    `ucol`,
    `evaluation`,
    `ico`,
    `ip`,
    `device`,
    `id`,
    `lid`,
    `uid`,
    `msg`,
    `youtube`,
    `pic`,
    `power`,
    `atar`,
    `back`,
    `rep`,
    `topic`,
    `username`,
    `password`,
    `token`,
    `loginG`,
    `muted`,
    `verification`,
    `ifedit`,
    `lastssen`,
    `joinuser`
  )
VALUES
  (
    1,
    '#FFFFFF',
    '#FFFFFF',
    '#000000',
    '#000000',
    '#000000',
    1,
    '',
    '***************',
    'win10|.8v.Chr.3xx|d3c.887.23a|e83.ea1.ba4|m',
    'DmR-p4zVXmluk3clAAAA',
    'ki6hmel6ebd2xext67zvs3h3szmdbl5',
    'wtxnz48y7cj3n2wr6uwpmz',
    '(عضو جديد)',
    '',
    'site/pic.png',
    'gochat',
    NULL,
    NULL,
    2435467,
    'gochat',
    'gochat',
    'sha1$b47d0ac9$1$4898dd39e2e9a31c7fe5aca96d508077b2c751e7',
    'fir4c8f945ndwq1akwgue5zsizznr8mnk1jc5xld5qhilehxllr0yh152r0vve8qcqiwdffvzarbqva376szge5c60cx2bdnkznlc0lhmykwf50qft11lefdzaur3ozhuzrxyu1ahhzjmttvamqfmcf5zab1q6aqe5qne0y8szgy1lcbs',
    0,
    0,
    1,
    NULL,
    '1743213576316',
    '2025-03-29 00:57:02'
  );

/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
