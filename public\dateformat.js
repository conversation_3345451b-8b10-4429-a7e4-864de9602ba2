// دالة لتنسيق التاريخ بالشكل العالمي
Date.prototype.formatDate = function() {
  var year = this.getFullYear();
  var month = (this.getMonth() + 1).toString().padStart(2, '0');
  var day = this.getDate().toString().padStart(2, '0');
  return year + '/' + month + '/' + day;
};

// تعديل دالة toLocaleDateString لتستخدم التنسيق العالمي
Date.prototype.toLocaleDateString = function() {
  return this.formatDate();
};

// دالة لتنسيق الوقت
Date.prototype.formatTime = function() {
  var hours = this.getHours().toString().padStart(2, '0');
  var minutes = this.getMinutes().toString().padStart(2, '0');
  var seconds = this.getSeconds().toString().padStart(2, '0');
  return hours + ':' + minutes + ':' + seconds;
};

// دالة لتنسيق التاريخ والوقت معًا
Date.prototype.formatDateTime = function() {
  return this.formatDate() + ' ' + this.formatTime();
};