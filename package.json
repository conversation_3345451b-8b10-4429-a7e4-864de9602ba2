{"name": "GoChat", "version": "3.0.0", "description": "Modern chat application by mobail Host", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js", "pm2:delete": "pm2 delete ecosystem.config.js", "test": "jest", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "ejs": "^3.1.9", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "fluent-ffmpeg": "^2.1.2", "GoChat": "file:", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "node-fetch": "^3.3.2", "sharp": "^0.33.1", "socket.io": "^4.7.5", "uuid": "^9.0.1", "winston": "^3.11.0", "ws": "^8.14.2"}, "engines": {"node": ">=18.0.0"}, "devDependencies": {"eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}}