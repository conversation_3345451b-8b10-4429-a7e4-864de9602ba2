<!DOCTYPE html>
<html lang="ar" hreflang="ar-sa" style="height: 100%;">

<head>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
  <meta property="og:title" content="" />
  <meta property="og:description" content="" />
  <meta property="og:image" content="" />
  <link rel="icon" type="image/x-icon" href="" />
  <meta name="google" value="notranslate" />
  <meta name="HandheldFriendly" content="True" />
  <meta name="viewport" content=" user-scalable=0, width=device-width" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="mobile-web-app-capable" content="yes" />
  <title>

  </title>
  <meta name="description" content="" />
  <meta content="" name="keywords" />
  <script src="j.js?z" type="text/javascript"></script>
  <script src="js/so.js" type="text/javascript"></script>
  <script src="admin.js?d"></script>
  <script>
    setTimeout(() => {
      allload();
    }, 500);
    RefreshPWS();
</script>
 <style>table.dataTable th.dt-left,table.dataTable td.dt-left{text-align:left}table.dataTable th.dt-center,table.dataTable td.dt-center,table.dataTable td.dataTables_empty{text-align:center}table.dataTable th.dt-right,table.dataTable td.dt-right{text-align:right}table.dataTable th.dt-justify,table.dataTable td.dt-justify{text-align:justify}table.dataTable th.dt-nowrap,table.dataTable td.dt-nowrap{white-space:nowrap}table.dataTable thead th.dt-head-left,table.dataTable thead td.dt-head-left,table.dataTable tfoot th.dt-head-left,table.dataTable tfoot td.dt-head-left{text-align:left}table.dataTable thead th.dt-head-center,table.dataTable thead td.dt-head-center,table.dataTable tfoot th.dt-head-center,table.dataTable tfoot td.dt-head-center{text-align:center}table.dataTable thead th.dt-head-right,table.dataTable thead td.dt-head-right,table.dataTable tfoot th.dt-head-right,table.dataTable tfoot td.dt-head-right{text-align:right}table.dataTable thead th.dt-head-justify,table.dataTable thead td.dt-head-justify,table.dataTable tfoot th.dt-head-justify,table.dataTable tfoot td.dt-head-justify{text-align:justify}table.dataTable thead th.dt-head-nowrap,table.dataTable thead td.dt-head-nowrap,table.dataTable tfoot th.dt-head-nowrap,table.dataTable tfoot td.dt-head-nowrap{white-space:nowrap}table.dataTable tbody th.dt-body-left,table.dataTable tbody td.dt-body-left{text-align:left}table.dataTable tbody th.dt-body-center,table.dataTable tbody td.dt-body-center{text-align:center}table.dataTable tbody th.dt-body-right,table.dataTable tbody td.dt-body-right{text-align:right}table.dataTable tbody th.dt-body-justify,table.dataTable tbody td.dt-body-justify{text-align:justify}table.dataTable tbody th.dt-body-nowrap,table.dataTable tbody td.dt-body-nowrap{white-space:nowrap}table.dataTable td.dt-control{text-align:center;cursor:pointer}table.dataTable td.dt-control:before{height:1em;width:1em;margin-top:-9px;display:inline-block;color:#fff;border:.15em solid #fff;border-radius:1em;box-shadow:0 0 .2em #444;box-sizing:content-box;text-align:center;text-indent:0!important;font-family:"Courier New",Courier,monospace;line-height:1em;content:"+";background-color:#31b131}table.dataTable tr.dt-hasChild td.dt-control:before{content:"-";background-color:#d33333}table.dataTable{width:100%;margin:0 auto;clear:both;border-collapse:separate;border-spacing:0}table.dataTable thead th,table.dataTable tfoot th{font-weight:700}table.dataTable thead th,table.dataTable thead td{padding:10px 18px;border-bottom:1px solid #111}table.dataTable thead th:active,table.dataTable thead td:active{outline:none}table.dataTable tfoot th,table.dataTable tfoot td{padding:10px 18px 6px;border-top:1px solid #111}table.dataTable thead .sorting,table.dataTable thead .sorting_asc,table.dataTable thead .sorting_desc,table.dataTable thead .sorting_asc_disabled,table.dataTable thead .sorting_desc_disabled{cursor:pointer;*cursor:hand;background-repeat:no-repeat;background-position:center right}table.dataTable thead .sorting{background-image:url(../images/sort_both.png)}table.dataTable thead .sorting_asc{background-image:url(../images/sort_asc.png)!important}table.dataTable thead .sorting_desc{background-image:url(../images/sort_desc.png)!important}table.dataTable thead .sorting_asc_disabled{background-image:url(../images/sort_asc_disabled.png)}table.dataTable thead .sorting_desc_disabled{background-image:url(../images/sort_desc_disabled.png)}table.dataTable tbody tr{background-color:#fff}table.dataTable tbody tr.selected{background-color:#b0bed9}table.dataTable tbody th,table.dataTable tbody td{padding:8px 10px}table.dataTable.row-border tbody th,table.dataTable.row-border tbody td,table.dataTable.display tbody th,table.dataTable.display tbody td{border-top:1px solid #ddd}table.dataTable.row-border tbody tr:first-child th,table.dataTable.row-border tbody tr:first-child td,table.dataTable.display tbody tr:first-child th,table.dataTable.display tbody tr:first-child td{border-top:none}table.dataTable.cell-border tbody th,table.dataTable.cell-border tbody td{border-top:1px solid #ddd;border-right:1px solid #ddd}table.dataTable.cell-border tbody tr th:first-child,table.dataTable.cell-border tbody tr td:first-child{border-left:1px solid #ddd}table.dataTable.cell-border tbody tr:first-child th,table.dataTable.cell-border tbody tr:first-child td{border-top:none}table.dataTable.stripe tbody tr.odd,table.dataTable.display tbody tr.odd{background-color:#f9f9f9}table.dataTable.stripe tbody tr.odd.selected,table.dataTable.display tbody tr.odd.selected{background-color:#acbad4}table.dataTable.hover tbody tr:hover,table.dataTable.display tbody tr:hover{background-color:#f6f6f6}table.dataTable.hover tbody tr:hover.selected,table.dataTable.display tbody tr:hover.selected{background-color:#aab7d1}table.dataTable.order-column tbody tr>.sorting_1,table.dataTable.order-column tbody tr>.sorting_2,table.dataTable.order-column tbody tr>.sorting_3,table.dataTable.display tbody tr>.sorting_1,table.dataTable.display tbody tr>.sorting_2,table.dataTable.display tbody tr>.sorting_3{background-color:#fafafa}table.dataTable.order-column tbody tr.selected>.sorting_1,table.dataTable.order-column tbody tr.selected>.sorting_2,table.dataTable.order-column tbody tr.selected>.sorting_3,table.dataTable.display tbody tr.selected>.sorting_1,table.dataTable.display tbody tr.selected>.sorting_2,table.dataTable.display tbody tr.selected>.sorting_3{background-color:#acbad5}table.dataTable.display tbody tr.odd>.sorting_1,table.dataTable.order-column.stripe tbody tr.odd>.sorting_1{background-color:#f1f1f1}table.dataTable.display tbody tr.odd>.sorting_2,table.dataTable.order-column.stripe tbody tr.odd>.sorting_2{background-color:#f3f3f3}table.dataTable.display tbody tr.odd>.sorting_3,table.dataTable.order-column.stripe tbody tr.odd>.sorting_3{background-color:#f5f5f5}table.dataTable.display tbody tr.odd.selected>.sorting_1,table.dataTable.order-column.stripe tbody tr.odd.selected>.sorting_1{background-color:#a6b4cd}table.dataTable.display tbody tr.odd.selected>.sorting_2,table.dataTable.order-column.stripe tbody tr.odd.selected>.sorting_2{background-color:#a8b5cf}table.dataTable.display tbody tr.odd.selected>.sorting_3,table.dataTable.order-column.stripe tbody tr.odd.selected>.sorting_3{background-color:#a9b7d1}table.dataTable.display tbody tr.even>.sorting_1,table.dataTable.order-column.stripe tbody tr.even>.sorting_1{background-color:#fafafa}table.dataTable.display tbody tr.even>.sorting_2,table.dataTable.order-column.stripe tbody tr.even>.sorting_2{background-color:#fcfcfc}table.dataTable.display tbody tr.even>.sorting_3,table.dataTable.order-column.stripe tbody tr.even>.sorting_3{background-color:#fefefe}table.dataTable.display tbody tr.even.selected>.sorting_1,table.dataTable.order-column.stripe tbody tr.even.selected>.sorting_1{background-color:#acbad5}table.dataTable.display tbody tr.even.selected>.sorting_2,table.dataTable.order-column.stripe tbody tr.even.selected>.sorting_2{background-color:#aebcd6}table.dataTable.display tbody tr.even.selected>.sorting_3,table.dataTable.order-column.stripe tbody tr.even.selected>.sorting_3{background-color:#afbdd8}table.dataTable.display tbody tr:hover>.sorting_1,table.dataTable.order-column.hover tbody tr:hover>.sorting_1{background-color:#eaeaea}table.dataTable.display tbody tr:hover>.sorting_2,table.dataTable.order-column.hover tbody tr:hover>.sorting_2{background-color:#ececec}table.dataTable.display tbody tr:hover>.sorting_3,table.dataTable.order-column.hover tbody tr:hover>.sorting_3{background-color:#efefef}table.dataTable.display tbody tr:hover.selected>.sorting_1,table.dataTable.order-column.hover tbody tr:hover.selected>.sorting_1{background-color:#a2aec7}table.dataTable.display tbody tr:hover.selected>.sorting_2,table.dataTable.order-column.hover tbody tr:hover.selected>.sorting_2{background-color:#a3b0c9}table.dataTable.display tbody tr:hover.selected>.sorting_3,table.dataTable.order-column.hover tbody tr:hover.selected>.sorting_3{background-color:#a5b2cb}table.dataTable.no-footer{border-bottom:1px solid #111}table.dataTable.nowrap th,table.dataTable.nowrap td{white-space:nowrap}table.dataTable.compact thead th,table.dataTable.compact thead td{padding:4px 17px}table.dataTable.compact tfoot th,table.dataTable.compact tfoot td{padding:4px}table.dataTable.compact tbody th,table.dataTable.compact tbody td{padding:4px}table.dataTable th,table.dataTable td{box-sizing:content-box}.dataTables_wrapper{position:relative;clear:both}.dataTables_wrapper .dataTables_length{float:left}.dataTables_wrapper .dataTables_length select{border:1px solid #aaa;border-radius:3px;padding:5px;background-color:transparent;padding:4px}.dataTables_wrapper .dataTables_filter{float:right;text-align:right}.dataTables_wrapper .dataTables_filter input{border:1px solid #aaa;border-radius:3px;padding:5px;background-color:transparent;margin-left:3px}.dataTables_wrapper .dataTables_info{clear:both;float:left;padding-top:.755em}.dataTables_wrapper .dataTables_paginate{float:right;text-align:right;padding-top:.25em}.dataTables_wrapper .dataTables_paginate .paginate_button{box-sizing:border-box;display:inline-block;min-width:1.5em;padding:.5em 1em;margin-left:2px;text-align:center;text-decoration:none!important;cursor:pointer;*cursor:hand;color:#333!important;border:1px solid transparent;border-radius:2px}.dataTables_wrapper .dataTables_paginate .paginate_button.current,.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover{color:#333!important;border:1px solid #979797;background-color:#fff;background:-webkit-gradient(linear,left top,left bottom,color-stop(0%,white),color-stop(100%,#dcdcdc));background:-webkit-linear-gradient(top,white 0%,#dcdcdc 100%);background:-moz-linear-gradient(top,white 0%,#dcdcdc 100%);background:-ms-linear-gradient(top,white 0%,#dcdcdc 100%);background:-o-linear-gradient(top,white 0%,#dcdcdc 100%);background:linear-gradient(to bottom,white 0%,#dcdcdc 100%)}.dataTables_wrapper .dataTables_paginate .paginate_button.disabled,.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover,.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:active{cursor:default;color:#666!important;border:1px solid transparent;background:transparent;box-shadow:none}.dataTables_wrapper .dataTables_paginate .paginate_button:hover{color:#fff!important;border:1px solid #111;background-color:#585858;background:-webkit-gradient(linear,left top,left bottom,color-stop(0%,#585858),color-stop(100%,#111));background:-webkit-linear-gradient(top,#585858 0%,#111 100%);background:-moz-linear-gradient(top,#585858 0%,#111 100%);background:-ms-linear-gradient(top,#585858 0%,#111 100%);background:-o-linear-gradient(top,#585858 0%,#111 100%);background:linear-gradient(to bottom,#585858 0%,#111 100%)}.dataTables_wrapper .dataTables_paginate .paginate_button:active{outline:none;background-color:#2b2b2b;background:-webkit-gradient(linear,left top,left bottom,color-stop(0%,#2b2b2b),color-stop(100%,#0c0c0c));background:-webkit-linear-gradient(top,#2b2b2b 0%,#0c0c0c 100%);background:-moz-linear-gradient(top,#2b2b2b 0%,#0c0c0c 100%);background:-ms-linear-gradient(top,#2b2b2b 0%,#0c0c0c 100%);background:-o-linear-gradient(top,#2b2b2b 0%,#0c0c0c 100%);background:linear-gradient(to bottom,#2b2b2b 0%,#0c0c0c 100%);box-shadow:inset 0 0 3px #111}.dataTables_wrapper .dataTables_paginate .ellipsis{padding:0 1em}.dataTables_wrapper .dataTables_processing{position:absolute;top:50%;left:50%;width:100%;height:40px;margin-left:-50%;margin-top:-25px;padding-top:20px;text-align:center;font-size:1.2em;background-color:#fff;background:-webkit-gradient(linear,left top,right top,color-stop(0%,rgba(255,255,255,0)),color-stop(25%,rgba(255,255,255,0.9)),color-stop(75%,rgba(255,255,255,0.9)),color-stop(100%,rgba(255,255,255,0)));background:-webkit-linear-gradient(left,rgba(255,255,255,0) 0%,rgba(255,255,255,0.9) 25%,rgba(255,255,255,0.9) 75%,rgba(255,255,255,0) 100%);background:-moz-linear-gradient(left,rgba(255,255,255,0) 0%,rgba(255,255,255,0.9) 25%,rgba(255,255,255,0.9) 75%,rgba(255,255,255,0) 100%);background:-ms-linear-gradient(left,rgba(255,255,255,0) 0%,rgba(255,255,255,0.9) 25%,rgba(255,255,255,0.9) 75%,rgba(255,255,255,0) 100%);background:-o-linear-gradient(left,rgba(255,255,255,0) 0%,rgba(255,255,255,0.9) 25%,rgba(255,255,255,0.9) 75%,rgba(255,255,255,0) 100%);background:linear-gradient(to right,rgba(255,255,255,0) 0%,rgba(255,255,255,0.9) 25%,rgba(255,255,255,0.9) 75%,rgba(255,255,255,0) 100%)}.dataTables_wrapper .dataTables_length,.dataTables_wrapper .dataTables_filter,.dataTables_wrapper .dataTables_info,.dataTables_wrapper .dataTables_processing,.dataTables_wrapper .dataTables_paginate{color:#333}.dataTables_wrapper .dataTables_scroll{clear:both}.dataTables_wrapper .dataTables_scroll div.dataTables_scrollBody{*margin-top:-1px;-webkit-overflow-scrolling:touch}.dataTables_wrapper .dataTables_scroll div.dataTables_scrollBody>table>thead>tr>th,.dataTables_wrapper .dataTables_scroll div.dataTables_scrollBody>table>thead>tr>td,.dataTables_wrapper .dataTables_scroll div.dataTables_scrollBody>table>tbody>tr>th,.dataTables_wrapper .dataTables_scroll div.dataTables_scrollBody>table>tbody>tr>td{vertical-align:middle}.dataTables_wrapper .dataTables_scroll div.dataTables_scrollBody>table>thead>tr>th>div.dataTables_sizing,.dataTables_wrapper .dataTables_scroll div.dataTables_scrollBody>table>thead>tr>td>div.dataTables_sizing,.dataTables_wrapper .dataTables_scroll div.dataTables_scrollBody>table>tbody>tr>th>div.dataTables_sizing,.dataTables_wrapper .dataTables_scroll div.dataTables_scrollBody>table>tbody>tr>td>div.dataTables_sizing{height:0;overflow:hidden;margin:0!important;padding:0!important}.dataTables_wrapper.no-footer .dataTables_scrollBody{border-bottom:1px solid #111}.dataTables_wrapper.no-footer div.dataTables_scrollHead table.dataTable,.dataTables_wrapper.no-footer div.dataTables_scrollBody>table{border-bottom:none}.dataTables_wrapper:after{visibility:hidden;display:block;content:"";clear:both;height:0}@media screen and (max-width: 767px){.dataTables_wrapper .dataTables_info,.dataTables_wrapper .dataTables_paginate{float:none;text-align:center}.dataTables_wrapper .dataTables_paginate{margin-top:.5em}}@media screen and (max-width: 640px){.dataTables_wrapper .dataTables_length,.dataTables_wrapper .dataTables_filter{float:none;text-align:center}.dataTables_wrapper .dataTables_filter{margin-top:.5em}}html{font-family:sans-serif;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}body{margin:0}article,aside,details,figcaption,figure,footer,header,hgroup,main,menu,nav,section,summary{display:block}audio,canvas,progress,video{display:inline-block;vertical-align:baseline}audio:not([controls]){display:none;height:0}[hidden],template{display:none}a{background-color:transparent}a:active,a:hover{outline:0}abbr[title]{border-bottom:none;text-decoration:underline;text-decoration:underline dotted}b,strong{font-weight:700}dfn{font-style:italic}h1{font-size:2em;margin:.67em 0}mark{background:#ff0;color:#000}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sup{top:-.5em}sub{bottom:-.25em}img{border:0}svg:not(:root){overflow:hidden}figure{margin:1em 40px}hr{-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;height:0}pre{overflow:auto}code,kbd,pre,samp{font-family:monospace,monospace;font-size:1em}button,input,optgroup,select,textarea{color:inherit;font:inherit;margin:0}button{overflow:visible}button,select{text-transform:none}button,html input[type="button"],input[type="reset"],input[type="submit"]{-webkit-appearance:button;cursor:pointer}button[disabled],html input[disabled]{cursor:default}button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}input{line-height:normal}input[type="checkbox"],input[type="radio"]{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;padding:0}input[type="number"]::-webkit-inner-spin-button,input[type="number"]::-webkit-outer-spin-button{height:auto}input[type="search"]{-webkit-appearance:textfield;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box}input[type="search"]::-webkit-search-cancel-button,input[type="search"]::-webkit-search-decoration{-webkit-appearance:none}fieldset{border:1px solid silver;margin:0 2px;padding:.35em .625em .75em}legend{border:0;padding:0}textarea{overflow:auto}optgroup{font-weight:700}table{border-collapse:collapse;border-spacing:0}td,th{padding:0}*{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}:before,:after{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}html{font-size:10px;-webkit-tap-highlight-color:rgba(0,0,0,0)}body{font-family:"Helvetica Neue",Helvetica,Arial,sans-serif;font-size:14px;line-height:1.42857143;color:#333;background-color:#fff}input,button,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit}a{color:#337ab7;text-decoration:none}a:hover,a:focus{color:#23527c;text-decoration:underline}a:focus{outline:5px auto -webkit-focus-ring-color;outline-offset:-2px}figure{margin:0}img{vertical-align:middle}.img-responsive{display:block;max-width:100%;height:auto}.img-rounded{border-radius:6px}.img-thumbnail{padding:4px;line-height:1.42857143;background-color:#fff;border:1px solid #ddd;border-radius:4px;-webkit-transition:all .2s ease-in-out;-o-transition:all .2s ease-in-out;transition:all .2s ease-in-out;display:inline-block;max-width:100%;height:auto}.img-circle{border-radius:50%}hr{margin-top:20px;margin-bottom:20px;border:0;border-top:1px solid #eee}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);border:0}.sr-only-focusable:active,.sr-only-focusable:focus{position:static;width:auto;height:auto;margin:0;overflow:visible;clip:auto}[role="button"]{cursor:pointer}fieldset{min-width:0;padding:0;margin:0;border:0}legend{display:block;width:100%;padding:0;margin-bottom:20px;font-size:21px;line-height:inherit;color:#333;border:0;border-bottom:1px solid #e5e5e5}label{display:inline-block;max-width:100%;margin-bottom:5px;font-weight:700}input[type="search"]{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;appearance:none}input[type="radio"],input[type="checkbox"]{margin:4px 0 0;margin-top:1px \9;line-height:normal}input[type="radio"][disabled],input[type="checkbox"][disabled],input[type="radio"].disabled,input[type="checkbox"].disabled,fieldset[disabled] input[type="radio"],fieldset[disabled] input[type="checkbox"]{cursor:not-allowed}input[type="file"]{display:block}input[type="range"]{display:block;width:100%}select[multiple],select[size]{height:auto}input[type="file"]:focus,input[type="radio"]:focus,input[type="checkbox"]:focus{outline:5px auto -webkit-focus-ring-color;outline-offset:-2px}output{display:block;padding-top:7px;font-size:14px;line-height:1.42857143;color:#555}.form-control{display:block;width:100%;height:34px;padding:6px 12px;font-size:14px;line-height:1.42857143;color:#555;background-color:#fff;background-image:none;border:1px solid #ccc;border-radius:4px;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);-webkit-transition:border-color ease-in-out .15s,-webkit-box-shadow ease-in-out .15s;-o-transition:border-color ease-in-out .15s,box-shadow ease-in-out .15s;transition:border-color ease-in-out .15s,box-shadow ease-in-out .15s}.form-control:focus{border-color:#66afe9;outline:0;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,0.6);box-shadow:inset 0 1px 1px rgba(0,0,0,.075),0 0 8px rgba(102,175,233,0.6)}.form-control::-moz-placeholder{color:#999;opacity:1}.form-control:-ms-input-placeholder{color:#999}.form-control::-webkit-input-placeholder{color:#999}.form-control::-ms-expand{background-color:transparent;border:0}.form-control[disabled],.form-control[readonly],fieldset[disabled] .form-control{background-color:#eee;opacity:1}.form-control[disabled],fieldset[disabled] .form-control{cursor:not-allowed}textarea.form-control{height:auto}@media screen and (-webkit-min-device-pixel-ratio:0){input[type="date"].form-control,input[type="time"].form-control,input[type="datetime-local"].form-control,input[type="month"].form-control{line-height:34px}input[type="date"].input-sm,input[type="time"].input-sm,input[type="datetime-local"].input-sm,input[type="month"].input-sm,.input-group-sm input[type="date"],.input-group-sm input[type="time"],.input-group-sm input[type="datetime-local"],.input-group-sm input[type="month"]{line-height:30px}input[type="date"].input-lg,input[type="time"].input-lg,input[type="datetime-local"].input-lg,input[type="month"].input-lg,.input-group-lg input[type="date"],.input-group-lg input[type="time"],.input-group-lg input[type="datetime-local"],.input-group-lg input[type="month"]{line-height:46px}}.form-group{margin-bottom:15px}.radio,.checkbox{position:relative;display:block;margin-top:10px;margin-bottom:10px}.radio.disabled label,.checkbox.disabled label,fieldset[disabled] .radio label,fieldset[disabled] .checkbox label{cursor:not-allowed}.radio label,.checkbox label{min-height:20px;padding-left:20px;margin-bottom:0;font-weight:400;cursor:pointer}.radio input[type="radio"],.radio-inline input[type="radio"],.checkbox input[type="checkbox"],.checkbox-inline input[type="checkbox"]{position:absolute;margin-top:4px \9;margin-left:-20px}.radio+.radio,.checkbox+.checkbox{margin-top:-5px}.radio-inline,.checkbox-inline{position:relative;display:inline-block;padding-left:20px;margin-bottom:0;font-weight:400;vertical-align:middle;cursor:pointer}.radio-inline.disabled,.checkbox-inline.disabled,fieldset[disabled] .radio-inline,fieldset[disabled] .checkbox-inline{cursor:not-allowed}.radio-inline+.radio-inline,.checkbox-inline+.checkbox-inline{margin-top:0;margin-left:10px}.form-control-static{min-height:34px;padding-top:7px;padding-bottom:7px;margin-bottom:0}.form-control-static.input-lg,.form-control-static.input-sm{padding-right:0;padding-left:0}.input-sm{height:30px;padding:5px 10px;font-size:12px;line-height:1.5;border-radius:3px}select.input-sm{height:30px;line-height:30px}textarea.input-sm,select[multiple].input-sm{height:auto}.form-group-sm .form-control{height:30px;padding:5px 10px;font-size:12px;line-height:1.5;border-radius:3px}.form-group-sm select.form-control{height:30px;line-height:30px}.form-group-sm textarea.form-control,.form-group-sm select[multiple].form-control{height:auto}.form-group-sm .form-control-static{height:30px;min-height:32px;padding:6px 10px;font-size:12px;line-height:1.5}.input-lg{height:46px;padding:10px 16px;font-size:18px;line-height:1.3333333;border-radius:6px}select.input-lg{height:46px;line-height:46px}textarea.input-lg,select[multiple].input-lg{height:auto}.form-group-lg .form-control{height:46px;padding:10px 16px;font-size:18px;line-height:1.3333333;border-radius:6px}.form-group-lg select.form-control{height:46px;line-height:46px}.form-group-lg textarea.form-control,.form-group-lg select[multiple].form-control{height:auto}.form-group-lg .form-control-static{height:46px;min-height:38px;padding:11px 16px;font-size:18px;line-height:1.3333333}.has-feedback{position:relative}.has-feedback .form-control{padding-right:42.5px}.form-control-feedback{position:absolute;top:0;right:0;z-index:2;display:block;width:34px;height:34px;line-height:34px;text-align:center;pointer-events:none}.input-lg+.form-control-feedback,.input-group-lg+.form-control-feedback,.form-group-lg .form-control+.form-control-feedback{width:46px;height:46px;line-height:46px}.input-sm+.form-control-feedback,.input-group-sm+.form-control-feedback,.form-group-sm .form-control+.form-control-feedback{width:30px;height:30px;line-height:30px}.has-success .help-block,.has-success .control-label,.has-success .radio,.has-success .checkbox,.has-success .radio-inline,.has-success .checkbox-inline,.has-success.radio label,.has-success.checkbox label,.has-success.radio-inline label,.has-success.checkbox-inline label{color:#3c763d}.has-success .form-control{border-color:#3c763d;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);box-shadow:inset 0 1px 1px rgba(0,0,0,0.075)}.has-success .form-control:focus{border-color:#2b542c;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 6px #67b168;box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 6px #67b168}.has-success .input-group-addon{color:#3c763d;background-color:#dff0d8;border-color:#3c763d}.has-success .form-control-feedback{color:#3c763d}.has-warning .help-block,.has-warning .control-label,.has-warning .radio,.has-warning .checkbox,.has-warning .radio-inline,.has-warning .checkbox-inline,.has-warning.radio label,.has-warning.checkbox label,.has-warning.radio-inline label,.has-warning.checkbox-inline label{color:#8a6d3b}.has-warning .form-control{border-color:#8a6d3b;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);box-shadow:inset 0 1px 1px rgba(0,0,0,0.075)}.has-warning .form-control:focus{border-color:#66512c;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 6px #c0a16b;box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 6px #c0a16b}.has-warning .input-group-addon{color:#8a6d3b;background-color:#fcf8e3;border-color:#8a6d3b}.has-warning .form-control-feedback{color:#8a6d3b}.has-error .help-block,.has-error .control-label,.has-error .radio,.has-error .checkbox,.has-error .radio-inline,.has-error .checkbox-inline,.has-error.radio label,.has-error.checkbox label,.has-error.radio-inline label,.has-error.checkbox-inline label{color:#a94442}.has-error .form-control{border-color:#a94442;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);box-shadow:inset 0 1px 1px rgba(0,0,0,0.075)}.has-error .form-control:focus{border-color:#843534;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 6px #ce8483;box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 6px #ce8483}.has-error .input-group-addon{color:#a94442;background-color:#f2dede;border-color:#a94442}.has-error .form-control-feedback{color:#a94442}.has-feedback label~.form-control-feedback{top:25px}.has-feedback label.sr-only~.form-control-feedback{top:0}.help-block{display:block;margin-top:5px;margin-bottom:10px;color:#737373}@media (min-width:768px){.form-inline .form-group{display:inline-block;margin-bottom:0;vertical-align:middle}.form-inline .form-control{display:inline-block;width:auto;vertical-align:middle}.form-inline .form-control-static{display:inline-block}.form-inline .input-group{display:inline-table;vertical-align:middle}.form-inline .input-group .input-group-addon,.form-inline .input-group .input-group-btn,.form-inline .input-group .form-control{width:auto}.form-inline .input-group>.form-control{width:100%}.form-inline .control-label{margin-bottom:0;vertical-align:middle}.form-inline .radio,.form-inline .checkbox{display:inline-block;margin-top:0;margin-bottom:0;vertical-align:middle}.form-inline .radio label,.form-inline .checkbox label{padding-left:0}.form-inline .radio input[type="radio"],.form-inline .checkbox input[type="checkbox"]{position:relative;margin-left:0}.form-inline .has-feedback .form-control-feedback{top:0}}.form-horizontal .radio,.form-horizontal .checkbox,.form-horizontal .radio-inline,.form-horizontal .checkbox-inline{padding-top:7px;margin-top:0;margin-bottom:0}.form-horizontal .radio,.form-horizontal .checkbox{min-height:27px}.form-horizontal .form-group{margin-right:-15px;margin-left:-15px}@media (min-width:768px){.form-horizontal .control-label{padding-top:7px;margin-bottom:0;text-align:right}}.form-horizontal .has-feedback .form-control-feedback{right:15px}@media (min-width:768px){.form-horizontal .form-group-lg .control-label{padding-top:11px;font-size:18px}}@media (min-width:768px){.form-horizontal .form-group-sm .control-label{padding-top:6px;font-size:12px}}.btn{display:inline-block;margin-bottom:0;font-weight:400;text-align:center;white-space:nowrap;vertical-align:middle;-ms-touch-action:manipulation;touch-action:manipulation;cursor:pointer;background-image:none;border:1px solid transparent;padding:6px 12px;font-size:14px;line-height:1.42857143;border-radius:4px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.btn:focus,.btn:active:focus,.btn.active:focus,.btn.focus,.btn:active.focus,.btn.active.focus{outline:5px auto -webkit-focus-ring-color;outline-offset:-2px}.btn:hover,.btn:focus,.btn.focus{color:#333;text-decoration:none}.btn:active,.btn.active{background-image:none;outline:0;-webkit-box-shadow:inset 0 3px 5px rgba(0,0,0,0.125);box-shadow:inset 0 3px 5px rgba(0,0,0,0.125)}.btn.disabled,.btn[disabled],fieldset[disabled] .btn{cursor:not-allowed;filter:alpha(opacity=65);opacity:.65;-webkit-box-shadow:none;box-shadow:none}a.btn.disabled,fieldset[disabled] a.btn{pointer-events:none}.btn-default{color:#333;background-color:#fff;border-color:#ccc}.btn-default:focus,.btn-default.focus{color:#333;background-color:#e6e6e6;border-color:#8c8c8c}.btn-default:hover{color:#333;background-color:#e6e6e6;border-color:#adadad}.btn-default:active,.btn-default.active,.open>.dropdown-toggle.btn-default{color:#333;background-color:#e6e6e6;background-image:none;border-color:#adadad}.btn-default:active:hover,.btn-default.active:hover,.open>.dropdown-toggle.btn-default:hover,.btn-default:active:focus,.btn-default.active:focus,.open>.dropdown-toggle.btn-default:focus,.btn-default:active.focus,.btn-default.active.focus,.open>.dropdown-toggle.btn-default.focus{color:#333;background-color:#d4d4d4;border-color:#8c8c8c}.btn-default.disabled:hover,.btn-default[disabled]:hover,fieldset[disabled] .btn-default:hover,.btn-default.disabled:focus,.btn-default[disabled]:focus,fieldset[disabled] .btn-default:focus,.btn-default.disabled.focus,.btn-default[disabled].focus,fieldset[disabled] .btn-default.focus{background-color:#fff;border-color:#ccc}.btn-default .badge{color:#fff;background-color:#333}.btn-primary{color:#fff;background-color:#337ab7;border-color:#2e6da4}.btn-primary:focus,.btn-primary.focus{color:#fff;background-color:#286090;border-color:#122b40}.btn-primary:hover{color:#fff;background-color:#286090;border-color:#204d74}.btn-primary:active,.btn-primary.active,.open>.dropdown-toggle.btn-primary{color:#fff;background-color:#286090;background-image:none;border-color:#204d74}.btn-primary:active:hover,.btn-primary.active:hover,.open>.dropdown-toggle.btn-primary:hover,.btn-primary:active:focus,.btn-primary.active:focus,.open>.dropdown-toggle.btn-primary:focus,.btn-primary:active.focus,.btn-primary.active.focus,.open>.dropdown-toggle.btn-primary.focus{color:#fff;background-color:#204d74;border-color:#122b40}.btn-primary.disabled:hover,.btn-primary[disabled]:hover,fieldset[disabled] .btn-primary:hover,.btn-primary.disabled:focus,.btn-primary[disabled]:focus,fieldset[disabled] .btn-primary:focus,.btn-primary.disabled.focus,.btn-primary[disabled].focus,fieldset[disabled] .btn-primary.focus{background-color:#337ab7;border-color:#2e6da4}.btn-primary .badge{color:#337ab7;background-color:#fff}.btn-success{color:#fff;background-color:#5cb85c;border-color:#4cae4c}.btn-success:focus,.btn-success.focus{color:#fff;background-color:#449d44;border-color:#255625}.btn-success:hover{color:#fff;background-color:#449d44;border-color:#398439}.btn-success:active,.btn-success.active,.open>.dropdown-toggle.btn-success{color:#fff;background-color:#449d44;background-image:none;border-color:#398439}.btn-success:active:hover,.btn-success.active:hover,.open>.dropdown-toggle.btn-success:hover,.btn-success:active:focus,.btn-success.active:focus,.open>.dropdown-toggle.btn-success:focus,.btn-success:active.focus,.btn-success.active.focus,.open>.dropdown-toggle.btn-success.focus{color:#fff;background-color:#398439;border-color:#255625}.btn-success.disabled:hover,.btn-success[disabled]:hover,fieldset[disabled] .btn-success:hover,.btn-success.disabled:focus,.btn-success[disabled]:focus,fieldset[disabled] .btn-success:focus,.btn-success.disabled.focus,.btn-success[disabled].focus,fieldset[disabled] .btn-success.focus{background-color:#5cb85c;border-color:#4cae4c}.btn-success .badge{color:#5cb85c;background-color:#fff}.btn-info{color:#fff;background-color:#5bc0de;border-color:#46b8da}.btn-info:focus,.btn-info.focus{color:#fff;background-color:#31b0d5;border-color:#1b6d85}.btn-info:hover{color:#fff;background-color:#31b0d5;border-color:#269abc}.btn-info:active,.btn-info.active,.open>.dropdown-toggle.btn-info{color:#fff;background-color:#31b0d5;background-image:none;border-color:#269abc}.btn-info:active:hover,.btn-info.active:hover,.open>.dropdown-toggle.btn-info:hover,.btn-info:active:focus,.btn-info.active:focus,.open>.dropdown-toggle.btn-info:focus,.btn-info:active.focus,.btn-info.active.focus,.open>.dropdown-toggle.btn-info.focus{color:#fff;background-color:#269abc;border-color:#1b6d85}.btn-info.disabled:hover,.btn-info[disabled]:hover,fieldset[disabled] .btn-info:hover,.btn-info.disabled:focus,.btn-info[disabled]:focus,fieldset[disabled] .btn-info:focus,.btn-info.disabled.focus,.btn-info[disabled].focus,fieldset[disabled] .btn-info.focus{background-color:#5bc0de;border-color:#46b8da}.btn-info .badge{color:#5bc0de;background-color:#fff}.btn-warning{color:#fff;background-color:#f0ad4e;border-color:#eea236}.btn-warning:focus,.btn-warning.focus{color:#fff;background-color:#ec971f;border-color:#985f0d}.btn-warning:hover{color:#fff;background-color:#ec971f;border-color:#d58512}.btn-warning:active,.btn-warning.active,.open>.dropdown-toggle.btn-warning{color:#fff;background-color:#ec971f;background-image:none;border-color:#d58512}.btn-warning:active:hover,.btn-warning.active:hover,.open>.dropdown-toggle.btn-warning:hover,.btn-warning:active:focus,.btn-warning.active:focus,.open>.dropdown-toggle.btn-warning:focus,.btn-warning:active.focus,.btn-warning.active.focus,.open>.dropdown-toggle.btn-warning.focus{color:#fff;background-color:#d58512;border-color:#985f0d}.btn-warning.disabled:hover,.btn-warning[disabled]:hover,fieldset[disabled] .btn-warning:hover,.btn-warning.disabled:focus,.btn-warning[disabled]:focus,fieldset[disabled] .btn-warning:focus,.btn-warning.disabled.focus,.btn-warning[disabled].focus,fieldset[disabled] .btn-warning.focus{background-color:#f0ad4e;border-color:#eea236}.btn-warning .badge{color:#f0ad4e;background-color:#fff}.btn-danger{color:#fff;background-color:#d9534f;border-color:#d43f3a}.btn-danger:focus,.btn-danger.focus{color:#fff;background-color:#c9302c;border-color:#761c19}.btn-danger:hover{color:#fff;background-color:#c9302c;border-color:#ac2925}.btn-danger:active,.btn-danger.active,.open>.dropdown-toggle.btn-danger{color:#fff;background-color:#c9302c;background-image:none;border-color:#ac2925}.btn-danger:active:hover,.btn-danger.active:hover,.open>.dropdown-toggle.btn-danger:hover,.btn-danger:active:focus,.btn-danger.active:focus,.open>.dropdown-toggle.btn-danger:focus,.btn-danger:active.focus,.btn-danger.active.focus,.open>.dropdown-toggle.btn-danger.focus{color:#fff;background-color:#ac2925;border-color:#761c19}.btn-danger.disabled:hover,.btn-danger[disabled]:hover,fieldset[disabled] .btn-danger:hover,.btn-danger.disabled:focus,.btn-danger[disabled]:focus,fieldset[disabled] .btn-danger:focus,.btn-danger.disabled.focus,.btn-danger[disabled].focus,fieldset[disabled] .btn-danger.focus{background-color:#d9534f;border-color:#d43f3a}.btn-danger .badge{color:#d9534f;background-color:#fff}.btn-link{font-weight:400;color:#337ab7;border-radius:0}.btn-link,.btn-link:active,.btn-link.active,.btn-link[disabled],fieldset[disabled] .btn-link{background-color:transparent;-webkit-box-shadow:none;box-shadow:none}.btn-link,.btn-link:hover,.btn-link:focus,.btn-link:active{border-color:transparent}.btn-link:hover,.btn-link:focus{color:#23527c;text-decoration:underline;background-color:transparent}.btn-link[disabled]:hover,fieldset[disabled] .btn-link:hover,.btn-link[disabled]:focus,fieldset[disabled] .btn-link:focus{color:#777;text-decoration:none}.btn-lg,.btn-group-lg>.btn{padding:10px 16px;font-size:18px;line-height:1.3333333;border-radius:6px}.btn-sm,.btn-group-sm>.btn{padding:5px 10px;font-size:12px;line-height:1.5;border-radius:3px}.btn-xs,.btn-group-xs>.btn{padding:1px 5px;font-size:12px;line-height:1.5;border-radius:3px}.btn-block{display:block;width:100%}.btn-block+.btn-block{margin-top:5px}input[type="submit"].btn-block,input[type="reset"].btn-block,input[type="button"].btn-block{width:100%}.btn-group,.btn-group-vertical{position:relative;display:inline-block;vertical-align:middle}.btn-group>.btn,.btn-group-vertical>.btn{position:relative;float:left}.btn-group>.btn:hover,.btn-group-vertical>.btn:hover,.btn-group>.btn:focus,.btn-group-vertical>.btn:focus,.btn-group>.btn:active,.btn-group-vertical>.btn:active,.btn-group>.btn.active,.btn-group-vertical>.btn.active{z-index:2}.btn-group .btn+.btn,.btn-group .btn+.btn-group,.btn-group .btn-group+.btn,.btn-group .btn-group+.btn-group{margin-left:-1px}.btn-toolbar{margin-left:-5px}.btn-toolbar .btn,.btn-toolbar .btn-group,.btn-toolbar .input-group{float:left}.btn-toolbar>.btn,.btn-toolbar>.btn-group,.btn-toolbar>.input-group{margin-left:5px}.btn-group>.btn:not(:first-child):not(:last-child):not(.dropdown-toggle){border-radius:0}.btn-group>.btn:first-child{margin-left:0}.btn-group>.btn:first-child:not(:last-child):not(.dropdown-toggle){border-top-right-radius:0;border-bottom-right-radius:0}.btn-group>.btn:last-child:not(:first-child),.btn-group>.dropdown-toggle:not(:first-child){border-top-left-radius:0;border-bottom-left-radius:0}.btn-group>.btn-group{float:left}.btn-group>.btn-group:not(:first-child):not(:last-child)>.btn{border-radius:0}.btn-group>.btn-group:first-child:not(:last-child)>.btn:last-child,.btn-group>.btn-group:first-child:not(:last-child)>.dropdown-toggle{border-top-right-radius:0;border-bottom-right-radius:0}.btn-group>.btn-group:last-child:not(:first-child)>.btn:first-child{border-top-left-radius:0;border-bottom-left-radius:0}.btn-group .dropdown-toggle:active,.btn-group.open .dropdown-toggle{outline:0}.btn-group>.btn+.dropdown-toggle{padding-right:8px;padding-left:8px}.btn-group>.btn-lg+.dropdown-toggle{padding-right:12px;padding-left:12px}.btn-group.open .dropdown-toggle{-webkit-box-shadow:inset 0 3px 5px rgba(0,0,0,0.125);box-shadow:inset 0 3px 5px rgba(0,0,0,0.125)}.btn-group.open .dropdown-toggle.btn-link{-webkit-box-shadow:none;box-shadow:none}.btn .caret{margin-left:0}.btn-lg .caret{border-width:5px 5px 0;border-bottom-width:0}.dropup .btn-lg .caret{border-width:0 5px 5px}.btn-group-vertical>.btn,.btn-group-vertical>.btn-group,.btn-group-vertical>.btn-group>.btn{display:block;float:none;width:100%;max-width:100%}.btn-group-vertical>.btn-group>.btn{float:none}.btn-group-vertical>.btn+.btn,.btn-group-vertical>.btn+.btn-group,.btn-group-vertical>.btn-group+.btn,.btn-group-vertical>.btn-group+.btn-group{margin-top:-1px;margin-left:0}.btn-group-vertical>.btn:not(:first-child):not(:last-child){border-radius:0}.btn-group-vertical>.btn:first-child:not(:last-child){border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}.btn-group-vertical>.btn:last-child:not(:first-child){border-top-left-radius:0;border-top-right-radius:0;border-bottom-right-radius:4px;border-bottom-left-radius:4px}.btn-group-vertical>.btn-group:not(:first-child):not(:last-child)>.btn{border-radius:0}.btn-group-vertical>.btn-group:first-child:not(:last-child)>.btn:last-child,.btn-group-vertical>.btn-group:first-child:not(:last-child)>.dropdown-toggle{border-bottom-right-radius:0;border-bottom-left-radius:0}.btn-group-vertical>.btn-group:last-child:not(:first-child)>.btn:first-child{border-top-left-radius:0;border-top-right-radius:0}.btn-group-justified{display:table;width:100%;table-layout:fixed;border-collapse:separate}.btn-group-justified>.btn,.btn-group-justified>.btn-group{display:table-cell;float:none;width:1%}.btn-group-justified>.btn-group .btn{width:100%}.btn-group-justified>.btn-group .dropdown-menu{left:auto}[data-toggle="buttons"]>.btn input[type="radio"],[data-toggle="buttons"]>.btn-group>.btn input[type="radio"],[data-toggle="buttons"]>.btn input[type="checkbox"],[data-toggle="buttons"]>.btn-group>.btn input[type="checkbox"]{position:absolute;clip:rect(0,0,0,0);pointer-events:none}.input-group{position:relative;display:table;border-collapse:separate}.input-group[class*="col-"]{float:none;padding-right:0;padding-left:0}.input-group .form-control{position:relative;z-index:2;float:left;width:100%;margin-bottom:0}.input-group .form-control:focus{z-index:3}.input-group-lg>.form-control,.input-group-lg>.input-group-addon,.input-group-lg>.input-group-btn>.btn{height:46px;padding:10px 16px;font-size:18px;line-height:1.3333333;border-radius:6px}select.input-group-lg>.form-control,select.input-group-lg>.input-group-addon,select.input-group-lg>.input-group-btn>.btn{height:46px;line-height:46px}textarea.input-group-lg>.form-control,textarea.input-group-lg>.input-group-addon,textarea.input-group-lg>.input-group-btn>.btn,select[multiple].input-group-lg>.form-control,select[multiple].input-group-lg>.input-group-addon,select[multiple].input-group-lg>.input-group-btn>.btn{height:auto}.input-group-sm>.form-control,.input-group-sm>.input-group-addon,.input-group-sm>.input-group-btn>.btn{height:30px;padding:5px 10px;font-size:12px;line-height:1.5;border-radius:3px}select.input-group-sm>.form-control,select.input-group-sm>.input-group-addon,select.input-group-sm>.input-group-btn>.btn{height:30px;line-height:30px}textarea.input-group-sm>.form-control,textarea.input-group-sm>.input-group-addon,textarea.input-group-sm>.input-group-btn>.btn,select[multiple].input-group-sm>.form-control,select[multiple].input-group-sm>.input-group-addon,select[multiple].input-group-sm>.input-group-btn>.btn{height:auto}.input-group-addon,.input-group-btn,.input-group .form-control{display:table-cell}.input-group-addon:not(:first-child):not(:last-child),.input-group-btn:not(:first-child):not(:last-child),.input-group .form-control:not(:first-child):not(:last-child){border-radius:0}.input-group-addon,.input-group-btn{width:1%;white-space:nowrap;vertical-align:middle}.input-group-addon{padding:6px 12px;font-size:14px;font-weight:400;line-height:1;color:#555;text-align:center;background-color:#eee;border:1px solid #ccc;border-radius:4px}.input-group-addon.input-sm{padding:5px 10px;font-size:12px;border-radius:3px}.input-group-addon.input-lg{padding:10px 16px;font-size:18px;border-radius:6px}.input-group-addon input[type="radio"],.input-group-addon input[type="checkbox"]{margin-top:0}.input-group .form-control:first-child,.input-group-addon:first-child,.input-group-btn:first-child>.btn,.input-group-btn:first-child>.btn-group>.btn,.input-group-btn:first-child>.dropdown-toggle,.input-group-btn:last-child>.btn:not(:last-child):not(.dropdown-toggle),.input-group-btn:last-child>.btn-group:not(:last-child)>.btn{border-top-right-radius:0;border-bottom-right-radius:0}.input-group-addon:first-child{border-right:0}.input-group .form-control:last-child,.input-group-addon:last-child,.input-group-btn:last-child>.btn,.input-group-btn:last-child>.btn-group>.btn,.input-group-btn:last-child>.dropdown-toggle,.input-group-btn:first-child>.btn:not(:first-child),.input-group-btn:first-child>.btn-group:not(:first-child)>.btn{border-top-left-radius:0;border-bottom-left-radius:0}.input-group-addon:last-child{border-left:0}.input-group-btn{position:relative;font-size:0;white-space:nowrap}.input-group-btn>.btn{position:relative}.input-group-btn>.btn+.btn{margin-left:-1px}.input-group-btn>.btn:hover,.input-group-btn>.btn:focus,.input-group-btn>.btn:active{z-index:2}.input-group-btn:first-child>.btn,.input-group-btn:first-child>.btn-group{margin-right:-1px}.input-group-btn:last-child>.btn,.input-group-btn:last-child>.btn-group{z-index:2;margin-left:-1px}.nav{padding-left:0;margin-bottom:0;list-style:none}.nav>li{position:relative;display:block}.nav>li>a{position:relative;display:block;padding:10px 15px}.nav>li>a:hover,.nav>li>a:focus{text-decoration:none;background-color:#eee}.nav>li.disabled>a{color:#777}.nav>li.disabled>a:hover,.nav>li.disabled>a:focus{color:#777;text-decoration:none;cursor:not-allowed;background-color:transparent}.nav .open>a,.nav .open>a:hover,.nav .open>a:focus{background-color:#eee;border-color:#337ab7}.nav .nav-divider{height:1px;margin:9px 0;overflow:hidden;background-color:#e5e5e5}.nav>li>a>img{max-width:none}.nav-tabs{border-bottom:1px solid #ddd}.nav-tabs>li{float:left;margin-bottom:-1px}.nav-tabs>li>a{margin-right:2px;line-height:1.42857143;border:1px solid transparent;border-radius:4px 4px 0 0}.nav-tabs>li>a:hover{border-color:#eee #eee #ddd}.nav-tabs>li.active>a,.nav-tabs>li.active>a:hover,.nav-tabs>li.active>a:focus{color:#555;cursor:default;background-color:#fff;border:1px solid #ddd;border-bottom-color:transparent}.nav-tabs.nav-justified{width:100%;border-bottom:0}.nav-tabs.nav-justified>li{float:none}.nav-tabs.nav-justified>li>a{margin-bottom:5px;text-align:center}.nav-tabs.nav-justified>.dropdown .dropdown-menu{top:auto;left:auto}@media (min-width:768px){.nav-tabs.nav-justified>li{display:table-cell;width:1%}.nav-tabs.nav-justified>li>a{margin-bottom:0}}.nav-tabs.nav-justified>li>a{margin-right:0;border-radius:4px}.nav-tabs.nav-justified>.active>a,.nav-tabs.nav-justified>.active>a:hover,.nav-tabs.nav-justified>.active>a:focus{border:1px solid #ddd}@media (min-width:768px){.nav-tabs.nav-justified>li>a{border-bottom:1px solid #ddd;border-radius:4px 4px 0 0}.nav-tabs.nav-justified>.active>a,.nav-tabs.nav-justified>.active>a:hover,.nav-tabs.nav-justified>.active>a:focus{border-bottom-color:#fff}}.nav-pills>li{float:left}.nav-pills>li>a{border-radius:4px}.nav-pills>li+li{margin-left:2px}.nav-pills>li.active>a,.nav-pills>li.active>a:hover,.nav-pills>li.active>a:focus{color:#fff;background-color:#337ab7}.nav-stacked>li{float:none}.nav-stacked>li+li{margin-top:2px;margin-left:0}.nav-justified{width:100%}.nav-justified>li{float:none}.nav-justified>li>a{margin-bottom:5px;text-align:center}.nav-justified>.dropdown .dropdown-menu{top:auto;left:auto}@media (min-width:768px){.nav-justified>li{display:table-cell;width:1%}.nav-justified>li>a{margin-bottom:0}}.nav-tabs-justified{border-bottom:0}.nav-tabs-justified>li>a{margin-right:0;border-radius:4px}.nav-tabs-justified>.active>a,.nav-tabs-justified>.active>a:hover,.nav-tabs-justified>.active>a:focus{border:1px solid #ddd}@media (min-width:768px){.nav-tabs-justified>li>a{border-bottom:1px solid #ddd;border-radius:4px 4px 0 0}.nav-tabs-justified>.active>a,.nav-tabs-justified>.active>a:hover,.nav-tabs-justified>.active>a:focus{border-bottom-color:#fff}}.tab-content>.tab-pane{display:none}.tab-content>.active{display:block}.nav-tabs .dropdown-menu{margin-top:-1px;border-top-left-radius:0;border-top-right-radius:0}.label{display:inline;padding:.2em .6em .3em;font-size:75%;font-weight:700;line-height:1;color:#fff;text-align:center;white-space:nowrap;vertical-align:baseline;border-radius:.25em}a.label:hover,a.label:focus{color:#fff;text-decoration:none;cursor:pointer}.label:empty{display:none}.btn .label{position:relative;top:-1px}.label-default{background-color:#777}.label-default[href]:hover,.label-default[href]:focus{background-color:#5e5e5e}.label-primary{background-color:#337ab7}.label-primary[href]:hover,.label-primary[href]:focus{background-color:#286090}.label-success{background-color:#5cb85c}.label-success[href]:hover,.label-success[href]:focus{background-color:#449d44}.label-info{background-color:#5bc0de}.label-info[href]:hover,.label-info[href]:focus{background-color:#31b0d5}.label-warning{background-color:#f0ad4e}.label-warning[href]:hover,.label-warning[href]:focus{background-color:#ec971f}.label-danger{background-color:#d9534f}.label-danger[href]:hover,.label-danger[href]:focus{background-color:#c9302c}.badge{display:inline-block;min-width:10px;padding:3px 7px;font-size:12px;font-weight:700;line-height:1;color:#fff;text-align:center;white-space:nowrap;vertical-align:middle;background-color:#777;border-radius:10px}.badge:empty{display:none}.btn .badge{position:relative;top:-1px}.btn-xs .badge,.btn-group-xs>.btn .badge{top:0;padding:1px 5px}a.badge:hover,a.badge:focus{color:#fff;text-decoration:none;cursor:pointer}.list-group-item.active>.badge,.nav-pills>.active>a>.badge{color:#337ab7;background-color:#fff}.list-group-item>.badge{float:right}.list-group-item>.badge+.badge{margin-right:5px}.nav-pills>li>a>.badge{margin-left:3px}.panel{margin-bottom:20px;background-color:#fff;border:1px solid transparent;border-radius:4px;-webkit-box-shadow:0 1px 1px rgba(0,0,0,0.05);box-shadow:0 1px 1px rgba(0,0,0,0.05)}.panel-body{padding:15px}.panel-heading{padding:10px 15px;border-bottom:1px solid transparent;border-top-left-radius:3px;border-top-right-radius:3px}.panel-heading>.dropdown .dropdown-toggle{color:inherit}.panel-title{margin-top:0;margin-bottom:0;font-size:16px;color:inherit}.panel-title>a,.panel-title>small,.panel-title>.small,.panel-title>small>a,.panel-title>.small>a{color:inherit}.panel-footer{padding:10px 15px;background-color:#f5f5f5;border-top:1px solid #ddd;border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.list-group,.panel>.panel-collapse>.list-group{margin-bottom:0}.panel>.list-group .list-group-item,.panel>.panel-collapse>.list-group .list-group-item{border-width:1px 0;border-radius:0}.panel>.list-group:first-child .list-group-item:first-child,.panel>.panel-collapse>.list-group:first-child .list-group-item:first-child{border-top:0;border-top-left-radius:3px;border-top-right-radius:3px}.panel>.list-group:last-child .list-group-item:last-child,.panel>.panel-collapse>.list-group:last-child .list-group-item:last-child{border-bottom:0;border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.panel-heading+.panel-collapse>.list-group .list-group-item:first-child{border-top-left-radius:0;border-top-right-radius:0}.panel-heading+.list-group .list-group-item:first-child{border-top-width:0}.list-group+.panel-footer{border-top-width:0}.panel>.table,.panel>.table-responsive>.table,.panel>.panel-collapse>.table{margin-bottom:0}.panel>.table caption,.panel>.table-responsive>.table caption,.panel>.panel-collapse>.table caption{padding-right:15px;padding-left:15px}.panel>.table:first-child,.panel>.table-responsive:first-child>.table:first-child{border-top-left-radius:3px;border-top-right-radius:3px}.panel>.table:first-child>thead:first-child>tr:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child{border-top-left-radius:3px;border-top-right-radius:3px}.panel>.table:first-child>thead:first-child>tr:first-child td:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child td:first-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:first-child,.panel>.table:first-child>thead:first-child>tr:first-child th:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child th:first-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:first-child{border-top-left-radius:3px}.panel>.table:first-child>thead:first-child>tr:first-child td:last-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:last-child,.panel>.table:first-child>tbody:first-child>tr:first-child td:last-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:last-child,.panel>.table:first-child>thead:first-child>tr:first-child th:last-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:last-child,.panel>.table:first-child>tbody:first-child>tr:first-child th:last-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:last-child{border-top-right-radius:3px}.panel>.table:last-child,.panel>.table-responsive:last-child>.table:last-child{border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.table:last-child>tbody:last-child>tr:last-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child{border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.table:last-child>tbody:last-child>tr:last-child td:first-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:first-child,.panel>.table:last-child>tfoot:last-child>tr:last-child td:first-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:first-child,.panel>.table:last-child>tbody:last-child>tr:last-child th:first-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:first-child,.panel>.table:last-child>tfoot:last-child>tr:last-child th:first-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:first-child{border-bottom-left-radius:3px}.panel>.table:last-child>tbody:last-child>tr:last-child td:last-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child td:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:last-child,.panel>.table:last-child>tbody:last-child>tr:last-child th:last-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child th:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:last-child{border-bottom-right-radius:3px}.panel>.panel-body+.table,.panel>.panel-body+.table-responsive,.panel>.table+.panel-body,.panel>.table-responsive+.panel-body{border-top:1px solid #ddd}.panel>.table>tbody:first-child>tr:first-child th,.panel>.table>tbody:first-child>tr:first-child td{border-top:0}.panel>.table-bordered,.panel>.table-responsive>.table-bordered{border:0}.panel>.table-bordered>thead>tr>th:first-child,.panel>.table-responsive>.table-bordered>thead>tr>th:first-child,.panel>.table-bordered>tbody>tr>th:first-child,.panel>.table-responsive>.table-bordered>tbody>tr>th:first-child,.panel>.table-bordered>tfoot>tr>th:first-child,.panel>.table-responsive>.table-bordered>tfoot>tr>th:first-child,.panel>.table-bordered>thead>tr>td:first-child,.panel>.table-responsive>.table-bordered>thead>tr>td:first-child,.panel>.table-bordered>tbody>tr>td:first-child,.panel>.table-responsive>.table-bordered>tbody>tr>td:first-child,.panel>.table-bordered>tfoot>tr>td:first-child,.panel>.table-responsive>.table-bordered>tfoot>tr>td:first-child{border-left:0}.panel>.table-bordered>thead>tr>th:last-child,.panel>.table-responsive>.table-bordered>thead>tr>th:last-child,.panel>.table-bordered>tbody>tr>th:last-child,.panel>.table-responsive>.table-bordered>tbody>tr>th:last-child,.panel>.table-bordered>tfoot>tr>th:last-child,.panel>.table-responsive>.table-bordered>tfoot>tr>th:last-child,.panel>.table-bordered>thead>tr>td:last-child,.panel>.table-responsive>.table-bordered>thead>tr>td:last-child,.panel>.table-bordered>tbody>tr>td:last-child,.panel>.table-responsive>.table-bordered>tbody>tr>td:last-child,.panel>.table-bordered>tfoot>tr>td:last-child,.panel>.table-responsive>.table-bordered>tfoot>tr>td:last-child{border-right:0}.panel>.table-bordered>thead>tr:first-child>td,.panel>.table-responsive>.table-bordered>thead>tr:first-child>td,.panel>.table-bordered>tbody>tr:first-child>td,.panel>.table-responsive>.table-bordered>tbody>tr:first-child>td,.panel>.table-bordered>thead>tr:first-child>th,.panel>.table-responsive>.table-bordered>thead>tr:first-child>th,.panel>.table-bordered>tbody>tr:first-child>th,.panel>.table-responsive>.table-bordered>tbody>tr:first-child>th{border-bottom:0}.panel>.table-bordered>tbody>tr:last-child>td,.panel>.table-responsive>.table-bordered>tbody>tr:last-child>td,.panel>.table-bordered>tfoot>tr:last-child>td,.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>td,.panel>.table-bordered>tbody>tr:last-child>th,.panel>.table-responsive>.table-bordered>tbody>tr:last-child>th,.panel>.table-bordered>tfoot>tr:last-child>th,.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>th{border-bottom:0}.panel>.table-responsive{margin-bottom:0;border:0}.panel-group{margin-bottom:20px}.panel-group .panel{margin-bottom:0;border-radius:4px}.panel-group .panel+.panel{margin-top:5px}.panel-group .panel-heading{border-bottom:0}.panel-group .panel-heading+.panel-collapse>.panel-body,.panel-group .panel-heading+.panel-collapse>.list-group{border-top:1px solid #ddd}.panel-group .panel-footer{border-top:0}.panel-group .panel-footer+.panel-collapse .panel-body{border-bottom:1px solid #ddd}.panel-default{border-color:#ddd}.panel-default>.panel-heading{color:#333;background-color:#f5f5f5;border-color:#ddd}.panel-default>.panel-heading+.panel-collapse>.panel-body{border-top-color:#ddd}.panel-default>.panel-heading .badge{color:#f5f5f5;background-color:#333}.panel-default>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#ddd}.panel-primary{border-color:#337ab7}.panel-primary>.panel-heading{color:#fff;background-color:#337ab7;border-color:#337ab7}.panel-primary>.panel-heading+.panel-collapse>.panel-body{border-top-color:#337ab7}.panel-primary>.panel-heading .badge{color:#337ab7;background-color:#fff}.panel-primary>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#337ab7}.panel-success{border-color:#d6e9c6}.panel-success>.panel-heading{color:#3c763d;background-color:#dff0d8;border-color:#d6e9c6}.panel-success>.panel-heading+.panel-collapse>.panel-body{border-top-color:#d6e9c6}.panel-success>.panel-heading .badge{color:#dff0d8;background-color:#3c763d}.panel-success>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#d6e9c6}.panel-info{border-color:#bce8f1}.panel-info>.panel-heading{color:#31708f;background-color:#d9edf7;border-color:#bce8f1}.panel-info>.panel-heading+.panel-collapse>.panel-body{border-top-color:#bce8f1}.panel-info>.panel-heading .badge{color:#d9edf7;background-color:#31708f}.panel-info>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#bce8f1}.panel-warning{border-color:#faebcc}.panel-warning>.panel-heading{color:#8a6d3b;background-color:#fcf8e3;border-color:#faebcc}.panel-warning>.panel-heading+.panel-collapse>.panel-body{border-top-color:#faebcc}.panel-warning>.panel-heading .badge{color:#fcf8e3;background-color:#8a6d3b}.panel-warning>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#faebcc}.panel-danger{border-color:#ebccd1}.panel-danger>.panel-heading{color:#a94442;background-color:#f2dede;border-color:#ebccd1}.panel-danger>.panel-heading+.panel-collapse>.panel-body{border-top-color:#ebccd1}.panel-danger>.panel-heading .badge{color:#f2dede;background-color:#a94442}.panel-danger>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#ebccd1}.close{float:right;font-size:21px;font-weight:700;line-height:1;color:#000;text-shadow:0 1px 0 #fff;filter:alpha(opacity=20);opacity:.2}.close:hover,.close:focus{color:#000;text-decoration:none;cursor:pointer;filter:alpha(opacity=50);opacity:.5}button.close{padding:0;cursor:pointer;background:transparent;border:0;-webkit-appearance:none;appearance:none}.modal-open{overflow:hidden}.modal{position:fixed;top:0;right:0;bottom:0;left:0;z-index:1050;display:none;overflow:hidden;-webkit-overflow-scrolling:touch;outline:0}.modal.fade .modal-dialog{-webkit-transform:translate(0,-25%);-ms-transform:translate(0,-25%);-o-transform:translate(0,-25%);transform:translate(0,-25%);-webkit-transition:-webkit-transform .3s ease-out;-o-transition:-o-transform .3s ease-out;transition:transform .3s ease-out}.modal.in .modal-dialog{-webkit-transform:translate(0,0);-ms-transform:translate(0,0);-o-transform:translate(0,0);transform:translate(0,0)}.modal-open .modal{overflow-x:hidden;overflow-y:auto}.modal-dialog{position:relative;width:auto;margin:10px}.modal-content{position:relative;background-color:#fff;-webkit-background-clip:padding-box;background-clip:padding-box;border:1px solid #999;border:1px solid rgba(0,0,0,0.2);border-radius:6px;-webkit-box-shadow:0 3px 9px rgba(0,0,0,0.5);box-shadow:0 3px 9px rgba(0,0,0,0.5);outline:0}.modal-backdrop{position:fixed;top:0;right:0;bottom:0;left:0;z-index:1040;background-color:#000}.modal-backdrop.fade{filter:alpha(opacity=0);opacity:0}.modal-backdrop.in{filter:alpha(opacity=50);opacity:.5}.modal-header{padding:15px;border-bottom:1px solid #e5e5e5}.modal-header .close{margin-top:-2px}.modal-title{margin:0;line-height:1.42857143}.modal-body{position:relative;padding:15px}.modal-footer{padding:15px;text-align:right;border-top:1px solid #e5e5e5}.modal-footer .btn+.btn{margin-bottom:0;margin-left:5px}.modal-footer .btn-group .btn+.btn{margin-left:-1px}.modal-footer .btn-block+.btn-block{margin-left:0}.modal-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}@media (min-width:768px){.modal-dialog{width:600px;margin:30px auto}.modal-content{-webkit-box-shadow:0 5px 15px rgba(0,0,0,0.5);box-shadow:0 5px 15px rgba(0,0,0,0.5)}.modal-sm{width:300px}}@media (min-width:992px){.modal-lg{width:900px}}.clearfix:before,.clearfix:after,.form-horizontal .form-group:before,.form-horizontal .form-group:after,.btn-toolbar:before,.btn-toolbar:after,.btn-group-vertical>.btn-group:before,.btn-group-vertical>.btn-group:after,.nav:before,.nav:after,.panel-body:before,.panel-body:after,.modal-header:before,.modal-header:after,.modal-footer:before,.modal-footer:after{display:table;content:" "}.clearfix:after,.form-horizontal .form-group:after,.btn-toolbar:after,.btn-group-vertical>.btn-group:after,.nav:after,.panel-body:after,.modal-header:after,.modal-footer:after{clear:both}.center-block{display:block;margin-right:auto;margin-left:auto}.pull-right{float:right!important}.pull-left{float:left!important}.hide{display:none!important}.show{display:block!important}.invisible{visibility:hidden}.text-hide{font:0/0 a;color:transparent;text-shadow:none;background-color:transparent;border:0}.hidden{display:none!important}.affix{position:fixed}.fa-youtube-play:before{content:"\f16a"}.fa::before{font-size:92%}@font-face{font-family:'FontAwesome';src:url(../fonts/fontawesome-webfont3295.eot?v=4.5.0);src:url(../fonts/fontawesome-webfontd41d.eot?#iefix&v=4.5.0) format("embedded-opentype"),url(../fonts/fontawesome-webfont3295.woff2?v=4.5.0) format("woff2"),url(../fonts/fontawesome-webfont3295.woff?v=4.5.0) format("woff"),url(../fonts/fontawesome-webfont3295.ttf?v=4.5.0) format("truetype"),url(../fonts/fontawesome-webfont3295.svg?v=4.5.0#fontawesomeregular) format("svg");font-weight:400;font-style:normal}.fa{display:inline-block;font:normal normal normal 14px/1 FontAwesome;font-size:inherit;text-rendering:auto;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.fa-lg{font-size:1.33333333em;line-height:.75em;vertical-align:-15%}.fa-2x{font-size:2em}.fa-3x{font-size:3em}.fa-4x{font-size:4em}.fa-5x{font-size:5em}.fa-fw{width:1.28571429em;text-align:center}.fa-ul{padding-left:0;margin-left:2.14285714em;list-style-type:none}.fa-ul>li{position:relative}.fa-li{position:absolute;left:-2.14285714em;width:2.14285714em;top:.14285714em;text-align:center}.fa-li.fa-lg{left:-1.85714286em}.fa-border{padding:.2em .25em .15em;border:solid .08em #eee;border-radius:.1em}.fa-pull-left{float:left}.fa-pull-right{float:right}.fa.fa-pull-left{margin-right:.3em}.fa.fa-pull-right{margin-left:.3em}.pull-right{float:right}.pull-left{float:left}.fa.pull-left{margin-right:.3em}.fa.pull-right{margin-left:.3em}.fa-spin{-webkit-animation:fa-spin 2s infinite linear;animation:fa-spin 2s infinite linear}.fa-pulse{-webkit-animation:fa-spin 1s infinite steps(8);animation:fa-spin 1s infinite steps(8)}@-webkit-keyframes fa-spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}@keyframes fa-spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}.fa-rotate-90{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=1);-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg)}.fa-rotate-180{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=2);-webkit-transform:rotate(180deg);-ms-transform:rotate(180deg);transform:rotate(180deg)}.fa-rotate-270{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=3);-webkit-transform:rotate(270deg);-ms-transform:rotate(270deg);transform:rotate(270deg)}.fa-flip-horizontal{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=0,mirror=1);-webkit-transform:scale(-1,1);-ms-transform:scale(-1,1);transform:scale(-1,1)}.fa-flip-vertical{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=2,mirror=1);-webkit-transform:scale(1,-1);-ms-transform:scale(1,-1);transform:scale(1,-1)}:root .fa-rotate-90,:root .fa-rotate-180,:root .fa-rotate-270,:root .fa-flip-horizontal,:root .fa-flip-vertical{filter:none}.fa-stack{position:relative;display:inline-block;width:2em;height:2em;line-height:2em;vertical-align:middle}.fa-stack-1x,.fa-stack-2x{position:absolute;left:0;width:100%;text-align:center}.fa-stack-1x{line-height:inherit}.fa-stack-2x{font-size:2em}.fa-inverse{color:#fff}.fa-glass:before{content:"\f000"}.fa-music:before{content:"\f001"}.fa-search:before{content:"\f002"}.fa-envelope-o:before{content:"\f003"}.fa-heart:before{content:"\f004"}.fa-star:before{content:"\f005"}.fa-star-o:before{content:"\f006"}.fa-user:before{content:"\f007"}.fa-film:before{content:"\f008"}.fa-th-large:before{content:"\f009"}.fa-th:before{content:"\f00a"}.fa-th-list:before{content:"\f00b"}.fa-check:before{content:"\f00c"}.fa-remove:before,.fa-close:before,.fa-times:before{content:"\f00d"}.fa-search-plus:before{content:"\f00e"}.fa-search-minus:before{content:"\f010"}.fa-power-off:before{content:"\f011"}.fa-signal:before{content:"\f012"}.fa-gear:before,.fa-cog:before{content:"\f013"}.fa-trash-o:before{content:"\f014"}.fa-home:before{content:"\f015"}.fa-file-o:before{content:"\f016"}.fa-clock-o:before{content:"\f017"}.fa-road:before{content:"\f018"}.fa-download:before{content:"\f019"}.fa-arrow-circle-o-down:before{content:"\f01a"}.fa-arrow-circle-o-up:before{content:"\f01b"}.fa-inbox:before{content:"\f01c"}.fa-play-circle-o:before{content:"\f01d"}.fa-rotate-right:before,.fa-repeat:before{content:"\f01e"}.fa-refresh:before{content:"\f021"}.fa-list-alt:before{content:"\f022"}.fa-lock:before{content:"\f023"}.fa-flag:before{content:"\f024"}.fa-headphones:before{content:"\f025"}.fa-volume-off:before{content:"\f026"}.fa-volume-down:before{content:"\f027"}.fa-volume-up:before{content:"\f028"}.fa-qrcode:before{content:"\f029"}.fa-barcode:before{content:"\f02a"}.fa-tag:before{content:"\f02b"}.fa-tags:before{content:"\f02c"}.fa-book:before{content:"\f02d"}.fa-bookmark:before{content:"\f02e"}.fa-print:before{content:"\f02f"}.fa-camera:before{content:"\f030"}.fa-font:before{content:"\f031"}.fa-bold:before{content:"\f032"}.fa-italic:before{content:"\f033"}.fa-text-height:before{content:"\f034"}.fa-text-width:before{content:"\f035"}.fa-align-left:before{content:"\f036"}.fa-align-center:before{content:"\f037"}.fa-align-right:before{content:"\f038"}.fa-align-justify:before{content:"\f039"}.fa-list:before{content:"\f03a"}.fa-dedent:before,.fa-outdent:before{content:"\f03b"}.fa-indent:before{content:"\f03c"}.fa-video-camera:before{content:"\f03d"}.fa-photo:before,.fa-image:before,.fa-picture-o:before{content:"\f03e"}.fa-pencil:before{content:"\f040"}.fa-map-marker:before{content:"\f041"}.fa-adjust:before{content:"\f042"}.fa-tint:before{content:"\f043"}.fa-edit:before,.fa-pencil-square-o:before{content:"\f044"}.fa-share-square-o:before{content:"\f045"}.fa-check-square-o:before{content:"\f046"}.fa-arrows:before{content:"\f047"}.fa-step-backward:before{content:"\f048"}.fa-fast-backward:before{content:"\f049"}.fa-backward:before{content:"\f04a"}.fa-play:before{content:"\f04b"}.fa-pause:before{content:"\f04c"}.fa-stop:before{content:"\f04d"}.fa-forward:before{content:"\f04e"}.fa-fast-forward:before{content:"\f050"}.fa-step-forward:before{content:"\f051"}.fa-eject:before{content:"\f052"}.fa-chevron-left:before{content:"\f053"}.fa-chevron-right:before{content:"\f054"}.fa-plus-circle:before{content:"\f055"}.fa-minus-circle:before{content:"\f056"}.fa-times-circle:before{content:"\f057"}.fa-check-circle:before{content:"\f058"}.fa-question-circle:before{content:"\f059"}.fa-info-circle:before{content:"\f05a"}.fa-crosshairs:before{content:"\f05b"}.fa-times-circle-o:before{content:"\f05c"}.fa-check-circle-o:before{content:"\f05d"}.fa-ban:before{content:"\f05e"}.fa-arrow-left:before{content:"\f060"}.fa-arrow-right:before{content:"\f061"}.fa-arrow-up:before{content:"\f062"}.fa-arrow-down:before{content:"\f063"}.fa-mail-forward:before,.fa-share:before{content:"\f064"}.fa-expand:before{content:"\f065"}.fa-compress:before{content:"\f066"}.fa-plus:before{content:"\f067"}.fa-minus:before{content:"\f068"}.fa-asterisk:before{content:"\f069"}.fa-exclamation-circle:before{content:"\f06a"}.fa-gift:before{content:"\f06b"}.fa-leaf:before{content:"\f06c"}.fa-fire:before{content:"\f06d"}.fa-eye:before{content:"\f06e"}.fa-eye-slash:before{content:"\f070"}.fa-warning:before,.fa-exclamation-triangle:before{content:"\f071"}.fa-plane:before{content:"\f072"}.fa-calendar:before{content:"\f073"}.fa-random:before{content:"\f074"}.fa-comment:before{content:"\f075"}.fa-magnet:before{content:"\f076"}.fa-chevron-up:before{content:"\f077"}.fa-chevron-down:before{content:"\f078"}.fa-retweet:before{content:"\f079"}.fa-shopping-cart:before{content:"\f07a"}.fa-folder:before{content:"\f07b"}.fa-folder-open:before{content:"\f07c"}.fa-arrows-v:before{content:"\f07d"}.fa-arrows-h:before{content:"\f07e"}.fa-bar-chart-o:before,.fa-bar-chart:before{content:"\f080"}.fa-twitter-square:before{content:"\f081"}.fa-facebook-square:before{content:"\f082"}.fa-camera-retro:before{content:"\f083"}.fa-key:before{content:"\f084"}.fa-gears:before,.fa-cogs:before{content:"\f085"}.fa-comments:before{content:"\f086"}.fa-thumbs-o-up:before{content:"\f087"}.fa-thumbs-o-down:before{content:"\f088"}.fa-star-half:before{content:"\f089"}.fa-heart-o:before{content:"\f08a"}.fa-sign-out:before{content:"\f08b"}.fa-linkedin-square:before{content:"\f08c"}.fa-thumb-tack:before{content:"\f08d"}.fa-external-link:before{content:"\f08e"}.fa-sign-in:before{content:"\f090"}.fa-trophy:before{content:"\f091"}.fa-github-square:before{content:"\f092"}.fa-upload:before{content:"\f093"}.fa-lemon-o:before{content:"\f094"}.fa-phone:before{content:"\f095"}.fa-square-o:before{content:"\f096"}.fa-bookmark-o:before{content:"\f097"}.fa-phone-square:before{content:"\f098"}.fa-twitter:before{content:"\f099"}.fa-facebook-f:before,.fa-facebook:before{content:"\f09a"}.fa-github:before{content:"\f09b"}.fa-unlock:before{content:"\f09c"}.fa-credit-card:before{content:"\f09d"}.fa-feed:before,.fa-rss:before{content:"\f09e"}.fa-hdd-o:before{content:"\f0a0"}.fa-bullhorn:before{content:"\f0a1"}.fa-bell:before{content:"\f0f3"}.fa-certificate:before{content:"\f0a3"}.fa-hand-o-right:before{content:"\f0a4"}.fa-hand-o-left:before{content:"\f0a5"}.fa-hand-o-up:before{content:"\f0a6"}.fa-hand-o-down:before{content:"\f0a7"}.fa-arrow-circle-left:before{content:"\f0a8"}.fa-arrow-circle-right:before{content:"\f0a9"}.fa-arrow-circle-up:before{content:"\f0aa"}.fa-arrow-circle-down:before{content:"\f0ab"}.fa-globe:before{content:"\f0ac"}.fa-wrench:before{content:"\f0ad"}.fa-tasks:before{content:"\f0ae"}.fa-filter:before{content:"\f0b0"}.fa-briefcase:before{content:"\f0b1"}.fa-arrows-alt:before{content:"\f0b2"}.fa-group:before,.fa-users:before{content:"\f0c0"}.fa-chain:before,.fa-link:before{content:"\f0c1"}.fa-cloud:before{content:"\f0c2"}.fa-flask:before{content:"\f0c3"}.fa-cut:before,.fa-scissors:before{content:"\f0c4"}.fa-copy:before,.fa-files-o:before{content:"\f0c5"}.fa-paperclip:before{content:"\f0c6"}.fa-save:before,.fa-floppy-o:before{content:"\f0c7"}.fa-square:before{content:"\f0c8"}.fa-navicon:before,.fa-reorder:before,.fa-bars:before{content:"\f0c9"}.fa-list-ul:before{content:"\f0ca"}.fa-list-ol:before{content:"\f0cb"}.fa-strikethrough:before{content:"\f0cc"}.fa-underline:before{content:"\f0cd"}.fa-table:before{content:"\f0ce"}.fa-magic:before{content:"\f0d0"}.fa-truck:before{content:"\f0d1"}.fa-pinterest:before{content:"\f0d2"}.fa-pinterest-square:before{content:"\f0d3"}.fa-google-plus-square:before{content:"\f0d4"}.fa-google-plus:before{content:"\f0d5"}.fa-money:before{content:"\f0d6"}.fa-caret-down:before{content:"\f0d7"}.fa-caret-up:before{content:"\f0d8"}.fa-caret-left:before{content:"\f0d9"}.fa-caret-right:before{content:"\f0da"}.fa-columns:before{content:"\f0db"}.fa-unsorted:before,.fa-sort:before{content:"\f0dc"}.fa-sort-down:before,.fa-sort-desc:before{content:"\f0dd"}.fa-sort-up:before,.fa-sort-asc:before{content:"\f0de"}.fa-envelope:before{content:"\f0e0"}.fa-linkedin:before{content:"\f0e1"}.fa-rotate-left:before,.fa-undo:before{content:"\f0e2"}.fa-legal:before,.fa-gavel:before{content:"\f0e3"}.fa-dashboard:before,.fa-tachometer:before{content:"\f0e4"}.fa-comment-o:before{content:"\f0e5"}.fa-comments-o:before{content:"\f0e6"}.fa-flash:before,.fa-bolt:before{content:"\f0e7"}.fa-sitemap:before{content:"\f0e8"}.fa-umbrella:before{content:"\f0e9"}.fa-paste:before,.fa-clipboard:before{content:"\f0ea"}.fa-lightbulb-o:before{content:"\f0eb"}.fa-exchange:before{content:"\f0ec"}.fa-cloud-download:before{content:"\f0ed"}.fa-cloud-upload:before{content:"\f0ee"}.fa-user-md:before{content:"\f0f0"}.fa-stethoscope:before{content:"\f0f1"}.fa-suitcase:before{content:"\f0f2"}.fa-bell-o:before{content:"\f0a2"}.fa-coffee:before{content:"\f0f4"}.fa-cutlery:before{content:"\f0f5"}.fa-file-text-o:before{content:"\f0f6"}.fa-building-o:before{content:"\f0f7"}.fa-hospital-o:before{content:"\f0f8"}.fa-ambulance:before{content:"\f0f9"}.fa-medkit:before{content:"\f0fa"}.fa-fighter-jet:before{content:"\f0fb"}.fa-beer:before{content:"\f0fc"}.fa-h-square:before{content:"\f0fd"}.fa-plus-square:before{content:"\f0fe"}.fa-angle-double-left:before{content:"\f100"}.fa-angle-double-right:before{content:"\f101"}.fa-angle-double-up:before{content:"\f102"}.fa-angle-double-down:before{content:"\f103"}.fa-angle-left:before{content:"\f104"}.fa-angle-right:before{content:"\f105"}.fa-angle-up:before{content:"\f106"}.fa-angle-down:before{content:"\f107"}.fa-desktop:before{content:"\f108"}.fa-laptop:before{content:"\f109"}.fa-tablet:before{content:"\f10a"}.fa-mobile-phone:before,.fa-mobile:before{content:"\f10b"}.fa-circle-o:before{content:"\f10c"}.fa-quote-left:before{content:"\f10d"}.fa-quote-right:before{content:"\f10e"}.fa-spinner:before{content:"\f110"}.fa-circle:before{content:"\f111"}.fa-mail-reply:before,.fa-reply:before{content:"\f112"}.fa-github-alt:before{content:"\f113"}.fa-folder-o:before{content:"\f114"}.fa-folder-open-o:before{content:"\f115"}.fa-smile-o:before{content:"\f118"}.fa-frown-o:before{content:"\f119"}.fa-meh-o:before{content:"\f11a"}.fa-gamepad:before{content:"\f11b"}.fa-keyboard-o:before{content:"\f11c"}.fa-flag-o:before{content:"\f11d"}.fa-flag-checkered:before{content:"\f11e"}.fa-terminal:before{content:"\f120"}.fa-code:before{content:"\f121"}.fa-mail-reply-all:before,.fa-reply-all:before{content:"\f122"}.fa-star-half-empty:before,.fa-star-half-full:before,.fa-star-half-o:before{content:"\f123"}.fa-location-arrow:before{content:"\f124"}.fa-crop:before{content:"\f125"}.fa-code-fork:before{content:"\f126"}.fa-unlink:before,.fa-chain-broken:before{content:"\f127"}.fa-question:before{content:"\f128"}.fa-info:before{content:"\f129"}.fa-exclamation:before{content:"\f12a"}.fa-superscript:before{content:"\f12b"}.fa-subscript:before{content:"\f12c"}.fa-eraser:before{content:"\f12d"}.fa-puzzle-piece:before{content:"\f12e"}.fa-microphone:before{content:"\f130"}.fa-microphone-slash:before{content:"\f131"}.fa-shield:before{content:"\f132"}.fa-calendar-o:before{content:"\f133"}.fa-fire-extinguisher:before{content:"\f134"}.fa-rocket:before{content:"\f135"}.fa-maxcdn:before{content:"\f136"}.fa-chevron-circle-left:before{content:"\f137"}.fa-chevron-circle-right:before{content:"\f138"}.fa-chevron-circle-up:before{content:"\f139"}.fa-chevron-circle-down:before{content:"\f13a"}.fa-html5:before{content:"\f13b"}.fa-css3:before{content:"\f13c"}.fa-anchor:before{content:"\f13d"}.fa-unlock-alt:before{content:"\f13e"}.fa-bullseye:before{content:"\f140"}.fa-ellipsis-h:before{content:"\f141"}.fa-ellipsis-v:before{content:"\f142"}.fa-rss-square:before{content:"\f143"}.fa-play-circle:before{content:"\f144"}.fa-ticket:before{content:"\f145"}.fa-minus-square:before{content:"\f146"}.fa-minus-square-o:before{content:"\f147"}.fa-level-up:before{content:"\f148"}.fa-level-down:before{content:"\f149"}.fa-check-square:before{content:"\f14a"}.fa-pencil-square:before{content:"\f14b"}.fa-external-link-square:before{content:"\f14c"}.fa-share-square:before{content:"\f14d"}.fa-compass:before{content:"\f14e"}.fa-toggle-down:before,.fa-caret-square-o-down:before{content:"\f150"}.fa-toggle-up:before,.fa-caret-square-o-up:before{content:"\f151"}.fa-toggle-right:before,.fa-caret-square-o-right:before{content:"\f152"}.fa-euro:before,.fa-eur:before{content:"\f153"}.fa-gbp:before{content:"\f154"}.fa-dollar:before,.fa-usd:before{content:"\f155"}.fa-rupee:before,.fa-inr:before{content:"\f156"}.fa-cny:before,.fa-rmb:before,.fa-yen:before,.fa-jpy:before{content:"\f157"}.fa-ruble:before,.fa-rouble:before,.fa-rub:before{content:"\f158"}.fa-won:before,.fa-krw:before{content:"\f159"}.fa-bitcoin:before,.fa-btc:before{content:"\f15a"}.fa-file:before{content:"\f15b"}.fa-file-text:before{content:"\f15c"}.fa-sort-alpha-asc:before{content:"\f15d"}.fa-sort-alpha-desc:before{content:"\f15e"}.fa-sort-amount-asc:before{content:"\f160"}.fa-sort-amount-desc:before{content:"\f161"}.fa-sort-numeric-asc:before{content:"\f162"}.fa-sort-numeric-desc:before{content:"\f163"}.fa-thumbs-up:before{content:"\f164"}.fa-thumbs-down:before{content:"\f165"}.fa-youtube-square:before{content:"\f166"}.fa-youtube:before{content:"\f167"}.fa-xing:before{content:"\f168"}.fa-xing-square:before{content:"\f169"}.fa-youtube-play:before{content:"\f16a"}.fa-dropbox:before{content:"\f16b"}.fa-stack-overflow:before{content:"\f16c"}.fa-instagram:before{content:"\f16d"}.fa-flickr:before{content:"\f16e"}.fa-adn:before{content:"\f170"}.fa-bitbucket:before{content:"\f171"}.fa-bitbucket-square:before{content:"\f172"}.fa-tumblr:before{content:"\f173"}.fa-tumblr-square:before{content:"\f174"}.fa-long-arrow-down:before{content:"\f175"}.fa-long-arrow-up:before{content:"\f176"}.fa-long-arrow-left:before{content:"\f177"}.fa-long-arrow-right:before{content:"\f178"}.fa-apple:before{content:"\f179"}.fa-windows:before{content:"\f17a"}.fa-android:before{content:"\f17b"}.fa-linux:before{content:"\f17c"}.fa-dribbble:before{content:"\f17d"}.fa-skype:before{content:"\f17e"}.fa-foursquare:before{content:"\f180"}.fa-trello:before{content:"\f181"}.fa-female:before{content:"\f182"}.fa-male:before{content:"\f183"}.fa-gittip:before,.fa-gratipay:before{content:"\f184"}.fa-sun-o:before{content:"\f185"}.fa-moon-o:before{content:"\f186"}.fa-archive:before{content:"\f187"}.fa-bug:before{content:"\f188"}.fa-vk:before{content:"\f189"}.fa-weibo:before{content:"\f18a"}.fa-renren:before{content:"\f18b"}.fa-pagelines:before{content:"\f18c"}.fa-stack-exchange:before{content:"\f18d"}.fa-arrow-circle-o-right:before{content:"\f18e"}.fa-arrow-circle-o-left:before{content:"\f190"}.fa-toggle-left:before,.fa-caret-square-o-left:before{content:"\f191"}.fa-dot-circle-o:before{content:"\f192"}.fa-wheelchair:before{content:"\f193"}.fa-vimeo-square:before{content:"\f194"}.fa-turkish-lira:before,.fa-try:before{content:"\f195"}.fa-plus-square-o:before{content:"\f196"}.fa-space-shuttle:before{content:"\f197"}.fa-slack:before{content:"\f198"}.fa-envelope-square:before{content:"\f199"}.fa-wordpress:before{content:"\f19a"}.fa-openid:before{content:"\f19b"}.fa-institution:before,.fa-bank:before,.fa-university:before{content:"\f19c"}.fa-mortar-board:before,.fa-graduation-cap:before{content:"\f19d"}.fa-yahoo:before{content:"\f19e"}.fa-google:before{content:"\f1a0"}.fa-reddit:before{content:"\f1a1"}.fa-reddit-square:before{content:"\f1a2"}.fa-stumbleupon-circle:before{content:"\f1a3"}.fa-stumbleupon:before{content:"\f1a4"}.fa-delicious:before{content:"\f1a5"}.fa-digg:before{content:"\f1a6"}.fa-pied-piper:before{content:"\f1a7"}.fa-pied-piper-alt:before{content:"\f1a8"}.fa-drupal:before{content:"\f1a9"}.fa-joomla:before{content:"\f1aa"}.fa-language:before{content:"\f1ab"}.fa-fax:before{content:"\f1ac"}.fa-building:before{content:"\f1ad"}.fa-child:before{content:"\f1ae"}.fa-paw:before{content:"\f1b0"}.fa-spoon:before{content:"\f1b1"}.fa-cube:before{content:"\f1b2"}.fa-cubes:before{content:"\f1b3"}.fa-behance:before{content:"\f1b4"}.fa-behance-square:before{content:"\f1b5"}.fa-steam:before{content:"\f1b6"}.fa-steam-square:before{content:"\f1b7"}.fa-recycle:before{content:"\f1b8"}.fa-automobile:before,.fa-car:before{content:"\f1b9"}.fa-cab:before,.fa-taxi:before{content:"\f1ba"}.fa-tree:before{content:"\f1bb"}.fa-spotify:before{content:"\f1bc"}.fa-deviantart:before{content:"\f1bd"}.fa-soundcloud:before{content:"\f1be"}.fa-database:before{content:"\f1c0"}.fa-file-pdf-o:before{content:"\f1c1"}.fa-file-word-o:before{content:"\f1c2"}.fa-file-excel-o:before{content:"\f1c3"}.fa-file-powerpoint-o:before{content:"\f1c4"}.fa-file-photo-o:before,.fa-file-picture-o:before,.fa-file-image-o:before{content:"\f1c5"}.fa-file-zip-o:before,.fa-file-archive-o:before{content:"\f1c6"}.fa-file-sound-o:before,.fa-file-audio-o:before{content:"\f1c7"}.fa-file-movie-o:before,.fa-file-video-o:before{content:"\f1c8"}.fa-file-code-o:before{content:"\f1c9"}.fa-vine:before{content:"\f1ca"}.fa-codepen:before{content:"\f1cb"}.fa-jsfiddle:before{content:"\f1cc"}.fa-life-bouy:before,.fa-life-buoy:before,.fa-life-saver:before,.fa-support:before,.fa-life-ring:before{content:"\f1cd"}.fa-circle-o-notch:before{content:"\f1ce"}.fa-ra:before,.fa-rebel:before{content:"\f1d0"}.fa-ge:before,.fa-empire:before{content:"\f1d1"}.fa-git-square:before{content:"\f1d2"}.fa-git:before{content:"\f1d3"}.fa-y-combinator-square:before,.fa-yc-square:before,.fa-hacker-news:before{content:"\f1d4"}.fa-tencent-weibo:before{content:"\f1d5"}.fa-qq:before{content:"\f1d6"}.fa-wechat:before,.fa-weixin:before{content:"\f1d7"}.fa-send:before,.fa-paper-plane:before{content:"\f1d8"}.fa-send-o:before,.fa-paper-plane-o:before{content:"\f1d9"}.fa-history:before{content:"\f1da"}.fa-circle-thin:before{content:"\f1db"}.fa-header:before{content:"\f1dc"}.fa-paragraph:before{content:"\f1dd"}.fa-sliders:before{content:"\f1de"}.fa-share-alt:before{content:"\f1e0"}.fa-share-alt-square:before{content:"\f1e1"}.fa-bomb:before{content:"\f1e2"}.fa-soccer-ball-o:before,.fa-futbol-o:before{content:"\f1e3"}.fa-tty:before{content:"\f1e4"}.fa-binoculars:before{content:"\f1e5"}.fa-plug:before{content:"\f1e6"}.fa-slideshare:before{content:"\f1e7"}.fa-twitch:before{content:"\f1e8"}.fa-yelp:before{content:"\f1e9"}.fa-newspaper-o:before{content:"\f1ea"}.fa-wifi:before{content:"\f1eb"}.fa-calculator:before{content:"\f1ec"}.fa-paypal:before{content:"\f1ed"}.fa-google-wallet:before{content:"\f1ee"}.fa-cc-visa:before{content:"\f1f0"}.fa-cc-mastercard:before{content:"\f1f1"}.fa-cc-discover:before{content:"\f1f2"}.fa-cc-amex:before{content:"\f1f3"}.fa-cc-paypal:before{content:"\f1f4"}.fa-cc-stripe:before{content:"\f1f5"}.fa-bell-slash:before{content:"\f1f6"}.fa-bell-slash-o:before{content:"\f1f7"}.fa-trash:before{content:"\f1f8"}.fa-copyright:before{content:"\f1f9"}.fa-at:before{content:"\f1fa"}.fa-eyedropper:before{content:"\f1fb"}.fa-paint-brush:before{content:"\f1fc"}.fa-birthday-cake:before{content:"\f1fd"}.fa-area-chart:before{content:"\f1fe"}.fa-pie-chart:before{content:"\f200"}.fa-line-chart:before{content:"\f201"}.fa-lastfm:before{content:"\f202"}.fa-lastfm-square:before{content:"\f203"}.fa-toggle-off:before{content:"\f204"}.fa-toggle-on:before{content:"\f205"}.fa-bicycle:before{content:"\f206"}.fa-bus:before{content:"\f207"}.fa-ioxhost:before{content:"\f208"}.fa-angellist:before{content:"\f209"}.fa-cc:before{content:"\f20a"}.fa-shekel:before,.fa-sheqel:before,.fa-ils:before{content:"\f20b"}.fa-meanpath:before{content:"\f20c"}.fa-buysellads:before{content:"\f20d"}.fa-connectdevelop:before{content:"\f20e"}.fa-dashcube:before{content:"\f210"}.fa-forumbee:before{content:"\f211"}.fa-leanpub:before{content:"\f212"}.fa-sellsy:before{content:"\f213"}.fa-shirtsinbulk:before{content:"\f214"}.fa-simplybuilt:before{content:"\f215"}.fa-skyatlas:before{content:"\f216"}.fa-cart-plus:before{content:"\f217"}.fa-cart-arrow-down:before{content:"\f218"}.fa-diamond:before{content:"\f219"}.fa-ship:before{content:"\f21a"}.fa-user-secret:before{content:"\f21b"}.fa-motorcycle:before{content:"\f21c"}.fa-street-view:before{content:"\f21d"}.fa-heartbeat:before{content:"\f21e"}.fa-venus:before{content:"\f221"}.fa-mars:before{content:"\f222"}.fa-mercury:before{content:"\f223"}.fa-intersex:before,.fa-transgender:before{content:"\f224"}.fa-transgender-alt:before{content:"\f225"}.fa-venus-double:before{content:"\f226"}.fa-mars-double:before{content:"\f227"}.fa-venus-mars:before{content:"\f228"}.fa-mars-stroke:before{content:"\f229"}.fa-mars-stroke-v:before{content:"\f22a"}.fa-mars-stroke-h:before{content:"\f22b"}.fa-neuter:before{content:"\f22c"}.fa-genderless:before{content:"\f22d"}.fa-facebook-official:before{content:"\f230"}.fa-pinterest-p:before{content:"\f231"}.fa-whatsapp:before{content:"\f232"}.fa-server:before{content:"\f233"}.fa-user-plus:before{content:"\f234"}.fa-user-times:before{content:"\f235"}.fa-hotel:before,.fa-bed:before{content:"\f236"}.fa-viacoin:before{content:"\f237"}.fa-train:before{content:"\f238"}.fa-subway:before{content:"\f239"}.fa-medium:before{content:"\f23a"}.fa-yc:before,.fa-y-combinator:before{content:"\f23b"}.fa-optin-monster:before{content:"\f23c"}.fa-opencart:before{content:"\f23d"}.fa-expeditedssl:before{content:"\f23e"}.fa-battery-4:before,.fa-battery-full:before{content:"\f240"}.fa-battery-3:before,.fa-battery-three-quarters:before{content:"\f241"}.fa-battery-2:before,.fa-battery-half:before{content:"\f242"}.fa-battery-1:before,.fa-battery-quarter:before{content:"\f243"}.fa-battery-0:before,.fa-battery-empty:before{content:"\f244"}.fa-mouse-pointer:before{content:"\f245"}.fa-i-cursor:before{content:"\f246"}.fa-object-group:before{content:"\f247"}.fa-object-ungroup:before{content:"\f248"}.fa-sticky-note:before{content:"\f249"}.fa-sticky-note-o:before{content:"\f24a"}.fa-cc-jcb:before{content:"\f24b"}.fa-cc-diners-club:before{content:"\f24c"}.fa-clone:before{content:"\f24d"}.fa-balance-scale:before{content:"\f24e"}.fa-hourglass-o:before{content:"\f250"}.fa-hourglass-1:before,.fa-hourglass-start:before{content:"\f251"}.fa-hourglass-2:before,.fa-hourglass-half:before{content:"\f252"}.fa-hourglass-3:before,.fa-hourglass-end:before{content:"\f253"}.fa-hourglass:before{content:"\f254"}.fa-hand-grab-o:before,.fa-hand-rock-o:before{content:"\f255"}.fa-hand-stop-o:before,.fa-hand-paper-o:before{content:"\f256"}.fa-hand-scissors-o:before{content:"\f257"}.fa-hand-lizard-o:before{content:"\f258"}.fa-hand-spock-o:before{content:"\f259"}.fa-hand-pointer-o:before{content:"\f25a"}.fa-hand-peace-o:before{content:"\f25b"}.fa-trademark:before{content:"\f25c"}.fa-registered:before{content:"\f25d"}.fa-creative-commons:before{content:"\f25e"}.fa-gg:before{content:"\f260"}.fa-gg-circle:before{content:"\f261"}.fa-tripadvisor:before{content:"\f262"}.fa-odnoklassniki:before{content:"\f263"}.fa-odnoklassniki-square:before{content:"\f264"}.fa-get-pocket:before{content:"\f265"}.fa-wikipedia-w:before{content:"\f266"}.fa-safari:before{content:"\f267"}.fa-chrome:before{content:"\f268"}.fa-firefox:before{content:"\f269"}.fa-opera:before{content:"\f26a"}.fa-internet-explorer:before{content:"\f26b"}.fa-tv:before,.fa-television:before{content:"\f26c"}.fa-contao:before{content:"\f26d"}.fa-500px:before{content:"\f26e"}.fa-amazon:before{content:"\f270"}.fa-calendar-plus-o:before{content:"\f271"}.fa-calendar-minus-o:before{content:"\f272"}.fa-calendar-times-o:before{content:"\f273"}.fa-calendar-check-o:before{content:"\f274"}.fa-industry:before{content:"\f275"}.fa-map-pin:before{content:"\f276"}.fa-map-signs:before{content:"\f277"}.fa-map-o:before{content:"\f278"}.fa-map:before{content:"\f279"}.fa-commenting:before{content:"\f27a"}.fa-commenting-o:before{content:"\f27b"}.fa-houzz:before{content:"\f27c"}.fa-vimeo:before{content:"\f27d"}.fa-black-tie:before{content:"\f27e"}.fa-fonticons:before{content:"\f280"}.fa-reddit-alien:before{content:"\f281"}.fa-edge:before{content:"\f282"}.fa-credit-card-alt:before{content:"\f283"}.fa-codiepie:before{content:"\f284"}.fa-modx:before{content:"\f285"}.fa-fort-awesome:before{content:"\f286"}.fa-usb:before{content:"\f287"}.fa-product-hunt:before{content:"\f288"}.fa-mixcloud:before{content:"\f289"}.fa-scribd:before{content:"\f28a"}.fa-pause-circle:before{content:"\f28b"}.fa-pause-circle-o:before{content:"\f28c"}.fa-stop-circle:before{content:"\f28d"}.fa-stop-circle-o:before{content:"\f28e"}.fa-shopping-bag:before{content:"\f290"}.fa-shopping-basket:before{content:"\f291"}.fa-hashtag:before{content:"\f292"}.fa-bluetooth:before{content:"\f293"}.fa-bluetooth-b:before{content:"\f294"}.fa-percent:before{content:"\f295"}.column{float:left;padding:6px;min-height:100%}.row:after{content:"";display:table;clear:both}ul.side-menu{text-align:left;margin-left:-4px}.side-menu > li{text-align:left;list-style:none;padding:5px;margin-left:-8px}.paneladmin{position:fixed;width:100%;height:100%;top:0;overflow:auto;left:0;display:none;background:#fff}.cl1{width:25%}.cl2{width:75%}.d2.light.fl.d2bc.filh.break{height:fit-content!important}.side-menu > li > a{background:rgba(0,0,0,0.2);padding:5px;color:#fff;position:absolute;width:63px;cursor:pointer;text-align:center}.side-menu > li > a.active{background:#6495ed;color:#fff;padding:4px}.side-menu > li > a{color:gray;outline:none;text-decoration:none}.side-menu > li > a{color:#000;padding:4px}.cl2{position:absolute;height:100%;margin-left:165px}@media only screen and (max-width: 600px){ul.side-menu{margin-left:-35px}.cl1{width:25%}.cl2{width:75%;margin-left:105px}}@media only screen and (min-width: 880px){.cl1{width:15%}.cl2{width:85%;margin-left:205px}}.notification{min-width:180px;max-width:260px;border:1px solid #000;z-index:2000;background-color:#efefef;position:relative;padding:5px;border-radius:5px;margin:.7rem auto 0}.notification__sender__avatar{border-radius:5px;margin-right:.1em}.notification__sender__name{margin:.2em 0;font-size:.6rem;font-weight:700;padding:.15em 0 0;max-width:80%;color:#000;display:inline-block}.notification p{margin-bottom:.4em}.username__wrapper{margin-top:.01em;padding:2.1px 1px;display:inline-block;white-space:nowrap;overflow:hidden!important;text-overflow:ellipsis;max-width:99%;margin-left:3PX;direction:initial;-webkit-user-select:none;font-size:15PX;margin-bottom:2px}.notification__message{font-size:.6rem;font-weight:700;margin-top:.2em;width:100%;padding:0 .2em;word-break:break-word}.notification__time{position:absolute;top:.2rem;right:.2rem;font-size:.5rem}.notification__title{font-size:.6rem;text-align:center;margin:-14px auto 0;width:50%;border:1px solid #000;border-radius:5px;line-height:1.6}.cc.noflow.nosel.hand.break{width:101%!important;padding:2px}.is_speaking{-webkit-animation:ripple .7s linear infinite;animation:ripple .7s linear infinite}@-webkit-keyframes ripple{0%{box-shadow:0 0 0 0 rgba(0,200,45,0),0 0 0 1px rgba(0,200,45,0.3),0 0 0 2px rgba(0,200,45,0.3),0 0 0 3px rgba(0,200,45,0.3)}to{box-shadow:0 0 0 1px rgba(0,200,45,0.3),0 0 0 4px rgba(0,200,45,0.3),0 0 0 5px rgba(0,200,45,0.3),0 0 0 6px rgba(0,200,45,0)}}@keyframes ripple{0%{box-shadow:0 0 0 0 rgba(255,0,0,0)0 0 0 1px rgba(0,200,45,0.3),0 0 0 2px rgba(0,200,45,0.3),0 0 0 3px rgba(0,200,45,0.3)}to{box-shadow:0 0 0 1px rgba(0,200,45,0.3),0 0 0 4px rgba(0,200,45,0.3),0 0 0 5px rgba(0,200,45,0.3),0 0 0 6px rgba(255,3,3,0)}}.ico_pl{color:#fff;background:#000;padding:4px}table > thead{background:#6495ed;color:#fff}.activepower{border:2px solid #000;background:#ffe4c4}.fa-windows:before,.fa-linux:before,.fa-apple:before,.fa-times-circle:before,.fa-th-large:before{margin-left:16px}.fa-chrome:before,.fa-edge:before,.fa-firefox:before,.fa-internet-explorer:before,.fa-opera:before,.fa-safari:before,.fa-android:before,.fa-scribd:before{margin-left:16px}#StoryProgress{width:100%;background-color:#ddd}#BarStory{width:0;height:3px;background-color:#6495ed;margin-bottom:15px}.desginliste{overflow-x:auto;overflow-y:hidden;display:flex;margin:-2px -36px;padding:2px 0 0 32px;width:112%;height:100px;margin-bottom:14px}.desginliste img{border:2px solid #000;width:100%;height:79px;margin-left:5px}.desginlist{overflow-x:auto;overflow-y:hidden;display:flex;margin:auto;margin-top:-14px;margin-bottom:0!important}.desginlist img{border:2px solid #000;width:100px;height:100px;margin-left:5px}@keyframes show_message_box{0%{transform:translateX(-100%)}40%{transform:translateX(10%)}80%,to{transform:translateX(0.8333333333rem)}}@keyframes hide_message_box{0%{transform:translateX(0.8333333333rem)}40%{transform:translateX(10%)}80%,to{opacity:0;pointer-events:none;transform:translateX(-100%)}}.message-box{animation:show_message_box 2s ease forwards;left:.8333333333rem;position:absolute;top:.8333333333rem;z-index:10000000}.message-box .box.green{border-color:#2ecc71}.message-box .box{height:33px;align-items:center;background:#fff;border-left:5px solid #2ecc71;border-radius:.4166666667rem;box-shadow:.0416666667rem .2916666667rem .5833333333rem -.2083333333rem rgba(0,0,0,0.15);display:flex;justify-content:space-between;padding:.4rem .6rem .4rem .4rem;width:auto}.message-box .box .content,.message-box .content .icon{align-items:center;display:flex}.message-box .box.green .content .icon{background:#2ecc71}.message-box .content .icon{padding:12px!important;background:#2ecc71;border-radius:50%;color:#fff;font-size:1.0416666667rem;height:1rem;justify-content:center;line-height:1rem;text-align:center;width:1rem}.message-box .content .icon .fa{font-size:.6rem;margin-top:.1rem}.message-box .content .details{direction:rtl;margin-left:.3rem;margin-top:13px}.message-box.hiden{animation:hide_message_box 2s ease forwards}.box.orange .icon{background:#df7a22}textarea#roomSearchInput::placeholder{color:#fff}textarea#usearch::placeholder{color:#fff}.box.red{border-color:#d44646}.box.red .icon{background:#d44646;color:#fff}.box.red .icon:before{content:"";position:absolute;top:4px;right:0;left:23px;width:2px;height:23px;background:#c70e0e;transform:rotate(45deg)}.box.orange{border-color:#df7a22}.d-color-container{display:none}.label-info{background-color:#5bc0de}.fl.mini.u-msg.dots{color:#888!important;margin-top:0!important;float:initial!important}div#cooment .popover{left:20px!important;background:#fff;border:1px solid;box-shadow:3px 3px 6px #9b9595}a.comm.btn.minix.btn-danger.fa.fa-comments.fr{background:#<%=colors.btcolor %>!important}.fa-volume-high:before,.fa-volume-up:before{content:"\f028"}.panel{margin-bottom:20px;background-color:#fff;border:1px solid transparent;border-radius:4px;-webkit-box-shadow:0 1px 1px rgba(0,0,0,0.05);box-shadow:0 1px 1px rgba(0,0,0,0.05)}.panel-body{padding:15px}.panel-heading{padding:10px 15px;border-bottom:1px solid transparent;border-top-left-radius:3px;border-top-right-radius:3px}.panel-heading>.dropdown .dropdown-toggle{color:inherit}.panel-title{margin-top:0;margin-bottom:0;font-size:16px;color:inherit}.panel-title>a,.panel-title>small,.panel-title>.small,.panel-title>small>a,.panel-title>.small>a{color:inherit}.panel-footer{padding:10px 15px;background-color:#f5f5f5;border-top:1px solid #ddd;border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.list-group,.panel>.panel-collapse>.list-group{margin-bottom:0}.panel>.list-group .list-group-item,.panel>.panel-collapse>.list-group .list-group-item{border-width:1px 0;border-radius:0}.panel>.list-group:first-child .list-group-item:first-child,.panel>.panel-collapse>.list-group:first-child .list-group-item:first-child{border-top:0;border-top-left-radius:3px;border-top-right-radius:3px}.panel>.list-group:last-child .list-group-item:last-child,.panel>.panel-collapse>.list-group:last-child .list-group-item:last-child{border-bottom:0;border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.panel-heading+.panel-collapse>.list-group .list-group-item:first-child{border-top-left-radius:0;border-top-right-radius:0}.panel-heading+.list-group .list-group-item:first-child{border-top-width:0}.list-group+.panel-footer{border-top-width:0}.panel>.table,.panel>.table-responsive>.table,.panel>.panel-collapse>.table{margin-bottom:0}.panel>.table caption,.panel>.table-responsive>.table caption,.panel>.panel-collapse>.table caption{padding-right:15px;padding-left:15px}.panel>.table:first-child,.panel>.table-responsive:first-child>.table:first-child{border-top-left-radius:3px;border-top-right-radius:3px}.panel>.table:first-child>thead:first-child>tr:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child{border-top-left-radius:3px;border-top-right-radius:3px}.panel>.table:first-child>thead:first-child>tr:first-child td:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child td:first-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:first-child,.panel>.table:first-child>thead:first-child>tr:first-child th:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child th:first-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:first-child{border-top-left-radius:3px}.panel>.table:first-child>thead:first-child>tr:first-child td:last-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:last-child,.panel>.table:first-child>tbody:first-child>tr:first-child td:last-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:last-child,.panel>.table:first-child>thead:first-child>tr:first-child th:last-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:last-child,.panel>.table:first-child>tbody:first-child>tr:first-child th:last-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:last-child{border-top-right-radius:3px}.panel>.table:last-child,.panel>.table-responsive:last-child>.table:last-child{border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.table:last-child>tbody:last-child>tr:last-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child{border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.table:last-child>tbody:last-child>tr:last-child td:first-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:first-child,.panel>.table:last-child>tfoot:last-child>tr:last-child td:first-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:first-child,.panel>.table:last-child>tbody:last-child>tr:last-child th:first-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:first-child,.panel>.table:last-child>tfoot:last-child>tr:last-child th:first-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:first-child{border-bottom-left-radius:3px}.panel>.table:last-child>tbody:last-child>tr:last-child td:last-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child td:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:last-child,.panel>.table:last-child>tbody:last-child>tr:last-child th:last-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child th:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:last-child{border-bottom-right-radius:3px}.panel>.panel-body+.table,.panel>.panel-body+.table-responsive,.panel>.table+.panel-body,.panel>.table-responsive+.panel-body{border-top:1px solid #ddd}.panel>.table>tbody:first-child>tr:first-child th,.panel>.table>tbody:first-child>tr:first-child td{border-top:0}.panel>.table-bordered,.panel>.table-responsive>.table-bordered{border:0}.panel>.table-bordered>thead>tr>th:first-child,.panel>.table-responsive>.table-bordered>thead>tr>th:first-child,.panel>.table-bordered>tbody>tr>th:first-child,.panel>.table-responsive>.table-bordered>tbody>tr>th:first-child,.panel>.table-bordered>tfoot>tr>th:first-child,.panel>.table-responsive>.table-bordered>tfoot>tr>th:first-child,.panel>.table-bordered>thead>tr>td:first-child,.panel>.table-responsive>.table-bordered>thead>tr>td:first-child,.panel>.table-bordered>tbody>tr>td:first-child,.panel>.table-responsive>.table-bordered>tbody>tr>td:first-child,.panel>.table-bordered>tfoot>tr>td:first-child,.panel>.table-responsive>.table-bordered>tfoot>tr>td:first-child{border-left:0}.panel>.table-bordered>thead>tr>th:last-child,.panel>.table-responsive>.table-bordered>thead>tr>th:last-child,.panel>.table-bordered>tbody>tr>th:last-child,.panel>.table-responsive>.table-bordered>tbody>tr>th:last-child,.panel>.table-bordered>tfoot>tr>th:last-child,.panel>.table-responsive>.table-bordered>tfoot>tr>th:last-child,.panel>.table-bordered>thead>tr>td:last-child,.panel>.table-responsive>.table-bordered>thead>tr>td:last-child,.panel>.table-bordered>tbody>tr>td:last-child,.panel>.table-responsive>.table-bordered>tbody>tr>td:last-child,.panel>.table-bordered>tfoot>tr>td:last-child,.panel>.table-responsive>.table-bordered>tfoot>tr>td:last-child{border-right:0}.panel>.table-bordered>thead>tr:first-child>td,.panel>.table-responsive>.table-bordered>thead>tr:first-child>td,.panel>.table-bordered>tbody>tr:first-child>td,.panel>.table-responsive>.table-bordered>tbody>tr:first-child>td,.panel>.table-bordered>thead>tr:first-child>th,.panel>.table-responsive>.table-bordered>thead>tr:first-child>th,.panel>.table-bordered>tbody>tr:first-child>th,.panel>.table-responsive>.table-bordered>tbody>tr:first-child>th{border-bottom:0}.panel>.table-bordered>tbody>tr:last-child>td,.panel>.table-responsive>.table-bordered>tbody>tr:last-child>td,.panel>.table-bordered>tfoot>tr:last-child>td,.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>td,.panel>.table-bordered>tbody>tr:last-child>th,.panel>.table-responsive>.table-bordered>tbody>tr:last-child>th,.panel>.table-bordered>tfoot>tr:last-child>th,.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>th{border-bottom:0}.panel>.table-responsive{margin-bottom:0;border:0}.panel-group{margin-bottom:20px}.panel-group .panel{margin-bottom:0;border-radius:4px}.panel-group .panel+.panel{margin-top:5px}.panel-group .panel-heading{border-bottom:0}.panel-group .panel-heading+.panel-collapse>.panel-body,.panel-group .panel-heading+.panel-collapse>.list-group{border-top:1px solid #ddd}.panel-group .panel-footer{border-top:0}.panel-group .panel-footer+.panel-collapse .panel-body{border-bottom:1px solid #ddd}.panel-default{border-color:#ddd}.fa-volume-high:before,.fa-volume-up:before{content:"\f028"}.style{background:#5cb85c;color:#fff;border:1px solid}@media (max-width: 767px){#dpnl{border:1px solid;max-width:340px;min-width:300px;top:0;right:0;position:fixed;bottom:36px;width:calc(100vw - 104px)}}@media (min-width: 768px){#dpnl{border:1px solid;max-width:340px;min-width:300px;top:0;right:0;position:fixed;bottom:36px;width:340px}}@media (max-width: 767px){.label.label-danger.border.nosel.fa.fa-close.fr{margin-left:260px}}@media (min-width: 768px){.label.label-danger.border.nosel.fa.fa-close.fr{margin-left:300px}}.panel-default>.panel-heading{color:#333;background-color:#f5f5f5;border-color:#ddd}.panel-default>.panel-heading+.panel-collapse>.panel-body{border-top-color:#ddd}.panel-default>.panel-heading .badge{color:#f5f5f5;background-color:#333}.panel-default>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#ddd}.panel-primary{border-color:#337ab7}.panel-primary>.panel-heading{color:#fff;background-color:#337ab7;border-color:#337ab7}.panel-primary>.panel-heading+.panel-collapse>.panel-body{border-top-color:#337ab7}.panel-primary>.panel-heading .badge{color:#337ab7;background-color:#fff}.panel-primary>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#337ab7}.panel-success{border-color:#d6e9c6}.panel-success>.panel-heading{color:#3c763d;background-color:#dff0d8;border-color:#d6e9c6}.panel-success>.panel-heading+.panel-collapse>.panel-body{border-top-color:#d6e9c6}.panel-success>.panel-heading .badge{color:#dff0d8;background-color:#3c763d}.panel-success>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#d6e9c6}.panel-info{border-color:#bce8f1}.panel-info>.panel-heading{color:#31708f;background-color:#d9edf7;border-color:#bce8f1}.panel-info>.panel-heading+.panel-collapse>.panel-body{border-top-color:#bce8f1}.panel-info>.panel-heading .badge{color:#d9edf7;background-color:#31708f}.panel-info>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#bce8f1}.panel-warning{border-color:#faebcc}.panel-warning>.panel-heading{color:#8a6d3b;background-color:#fcf8e3;border-color:#faebcc}.panel-warning>.panel-heading+.panel-collapse>.panel-body{border-top-color:#faebcc}.panel-warning>.panel-heading .badge{color:#fcf8e3;background-color:#8a6d3b}.panel-warning>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#faebcc}.panel-danger{border-color:#ebccd1}.panel-danger>.panel-heading{color:#a94442;background-color:#f2dede;border-color:#ebccd1}.panel-danger>.panel-heading+.panel-collapse>.panel-body{border-top-color:#ebccd1}.panel-danger>.panel-heading .badge{color:#f2dede;background-color:#a94442}.panel-danger>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#ebccd1}.broadcasters{width:100%;border:none;padding:2px;height:54px;background:#<%=colors.btcolor %>;background-image:url(/site/<%= host %>bacmic.png?z)}.broadcasters>div{max-width:350px}.broadcasters>div>.prod{float:right;color:#113233;background-image:url(imgs/mic.png);position:relative;left:3px;text-align:center;width:56px;border:2px solid #afbfc2!important;background-color:#fff;border-radius:30% 1%!important;margin:1px!important;height:47px!important;background-position:center!important;background-repeat:no-repeat!important;background-size:cover;border-radius:30% 1%!important}.broadcasters>div>.prod>hr{margin:0;border-top-color:#<%=colors.btcolor %>}.broadcasters>div>.prod>.evant>i{position:relative;font-size:16px!important;text-align:center;color:#<%=colors.btcolor %>;top:36px;height:18px;border-radius:50%;background-color:#ffffffc7;border:1px solid #d9534f70}.fr{float:right}.fl{float:left}.tbox{overflow:scroll;border-radius:2px;border:1px solid #d3d3d3;padding:6px;max-height:32px;min-height:32px;height:32px;font-weight:700;overflow:hidden;resize:none}.hid{display:none}.noflow{overflow:hidden}.hand{cursor:hand}img{vertical-align:middle}.break{overflow:auto;word-wrap:break-word;overflow-x:hidden;overflow-wrap:break-word}#cp .tab-content .tab-pane{min-width:400px!important}.u-ico{margin-top:1px;max-height:18px;object-fit:scale-down}.emoi{max-width:240px;max-height:20px}.unread{background-color:#ffc89d!important}.object-fit{object-fit:contain;object-position:center right}table,th,td{border:0}th,td{padding:0}.loginItms{position:absolute;display:none;border:2px solid #af020b;background-color:#f93634;border-radius:25px 0 0 25px;background-image:url(imgs/banner.png);background-repeat:no-repeat;background-size:contain;right:0;top:30px;width:250px}.loginImg{float:right;width:36px;height:36px;border:1px solid #ed5555;margin:1px;background-size:cover;background-repeat:no-repeat;border-radius:50%}.loginLogo{float:right;margin:1px;margin-top:-20px}.loginIco{float:right;margin:0 1px 1px;max-height:18px;background-color:#fff;padding:1px;border-radius:2px}.loginFlog{float:left;margin:2px 0 5px 5px;max-height:15px}.loginUserName{font-size:15px!important;float:right;font-family:arial;font-weight:9000;max-width:170px;min-width:100px;text-align:right;text-shadow:-1px 1px 2px #fff;color:#842c2a;white-space:nowrap;overflow:hidden!important;text-overflow:ellipsis}table{border-spacing:0;border-collapse:collapse}.mini{font-size:90%!important}.corner{border-radius:5px}.minix{font-size:80%!important}.nosel,.u-ico,.u-pic,.tago{-webkit-touch-callout:none;-webkit-user-select:none;-khtml-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:default}.center{margin:0 auto}.dots{display:inline-block;white-space:nowrap;overflow:hidden!important;text-overflow:ellipsis;max-width:100%}.borderg{border:1px solid #f4f4f4}.border{border:1px solid #000}.fitimg{background-size:contain;background-repeat:no-repeat;background-position:50% 50%}.bord{border-inline:4px solid #1e90ff!important}html{height:100vh;width:100vw}.fa{text-align:left}.modal-header{padding:5px}.badge{padding:0 3px}.item3{margin-left:45px}button.btn{padding:5px}.d2{padding-bottom:2px}.d2,#rooms,#users{background-color:#<%= colors.hicolor %>!important}*{font-family:serif;font-weight:700;text-shadow:none!important;font-size:15.2px!important;line-height:1.4!important;font-weight:700!important;text-shadow:none!important}.u-msg{line-height:1.5!important}.ae{border:1px solid #000!important;border-radius:2px!important;float:left;text-align:center;width:20%;max-width:80px;padding:4px 0;margin:1px;margin-bottom:2px}.pmsgc{background-color:rgba(0,77,255,0.08)!important}.ppmsgc{background-color:#f1f1ff!important}.hmsg{background-color:#faf0e6!important}.label-primary,.btn-primary,.bg-primary,.label-primary:hover,.btn-primary:hover,.btn-primary:focus{background-color:#<%=colors.btcolor %>!important;background-image:none}.bg{background-color:#<%=colors.bgcolor %>!important}.bgg{background-color:#789}.pophead{background-color:slategrey}.light{background-color:#<%= colors.hicolor %>!important}.light.fl.pro.center.break{background-color:#<%= colors.hicolor %>!important}.label,.btn{border-radius:1px}.label-primary,.btn-primary{background-color:#000}.tablebox.d-flex.footer.fl.light{background-color:#<%= colors.hicolor %>!important}.fl.nosel.label.pnhead{background-color:#<%= colors.bgcolor %>!important}.label-primary{background-color:#337ab7}.label-primary[href]:hover,.label-primary[href]:focus{background-color:#286090}.label-success{background-color:#5cb85c}.hid{display:none}#mic .mic,#muteall{background:#<%=colors.btcolor %>}.typingIndicatorBubbleDot{width:4px;height:4px;margin-right:4px;background-color:#57585a;border-radius:50%;animation-name:bounce;animation-duration:1.3s;animation-timing-function:linear;animation-iteration-count:infinite}.mic{width:53px;height:50px;margin:1px;border-radius:4px;background-image:url(imgs/mic.png);background-size:cover;background-repeat:no-repeat;background-position:center}.typingIndicatorBubbleDot:first-of-type{margin:0 4px}.typingIndicatorBubbleDot:nth-of-type(2){animation-delay:.15s}.typingIndicatorBubbleDot:nth-of-type(3){animation-delay:.3s}.flex-grow-1{flex-grow:1!important}.c-flex{display:flex;flex-direction:column}.d-flex{display:flex}.flex-fill{flex:1 1 auto!important}@supports not ((-webkit-backdrop-filter: none) or (backdrop-filter: none)){.bgfbackground-color:rgba(0,0,0,.5)}}.nav-pills li a{border-radius:0!important}@keyframes bounce{0%,60%,100%{transform:translateY(0)}30%{transform:translateY(-4px)}}td{border:1px solid grey;font-weight:700}th{background-color:#6495ed;color:#fff;padding:5px}.tab-pane{padding:0;width:100%}.pgr{-webkit-appearance:none;-moz-appearance:none;appearance:none}html{overflow:hidden}.tc{border-radius:3px!important}.bgf{backdrop-filter:blur(8px);-webkit-backdrop-filter:blur(8px)}@supports not ((-webkit-backdrop-filter: none) or (backdrop-filter: none)){bgfbackground-color:rgba(.5)}td{overflow-y:hidden!important}.uzr .fitimg{background-size:cover}.uhtml{text-align:left;width:100%;margin:0!important;border-bottom:1px solid #e6e6fa;padding-right:1px;background-color:#fafafa}.uhtml .u-pic{min-width:52px;width:52px;min-height:48px;max-height:62px;background-color:#f3f3f3;margin-top:1px}.uhtml .co.ico{width:18px;height:18px;border-radius:1px;margin-top:-2px;margin-left:auto}.emoi{max-width:240px;max-height:20px}.emoi{height:20px;margin:3px;cursor:pointer}.emoii{width:22px;max-width:22px;height:22px;margin:0;padding:5px}.popover{position:absolute;top:0;left:0;z-index:1060;display:none;max-width:276px;padding:1px;font-family:"Helvetica Neue",Helvetica,Arial,sans-serif;font-size:14px;font-style:normal;font-weight:400;line-height:1.42857143;text-align:left;text-align:start;text-decoration:none;text-shadow:none;text-transform:none;letter-spacing:normal;word-break:normal;word-spacing:normal;word-wrap:normal;white-space:normal;background-color:#fff;-webkit-background-clip:padding-box;background-clip:padding-box;border:1px solid #ccc;border:1px solid rgba(0,0,0,0.2);border-radius:6px;-webkit-box-shadow:0 5px 10px rgba(0,0,0,0.2);box-shadow:0 5px 10px rgba(0,0,0,0.2);line-break:auto}.popover.top{margin-top:-10px}.popover.right{margin-left:10px}.popover.bottom{margin-top:10px}.popover.left{margin-left:-10px}.popover-title{padding:8px 14px;margin:0;font-size:14px;background-color:#f7f7f7;border-bottom:1px solid #ebebeb;border-radius:5px 5px 0 0}.popover{height:250px;overflow-y:auto;white-space:pre-wrap}.popover.right{margin-left:-111px;min-width:339px!important;border-radius:0;min-height:559px;margin-top:-300px}.popover.right .btn.hand.borderg.corner{min-width:min-content;padding:4px!important;height:32px}.popover.right{margin-left:-111px!important;min-width:339px!important;border-radius:0;min-height:559px;margin-top:-300px}.popover.right .btn.hand.borderg.corner{min-width:min-content;padding:4px!important;height:32px}.popover.fade.top{width:700px!important;height:300px!important;min-width:300px!important;border-radius:0!important}.popover-content{padding:1px}.popover > .arrow,.popover > .arrow:after{position:absolute;display:block;width:0;height:0;border-color:transparent;border-style:solid}.popover > .arrow{border-width:11px}.popover > .arrow:after{content:"";border-width:10px}.popover.top > .arrow{bottom:-11px;left:50%;margin-left:-11px;border-top-color:#999;border-top-color:rgba(0,0,0,0.25);border-bottom-width:0}.popover.top > .arrow:after{bottom:1px;margin-left:-10px;content:" ";border-top-color:#fff;border-bottom-width:0}.popover.right > .arrow{top:50%;left:-11px;margin-top:-11px;border-right-color:#999;border-right-color:rgba(0,0,0,0.25);border-left-width:0}.popover.right > .arrow:after{bottom:-10px;left:1px;content:" ";border-right-color:#fff;border-left-width:0}.popover.bottom > .arrow{top:-11px;left:50%;margin-left:-11px;border-top-width:0;border-bottom-color:#999;border-bottom-color:rgba(0,0,0,0.25)}.popover.bottom > .arrow:after{top:1px;margin-left:-10px;content:" ";border-top-width:0;border-bottom-color:#fff}.popover.left > .arrow{top:50%;right:-11px;margin-top:-11px;border-right-width:0;border-left-color:#999;border-left-color:rgba(0,0,0,0.25)}.popover.left > .arrow:after{right:1px;bottom:-10px;content:" ";border-right-width:0;border-left-color:#fff}</style>
</head>
<body>
  <div class="modal break" id="edituser" role="dialog" style="margin:5px;z-index: 30000">
    <div class="modal-dialog fr break" style="height: 50%;min-height: 300px;overflow: visible;margin-top:2px;">
      <div class="modal-content c-flex break" style="width:306px;height: 100%;">
        <div style="color:white;" onclick="$(this).parent().parent().parent().modal('hide');$('.reply').html('')"
          class="modal-header label-primary">
          <span class="pull-right clickable badge"><i class="fa fa-times"></i></span>
          <label style="margin:1px;" class="mini modal-title tltp"></label>
        </div>
        <label style="margin:2px" class="label label-primary">كلمه السر</label>
        <div style="display:inherit;border-bottom:1px solid darkgray">
          <input style="width:250px;" class="userpwd form-control" type="text" placeholder="كلمه السر الجديدة">
          <button style="width: 96%;
text-align: center;
margin: 5px;" class="usersetpwd btn btn-success fa fa-save"
            onclick="$(this).hide();setTimeout(()=>{$(this).show()},5000);">حفظ</button>
        </div>
        <div class="border fl" style="width: 100%; padding: 4px; margin-top: 2px;">
          <label>المجموعه</label><br />
          <select style="width: 200px; display: inline;" class="userpowera selbox form-control"> </select>
          <br />
          <label>المده بالأيام</label><br />
          <input type="number" class="userdaysa" /><br />
          <a class="fa fa-check btn btn-success upowera border" style="margin: 2px;">حفظ</a>
        </div>
        <div class='s01' style="text-align: right;margin-right:5px">توثيق العضوية <input class="verification"
            style="margin-left: 5px;" type="checkbox"></div>
        <div class='s02' style="text-align: right;margin-right:5px">دخول مميز <input class="loginG"
            style="margin-left: 5px;" type="checkbox"></div>
            <div class='s03' style="text-align: right;margin-right:5px">تصميم العضوية<input class="ifedit"
              style="margin-left: 5px;" type="checkbox"></div>
        <button class="verlog btn btn-success fa fa-save"
          style="width: 100%;text-align:center;margin:5px">حفظ</button>
        <hr style="width: 50%; margin-top: 5px;margin-bottom: 5px;">
        <button class="delus btn btn-danger" style="width: 96%;
text-align: center;
margin: 5px;">حذف العضويه</button>
        <hr>
      </div>
    </div>
  </div>
 
  <div class="paneladmin" style="z-index: 1000; display: block;">
 <div style="margin-left: -15px;">
<div class="column cl1" style="
    border-right: 2px solid darkgray;
    border-bottom: 2px solid darkgray;
    
    background-position: center;
	overflow: auto;
    background-size: cover;
    background-repeat: no-repeat;">
<ul class="side-menu">
            <ul class="side-menu">
              <li class='logsad hid'> <i class='fa fa-bars ico_pl'></i> <a>السجل</a></li>
              <li class='statead hid'><i class='fa fa-history ico_pl'></i> <a>الحالات</a></li>
              <li class='userad hid'><i class='fa fa-users ico_pl'></i><a>الأعضاء</a></li>
              <li class='bandad hid'><i class='fa fa-ban ico_pl'></i><a>الحظر</a></li>
              <li class='powerad hid'><i class='fa fa-delicious ico_pl'></i><a>الصلاحيات</a></li>
              <li class='filterad hid'><i class='fa fa-filter ico_pl'></i><a>فلتر</a></li>
              <li class='report hid'><i class='fa fa-filter ico_pl'></i><a>التبليغات</a></li>
              <li class='roomsad hid'><i class='fa fa-home ico_pl'></i><a>الغرف</a></li>
              <li class='shrtad hid'><i class='fa fa-gg ico_pl'></i><a>الإختصارات</a></li>
              <li class='subsad hid'><i class='fa fa-archive ico_pl'></i><a>الإشتراكات</a></li>
              <li class='msgsad hid'><i class='fa fa-comment ico_pl'></i><a>الرسائل</a></li>
              <li class='emo hid'><i class='fa fa-smile-o ico_pl'></i><a>الرموز</a></li>
              <li class='ownad hid'><i class='fa fa-cog ico_pl'></i><a>الإعدادات</a></li>
              <li class='ownad1 hid'><i class='fa fa-sitemap ico_pl'></i><a>الموقع</a></li>
              <li class='otherad hid'><i class='fa fa-pencil-square-o ico_pl'></i><a>الإضافات</a></li>
              <li class='ownad1 hid'><i class='fa fa-user ico_pl'></i><a>Bots</a></li>
            </ul>
          </div>
          <div class="column cl2">
            <div class="Panellogs" style="display:none">
              <table id="logsPage" class="cell-border hover order-column">
                <thead>
                  <tr>
                    <th>الحاله</th>
                    <th>العضو</th>
                    <th>الزخرفه</th>
                    <th>الأي بي</th>
                    <th>الدولة</th>
                    <th>الجهاز</th>
                    <th>الوقت</th>
                    <th>المصدر</th>
                    <th>سماح</th>
                  </tr>
                </thead>
              </table>
              <div class='fr'>
                <button class='btn btn-info' onclick="nextPage('log')">عرض المزيد</button>
              </div>
            </div>

            <div class="Panelstate" style="display:none">
              <table id="statePage" class="cell-border hover order-column">
                <thead>
                  <tr>
                    <th>الحاله</th>
                    <th>العضو</th>
                    <th>العضو الثاني</th>
                    <th>الغرفة</th>
                    <th>الاي بي</th>
                    <th>الوقت</th>
                  </tr>
                </thead>

              </table>
              <div class='fr'>
                <button class='btn btn-info' onclick="nextPage('state')">عرض المزيد</button>
              </div>
            </div>
            <div class="reportstate" style="display:none">
              <table id="reportpage" class="cell-border hover order-column">
                <thead>
                  <tr>
                    <th>الحالة</th>
                    <th>المبلغ</th>
                    <th>المبلغ عليه </th>
                    <th>الرسالة</th>
                  </tr>
                </thead>

              </table>
              <div class='fr'>
                <button class='btn btn-info' onclick="nextPage('report')">عرض المزيد</button>
              </div>
            </div>
            <div class="UsersLogs" style="display:none">
              <table id="usersPage" class="cell-border hover order-column">
                <input id="searchusers" type="text" class='form-control' style="margin-bottom:5px;width:200px"
                  placeholder='بحث'>
                <thead>
                  <tr>
                    <th>العضو</th>
                    <th>الزخرفه</th>
                    <th>الاي بي</th>
                    <th>الجهاز</th>
                    <th>الصلاحية</th>
                    <th>أخر تواجد</th>
                    <th>التسجيل</th>
                    <th>الاعدادات</th>
                  </tr>
                </thead>
              </table>
              <div class='fr'>
                <button class='btn btn-info' onclick="nextPage('users')">عرض المزيد</button>
              </div>
            </div>

            <div class="GustsLogs" style="display:none">
              <div style="padding:5px">
                <label class="label label-info fa fa-home">Bots</label>
                <label style="margin-left:5px;background-color: black;" id="maxbots" class="label label-primary"></label><br>
                <hr style="margin:5px">
                <input id="nameb" type="text" placeholder="إسم البوت" style="width:210px">
                <label style="margin-left:5px;" class="label label-primary">الاسم</label>
                <hr style="margin:5px">
                <input id="timestart" type="number" placeholder="وقت الدخول بالدقائق" style="width:200px">
                <label style="margin: 0px;" class="label label-primary">الخمول</label>
                <hr style="margin:5px">
                <input id="timestop" type="number" placeholder="وقت الخروج بالدقائق" style="width:200px">
                <label style="margin: 0px;" class="label label-primary">الخروج</label>
                <hr style="margin:5px">
<label class="checkbox-inline"><input id="rdel" type="checkbox" value="">دخول وخروج تلقائي</label>
<hr style="margin:5px">
                <input id="msgbot" type="text" placeholder="الحاله" style="width:210px">
                <label style="margin-left:5px;" class="label label-primary">الحاله</label>
                <hr style="margin:5px">
                <div style="display:inline">
                  <img style="width:100px;height:100px" class="spicbot" onclick="S_PIC('bot')"
                    src="/site/<%= host %>pic.png">
                    <script>
                      $(".spicbot").attr("src","/site/"+location.host+"pic.png")
                      </script>
                  <label style="margin-left:5px;" class="label label-primary">الصورة</label>
                </div>
                <hr style="margin:5px">
                <select id="rommbot" style="width:210px">
                </select>
                <label style="margin-left:5px;" class="label label-primary">الغرفة</label>
                <hr style="margin:5px">
                <select id="statsbots" style="width:210px">
                  <option value="0">أخضر</option>
                  <option value="1">أصفر</option>
                  <option value="2">أحمر</option>
                </select>
                <label style="margin-left:5px;" class="label label-primary">الحاله</label>
      <hr style="margin:5px">
      <input class="color {pickerPosition:'top'} botnamec dots" style="width: 64px; height: 22px; color: rgb(0, 0, 0); background-image: none; background-color: rgb(0, 0, 0);" autocomplete="off" />
              <label style="margin-left:5px;" class="label label-primary">لون الخلفيه</label>
      <hr style="margin:5px">
        <input class="color {pickerPosition:'top'} botnamebc dots" style="width: 64px; height: 22px; color: rgb(0, 0, 0); background-image: none; background-color: rgb(0, 0, 0);" autocomplete="off" />
      <label style="margin-left:5px;" class="label label-primary">لون الأسم</label>
      <hr style="margin:5px">
      <input class="color {pickerPosition:'top'} botmsgc dots" style="width: 64px; height: 22px; color: rgb(0, 0, 0); background-image: none; background-color: rgb(0, 0, 0);" autocomplete="off" />
      <label style="margin-left:5px;" class="label label-primary">لون الحاله</label>
      <hr style="margin:5px">
                <input id="likebot" type="number" placeholder="لايكات" style="width:200px">
                <label style="margin: 0px;" class="label label-primary">اللايكات</label>
                <hr style="margin:5px">
                <select id="countrybot" style="width:210px">
                </select>
                <label style="margin-left:5px;" class="label label-primary">الدولة</label>
                <hr style="margin:5px">
                <select id="rankbot" style="width:210px">
                </select>
                <label style="margin-left:5px;" class="label label-primary">الرتبة</label>
                <hr style="margin:5px">
                <button class="btn btn-success"
                  onclick="addbot();$(this).hide();setTimeout(()=>{$(this).show()},5000);">حفظ</button>
              </div>

              <table id="gustsPage" class="cell-border hover order-column">
                <thead>
                  <tr>
                    <th>الصوره</th>
                    <th>الأسم</th>
                    <th>الصلاحية</th>
                    <th>الدولة</th>
                    <th>الغرفة</th>
                    <th>الأي بي</th>
                    <th>الحاله</th>
                    <th>رسائل</th>
                    <th>د/خ</th>
                    <th>حذف</th>
                  </tr>
                </thead>
              </table>
              <div class='fr'>
                <button class='btn btn-info' onclick="nextPage('gusts')">عرض المزيد</button>
              </div>
            </div>

            <div class="BotsLogs" style="display:none">
              <table id="botsPage">
                <thead>
                  <tr>
                    <th>الصوره</th>
                    <th>الأسم</th>
                    <th>الصلاحية</th>
                    <th>الدولة</th>
                    <th>الغرفة</th>
                    <th>الاي بي</th>
                    <th>الحاله</th>
                    <th>رسائل</th>
                    <th>د/خ</th>
                    <th>حذف</th>
                  </tr>
                </thead>
              </table>
            </div>

            <div class="BansLogs" style="display:none">
              <div id="browser" style="width: 100%;margin: 20px 2px;">
                <label class="label label-primary">المتصفحات المسموح بها</label>
                <div
                  style="width: 400px;border: 1px solid;margin: 9px 0 0 -4px;padding: 2px;border-radius: 0 7px 7px 0;">
                  <hr style="margin: 10px 5px 5px;">
                  <label class="checkbox-inline" style="margin: 0 0 0 10px;">
                    <input type="checkbox" id="browser9" value="option3"> السماح بدخول جميع المتصفحات
                  </label>
                  <hr style="margin: 10px 5px 5px;">
                  <label style="width: 170px;margin: 0 0 0 15px;" class="checkbox-inline">
                    <input style="margin-left: -35px !important;" class="fa fa-chrome" type="checkbox" id="browser1"
                      value="option1">Chrome
                  </label>
                  <label style="width: 170px;margin: 0 0 0 15px;" class="checkbox-inline">
                    <input style="margin-left: -35px !important;" type="checkbox" id="browser2" value="option2"
                      class="fa fa-firefox"> Firefox
                  </label>
                  <br>
                  <label style="width: 170px;margin: 0 0 0 15px;" class="checkbox-inline">
                    <input style="margin-left: -35px !important;" type="checkbox" id="browser3" value="option3"
                      class="fa fa-safari"> Safari
                  </label>
                  <label style="width: 170px;margin: 0 0 0 15px;" class="checkbox-inline">
                    <input style="margin-left: -35px !important;" type="checkbox" id="browser4" value="option3"
                      class="fa fa-opera"> Opera
                  </label>
                  <br>
                  <label style="width: 170px;margin: 0 0 0 15px;" class="checkbox-inline">
                    <input style="margin-left: -35px !important;" type="checkbox" id="browser5" value="option3"
                      class="fa fa-internet-explorer"> Internet Explorer
                  </label>
                  <label style="width: 170px;margin: 0 0 0 15px;" class="checkbox-inline">
                    <input style="margin-left: -35px !important;" type="checkbox" id="browser6" value="option3"
                      class="fa fa-edge"> Edge
                  </label>
                  <br>
                  <label style="width: 170px;margin: 0 0 0 15px;" class="checkbox-inline">
                    <input style="margin-left: -35px !important;" type="checkbox" id="browser7" value="option3"
                      class="fa fa-android"> Android webview
                  </label>
                  <label style="width: 170px;margin: 0 0 0 15px;" class="checkbox-inline">
                    <input style="margin-left: -35px !important;" type="checkbox" id="browser8" value="option3"
                      class="fa fa-scribd"> Samsung Internet
                  </label>
                  <button id="btnbrowser" onclick="$(this).hide();setTimeout(()=>{$(this).show()},5000);"
                    class="btn btn-success">حفظ</button>
                </div>
              </div>

              <div id="system" style="width: 100%;margin: 20px 2px;">
                <label class="label label-primary">انظمة التشغيل المسموح بها</label>
                <div
                  style="width: 400px;border: 1px solid;margin: 9px 0 0 -4px;padding: 2px;border-radius: 0 7px 7px 0;">
                  <hr style="margin: 10px 5px 5px;">
                  <label class="checkbox-inline" style="margin: 0 0 0 10px;">
                    <input type="checkbox" id="system7" value="option3"> السماح بدخول جميع انظمة التشغيل
                  </label>
                  <hr style="margin: 10px 5px 5px;">
                  <label style="width: 170px;margin: 0 0 0 15px;" class="checkbox-inline">
                    <input style="margin-left: -35px !important;" class="fa fa-windows" type="checkbox" id="system1"
                      value="option1">Windows
                  </label>
                  <label style="width: 170px;margin: 0 0 0 15px;" class="checkbox-inline">
                    <input style="margin-left: -35px !important;" type="checkbox" id="system2" value="option2"
                      class="fa fa-linux"> Linux
                  </label>
                  <br>
                  <label style="width: 170px;margin: 0 0 0 15px;" class="checkbox-inline">
                    <input style="margin-left: -35px !important;" type="checkbox" id="system3" value="option3"
                      class="fa fa-android"> Android
                  </label>
                  <label style="width: 170px;margin: 0 0 0 15px;" class="checkbox-inline">
                    <input style="margin-left: -35px !important;" type="checkbox" id="system4" value="option3"
                      class="fa fa-apple"> iOS
                  </label>
                  <br>
                  <label style="width: 170px;margin: 0 0 0 15px;" class="checkbox-inline">
                    <input style="margin-left: -35px !important;" type="checkbox" id="system6" value="option3"
                      class="fa fa-th-large"> Windows Phone
                  </label>
                  <label style="width: 170px;margin: 0 0 0 15px;" class="checkbox-inline">
                    <input style="margin-left: -35px !important;" type="checkbox" id="system5" value="option3"
                      class="fa fa-times-circle "> Mac OS
                  </label>
                  <button id="btnSystem" onclick="$(this).hide();setTimeout(()=>{$(this).show()},5000);"
                    class="btn btn-success">حفظ</button>
                </div>
              </div>
              <button class="bandnow btn btn-danger">حظر</button> <input class='baninput' style="margin-left:5px"
                type="text" placeholder="حظر جهاز/اي بي/ دولة / حساب">
              <table id="bansPage" class="cell-border hover order-column">
                <thead>
                  <tr>
                    <th>العضو</th>
                    <th>من قبل</th>
                    <th>السبب</th>
                    <th>الجهاز</th>
                    <th>الاي بي</th>
                    <th>الحساب</th>
                    <th>الدولة</th>
                    <th>تاريخ الإنتهاء</th>
                    <th>تاريخ الحظر</th>
                    <th>الحاله</th>
                  </tr>
                </thead>
              </table>
              <button class='btn btn-info' onclick="nextPage('bands')">عرض المزيد</button>

            </div>

            <div class="MessageLogs" style="display:none">
              <div id="msgs">
                <input class="msgt" placeholder="عنوان الرساله">
                <br>
                <textarea class="msgm" style="width: 199px;height: 200px;resize: none;margin-top: 5px;"
                  placeholder="الرساله" maxlength="250"></textarea>
                <br><button style="margin:5px;width:auto;" onclick="msgsit('w',$('.msgt').val(),$('.msgm').val());"
                  class="fa fa-check btn btn-success">إضافه ألى رسائل الترحيب</button>
                <br><button style="margin:5px;width:auto;" onclick="msgsit('d',$('.msgt').val(),$('.msgm').val());"
                  class="fa fa-check btn btn-danger">إضافه ألى الرسائل اليوميه</button>
              </div>
              <table id="messagePage" class="cell-border hover order-column">
                <thead>
                  <tr>
                    <th>التصنيف</th>
                    <th>العنوان</th>
                    <th>الرساله</th>
                    <th>الحاله</th>
                  </tr>
                </thead>
              </table>
              <div class='fr'>
                <button class='btn btn-info' onclick="nextPage('message')">عرض المزيد</button>
              </div>
            </div>


            <div class="ShortLogs" style="display:none">
              <div id="shrt">
                <input style="margin: 4px;" class="shrtname" placeholder="الإختصار \ س1">
                <br><input style="margin: 4px;" class="shrtvalue" placeholder="الزخرفه \ السلام عليكم">
                <br><button style="margin:5px;width:200px;" onclick="shrtadd();"
                  class="fa fa-check btn btn-success">إضافه</button>
                <br>
              </div>

              <table id="shortPage" class="cell-border hover order-column">
                <thead>
                  <tr>
                    <th>الإختصار</th>
                    <th>الزخرفه</th>
                    <th>حذف</th>
                  </tr>
                </thead>
              </table>
              <div class='fr'>
                <button class='btn btn-info' onclick="nextPage('short')">عرض المزيد</button>
              </div>
            </div>

            <div class="SubsLogs" style="display:none">
              <table id="subsPage" class="cell-border hover order-column">
                <thead>
                  <tr>
                    <th>الإشتراك</th>
                    <th>العضو</th>
                    <th>الزخرفه</th>
                    <th>تاريخ الإنتهاء</th>
                    <th>الأيام المتبقية</th>
                    <th>تاريخ الترقية</th>
                    <th>حذف</th>
                  </tr>
                </thead>
              </table>
              <div class='fr'>
                <button class='btn btn-info' onclick="nextPage('subs')">عرض المزيد</button>
              </div>
            </div>

            <div class="FilterLogs" style="display:none">
              <div id="fltr">
                <input class="fltrit" style="width: 195px;margin: 5px;" placeholder="اضافه كلمه\موقع">
                <br><button style="margin:5px;width:195px;" onclick="fltrit('bmsgs',$('.fltrit').val());"
                  class="fa fa-times btn btn-danger">إضافه ألى الكلمات الممنوعه</button>
                <br><button style="margin:5px;width:195px;" onclick="fltrit('wmsgs',$('.fltrit').val());"
                  class="fa fa-warning btn btn-info">إضافه ألى الكلمات المراقبه</button>
                <br><b>آخر الكلمات الممنوعه</b>
                <div id="fltred" class="break" style="width:290px;height:200px;"></div>
              </div>
              <table id="filterPage" class="cell-border hover order-column">
                <thead>
                  <tr>
                    <th>التصنيف</th>
                    <th>الكلمه</th>
                    <th>حذف</th>
                  </tr>
                </thead>
              </table>
              <div class='fr'>
                <button class='btn btn-info' onclick="nextPage('filter')">عرض المزيد</button>
              </div>
            </div>

            <div class="RoomsLogs" style="display:none">
              <table id="roomsPage" class="cell-border hover order-column">
                <thead>
                  <tr>
                    <th>الصورة</th>
                    <th>الغرفة</th>
                    <th>صاحب الغرفه</th>
                    <th>كلمة السر</th>
                    <th>تحديد الغرفة</th>
                    <th>حذف الغرفة</th>
                  </tr>
                </thead>
              </table>
              <div class='fr'>
                <button class='btn btn-info' onclick="nextPage('rooms')">عرض المزيد</button>
              </div>
            </div>

            <div class="SettingsLogs" style="display:none">
              <label class="label label-info fa fa-home">إعدادات الموقع</label>
             
              <div class='owneredit'>
                <input type="number" min="0" value="0" class="lengthbc  dots" style="width: 80px;"
                  autocomplete="off"><b>عدد احرف الرساله المسموح بها في الحائط</b><br>
                <input type="number" min="0" value="0" class="lengthpm  dots" style="width: 80px;"
                  autocomplete="off"><b>عدد احرف الرساله المسموح بها في الخاص</b><br>
                <input type="number" min="0" value="0" class="lengthroom  dots" style="width: 80px;"
                  autocomplete="off"><b>عدد احرف الرساله المسموح بها في العام</b>
                <hr>
                <input type="number" min="0" value="0" class="maxdaymsg  dots" style="width: 80px;"
                  autocomplete="off"><b>المده بين رسائل اليومية بالدقيقه</b><br>
                <input type="number" min="0" value="0" class="maxlikealert  dots" style="width: 80px;"
                  autocomplete="off"><b>عدد لايكات الأعلان</b><br>
                <input type="number" min="0" value="0" class="maxlikebc  dots" style="width: 80px;"
                  autocomplete="off"><b>عدد لايكات الحائط</b><br>
                <input type="number" min="0" value="0" class="maxlikecam  dots" style="width: 80px;"
                  autocomplete="off"><b>عدد لايكات للإتصال في الخاص</b><br>
                <input type="number" min="0" value="0" class="maxlikemic  dots" style="width: 80px;"
                  autocomplete="off"><b>عدد لايكات المايك</b><br>
                <input type="number" min="0" value="0" class="maxlikestory  dots" style="width: 80px;"
                  autocomplete="off"><b>عدد لايكات إنشاء قصة</b><br>
                <input type="number" min="0" value="0" class="maxlikename  dots" style="width: 80px;"
                  autocomplete="off"><b>عدد لايكات تغيير الأسم</b><br>
                <input type="number" min="0" value="0" class="maxlikepic  dots" style="width: 80px;"
                  autocomplete="off"><b>عدد لايكات تغيير الصوره</b><br>
                  <input type="number" min="0" value="0" class="maxlikeyot  dots" style="width: 80px;"
                  autocomplete="off"><b>عدد لايكات تغيير رابط اليوتيوب</b><br>
                <input type="number" min="0" value="0" class="maxlikepm  dots" style="width: 80px;"
                  autocomplete="off"><b>عدد لايكات إرسال رسالة في الخاص</b><br>
                <input type="number" min="0" value="0" class="maxlikeroom  dots" style="width: 80px;"
                  autocomplete="off"><b>عدد لايكات الكتابة في الغرفة</b><br>
                <input type="number" min="0" value="0" class="maxlikesendpicpm  dots" style="width: 80px;"
                  autocomplete="off"><b>عدد لايكات إرسال ملف في الخاص</b><br>
                <input type="number" min="0" value="0" class="maxlogin  dots" style="width: 80px;"
                  autocomplete="off"><b>عدد العضويات التي يمكنك الدخول بيها</b><br>
                <input type="number" min="0" value="0" class="maxuploadfile  dots" style="width: 80px;"
                  autocomplete="off"><b>عدد لايكات لارسال ملف في الحائط</b><br>
                <input type="number" min="0" value="0" class="maxrep  dots" style="width: 80px;"
                  autocomplete="off"><b>عدد تسجيل عضويات للمستخدم الواحد</b><br>
                <input type="number" min="0" value="0" class="gustmin  dots" style="width: 80px;"
                  autocomplete="off"><b>عدد الاحرف لأسم الزائر</b><br>
                <input type="number" min="0" value="0" class="registermin  dots" style="width: 80px;"
                  autocomplete="off"><b>عدد الاحرف لتسجيل العضوية</b><br>
                <input type="number" min="0" value="0" class="bctime  dots" style="width: 80px;"
                  autocomplete="off"><b>المده بين رسائل الحائط بالدقيقه</b>
                <hr>


                <input id="bars" type="checkbox" class="bars  dots" autocomplete="off">
                <label for="bars" class="checkbox-inline"> عدم تثبيت الحائط</label>
                <br>
                <input id="gust" type="checkbox" class="gust  dots" autocomplete="off">
                <label for="gust" class="checkbox-inline"> عدم السماح بدخول الزائر</label>
                 <br> 
                <input id="register" type="checkbox" class="register  dots" autocomplete="off">
                <label for="register" class="checkbox-inline">عدم السماح بتسجيل عضوية</label>
                <br>
                <input id="offline" type="checkbox" class="offline  dots" autocomplete="off">
                <label for="offline" class="checkbox-inline">تثبيت العضويات</label>
                <br>
                <input id="replay" type="checkbox" class="replay  dots" autocomplete="off">
                <label for="replay" class="checkbox-inline"> الرد في الغرف</label>
                <br>
                <input id="replaybc" type="checkbox" class="replaybc  dots" autocomplete="off">
                <label for="replaybc" class="checkbox-inline"> التعليقات في الحائط</label>
                <br>
                <input id="likebc" type="checkbox" class="likebc  dots" autocomplete="off">
                <label for="likebc" class="checkbox-inline"> اللايكات في الحائط</label>
                <br>
                <input id="callmic" type="checkbox" class="callmic  dots" autocomplete="off">
                <label for="callmic" class="checkbox-inline">المكالمة في الخاص</label>
                <br>
                <input id="callsot" type="checkbox" class="callsot  dots" autocomplete="off">
                <label for="callsot" class="checkbox-inline">البصمة في الخاص</label>
                <br>
                <input id="showyot" type="checkbox" class="showyot  dots" autocomplete="off">
                <label for="showyot" class="checkbox-inline">بحث اليوتيوب في الجدار</label>
                <br>
                <input id="showsto" type="checkbox" class="showsto  dots" autocomplete="off">
                <label for="showsto" class="checkbox-inline">القصص في الجدار</label>
                <br>
                <input id="showtop" type="checkbox" class="showtop  dots" autocomplete="off">
                <label for="showtop" class="checkbox-inline">مبدع الحائط</label>
                <br>
                <input id="vpn" type="checkbox" class="vpn  dots" autocomplete="off">
                <label for="vpn" class="checkbox-inline">حظر VPN</label>
                <hr>
                <label class="btn btn-danger label fa fa-save border"
                  onclick="SaveSettings();$(this).hide();setTimeout(()=>{$(this).show()},5000);">حفظ</label>
                <br><br>
              </div>
              <div class="" style="padding:4px">
                <label style="margin-left:5px;" class="label hid label-primary">خيارات الموقع | </label>
                <label class="label label-primary fa fa-image">ايقونات السوابر</label><span
                  class="btn btn-primary minix fa fa-plus border" onclick="sendfilea(this,'sico');"></span>
                <div style="width:300px;" class="p-sico break">
                </div>
                <br>
                <label class="label label-primary fa fa-image">ايقونات الهدايا</label><span
                  class="btn btn-primary minix fa fa-plus border" onclick="sendfilea(this,'dro3');"></span>
                <div style="width:300px;" class="p-dro3 break">
                </div>
                <label class="label label-primary fa fa-image">اطار الأعضاء</label><span
                class="btn btn-primary minix fa fa-plus border" onclick="sendfilea(this,'atar');"></span>
              <div style="width:300px;" class="p-atar break">
              </div>
              <label class="label label-primary fa fa-image">خلفية الاعضاء</label><span
                class="btn btn-primary minix fa fa-plus border" onclick="sendfilea(this,'back');"></span>
              <div style="width:300px;" class="p-back break">
              </div>
              </div>
            </div>

            <div class="EmojiLogs" style="display:none">
              <label class="label label-primary fa fa-image">الإبتسامات</label><span
                class="btn btn-primary minix fa fa-plus border" onclick="sendfilea(this,'emo');"></span>
              <div class="p-emo break">
              </div>
            </div>
            <div class="OtherLogs" style="display:none">
              <label
                style="margin: 2px;font-weight: 100 !important;font-size: 14px !important;min-width: 160px;text-align: center;"
                class="btn btn-danger label fa fa-trash " onclick="StatsChat('logs')">حذف سجل الدخول</label>
              <label
                style="margin: 2px;font-weight: 100 !important;font-size: 14px !important;min-width: 160px;text-align: center;"
                class="btn btn-danger label fa fa-trash " onclick="StatsChat('stats')">حذف سجل الحالات</label>
              <label
                style="margin: 2px;font-weight: 100 !important;font-size: 14px !important;min-width: 160px;text-align: center;"
                class="btn btn-danger label fa fa-trash " onclick="StatsChat('filter')">حذف الفلتر</label>
                <label
                style="margin: 2px;font-weight: 100 !important;font-size: 14px !important;min-width: 160px;text-align: center;"
                class="btn btn-danger label fa fa-trash " onclick="StatsChat('reporte')">حذف التبليغات</label>
              <label
                style="margin: 2px;font-weight: 100 !important;font-size: 14px !important;min-width: 160px;text-align: center;"
                class="btn btn-danger label fa fa-trash " onclick="StatsChat('files')">حذف ملفات الدردشة</label>
              <label
                style="margin: 2px;font-weight: 100 !important;font-size: 14px !important;min-width: 160px;text-align: center;"
                class="btn btn-danger label fa fa-trash " onclick="StatsChat('bars')">حذف الحائط</label>
              <label
                style="margin: 2px;font-weight: 100 !important;font-size: 14px !important;min-width: 160px;text-align: center;"
                class="btn btn-danger label fa fa-trash " onclick="StatsChat('story')">حذف القصص</label>
              <label
                style="margin: 2px;font-weight: 100 !important;font-size: 14px !important;min-width: 160px;text-align: center;"
                class="btn btn-danger label fa fa-trash " onclick="StatsChat('point')">تصفير نقاط</label><br><br>
              <label
                style="margin: 2px;font-weight: 100 !important;font-size: 14px !important;min-width: 160px;text-align: center;"
                class="btn btn-info label fa fa-refresh " onclick="StatsChat('restart')">اعادة تشغيل
                الدردشة</label><br><br>
              <label
                style="margin: 2px;font-weight: 100 !important;font-size: 14px !important;min-width: 160px;text-align: center;"
                class="btn btn-success label fa fa-save " onclick="StatsChat('backup')">إنشاء نسخة احتياطي </label>
              <label
                style="margin: 2px;font-weight: 100 !important;font-size: 14px !important;min-width: 160px;text-align: center;"
                class="btn btn-warning label fa fa-refresh " onclick="StatsChat('import')">إستعادة اخر نسخة
                إحتياطية</label><br>
              <br>
              <div class='usernmb label label-info'></div>

            </div>
            <div class="SiteLogs" style="display:none">
              <label class="label label-primary fa fa-link">الدومين</label>
              
              <select id="hostname"></select>
              <style>hr#seo {
                margin-top: 10px !important;
                margin-bottom: 10px !important;
                border: 0;
                border-top: 2px solid #000 !important;
            }</style>
             <hr id="seo">
              <label style="margin-left:5px;" class="label label-primary">إسم الموقع</label>
              <br>
              <textarea style="resize:none;width:100%;height:80px" id="sett_name" type="text"
                placeholder="شات فلان الكتابي"></textarea>
              <br>
              <label class="label label-primary">عنوان الصفحه</label>
              <br>
              <textarea style="width:100%;resize:none;height:80px;" id="sett_title" type="text"
                placeholder="شات فلان الكتابي للجوال - شات خليجي عربي"></textarea>
              <br>
              <label class="label label-primary">وصف الموقع</label>
              <br>
              <textarea style="width:100%;resize:none;height:150px" id="sett_description" type="text"
                placeholder="شات فلان الكتابي للجوال , دردشه , مسابقات , العاب , تعارف , بدون تسجيل او تحميل"></textarea>
              <br>
              <label class="label label-primary">الكلمات الدلاليه</label>
              <br>
              <textarea style="width:100%;resize:none;height:150px" id="sett_keywords" type="text"
                placeholder="شات فلان, شات فلان الكتابي, شات خليجي, شات بدون تسجيل, شات بدون تحميل"></textarea>
              <br>
              <label class="label label-primary">السكربت JavaScript</label>
              <br>
              <textarea style="width:100%;resize:none;height:200px" id="sett_scr" type="text"
                placeholder="للمبرمجين فقط"></textarea>
              <br>
              <label class="label label-primary">لون القوالب</label>
      <br>
      <input class="jscolor color {pickerPosition:'top'} sbgt   dots" style="width: 80px; color: rgb(255, 255, 255); background-image: none; background-color: rgb(0, 0, 0);" autocomplete="off">
      <br>
      <label class="label label-primary">لون المحتوى</label>
      <br>
      <input class="color {pickerPosition:'top'} sbackground   dots" style="width: 80px; color: rgb(255, 255, 255); background-image: none; background-color: rgb(0, 0, 0);" autocomplete="off">
      <br>
      <label style="margin-left:5px;" class="label label-primary">لون الأزرار</label>
      <br>
      <input class="color {pickerPosition:'top'} sbuttons   dots" style="width: 80px; color: rgb(255, 255, 255); background-image: none; background-color: rgb(0, 0, 0);" autocomplete="off">
              <br><br><button onclick="SaveTNae();$(this).hide();setTimeout(()=>{$(this).show()},10000)"
                class="btn btn-success fa fa-save">حفظ</button>
              <br><br>
              <div style="display:inline-block;;text-align:center;margin-right:5px">
                <label class="label label-primary fa fa-image">ايقونه الموقع</label>
                <br>
                <img class="p-logo" onclick="sendfilea(this,'logo');"
                  style="width: 50px;height:50px;border: 2px solid darkgray;margin-top: 3px;" src="/site/<%= host %>logo.png">
              </div>
              <script>
                      $(".p-logo").attr("src","/site/"+location.host+"logo.png")
                      </script>
              <div style="display:inline-block;;text-align:center;margin-right:5px">
                <label class="label label-primary fa fa-image">ايقونه الرسائل</label>
                <br>
                <img class="p-msgpic" onclick="sendfilea(this,'msgpic');"
                  style="width: 50px;height:50px;border: 2px solid darkgray;margin-top: 3px;" src="/site/msgpic.png">
              </div>
              
              <div style="display:inline-block;text-align:center;margin-right:5px">
                <label class="label label-primary fa fa-image">ايقونه الغرف</label>
                <br>
                <img class="p-room" onclick="sendfilea(this,'room');"
                  style="width: 50px;height:50px;border: 2px solid darkgray;margin-top: 3px;" src="/site/<%= host %>room.png">
              </div>
              <script>
                      $(".p-room").attr("src","/site/"+location.host+"room.png")
                      </script>
              <div style="display:inline-block;text-align:center">
                <label class="label label-primary fa fa-image">ايقونه الاعضاء</label>
                <br>
                <img class="p-user" onclick="sendfilea(this,'user');"
                  style="width: 50px;height:50px;border: 2px solid darkgray;margin-top: 3px;" src="/site/<%= host %>pic.png">
              </div>
              <script>
                      $(".p-user").attr("src","/site/"+location.host+"pic.png")
                      </script>
              <br><br>
            </div>
            <div class="PowersLogs" style="display:none">
              <div id="powers" style="width:500px;">
                <select onchange="powerchange();" style="width:200px;display:inline"
                  class="powerbox selbox form-control"></select>
                <button class="delp btn btn-danger fa fa-times" style="top: 40px;position: relative;">حذف</button>
                <div class="sico border  fr"
                  style="width: auto;max-width: 230px;max-height: 612px;height:auto;overflow: auto;"></div>
              </div>
            </div>

          </div>
        </div>
      </div>

      <div id="StoryPanel"
        style="position:fixed;display:none;width:100%;height:100%;background:#000;z-index:99999999;left:0;top:0;padding:10px">
        <div id="StoryProgress">
          <div id="BarStory" class='brsy'></div>
        </div>
        <img class='st_pic fl' style="border-radius:50%;width:50px;height:50px;border:2px solid #fff">

        <div style="float: left; padding: 10px; color: #fff; font-size: 15px !important; font-family: cursive; top: 25px; left: 55px;"> <span class='st_topic'></span><br> <span class='st_time' style="font-size: 11px !important; color: darkgray; top: -6px; font-family: cursive; position: relative;"></span> </div>
        <div class='fr' style="right:10px">
          <i class='fa fa-trash supstory'
            style="display:none;color:#fff;margin-right:20px;font-size:20px !important"></i>
          <i class='storymuted fa fa-volume-up'
            style="display:none;color:#fff;margin-right:20px;font-size:20px !important"></i>
          <i class='storyplay fa fa-play'
            style="display:none;color:#fff;margin-right:20px;font-size:20px !important"></i>
          <i class='fa fa-close' onclick="HideStory()"
            style="color:#fff;font-size:20px !important;margin-right:10px"></i>
        </div>
        <div style="z-index: 11; font-size: 50px !important; color: #fff; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);" class='loadstory fa fa-spinner fa-pulse'></div>
        <video class='st_v' id="st_v_p" style="position: absolute; width: 100%; height: 100%; top: 0px; display:none; left: 0px; z-index: -1;" autoplay> <source> </video> <div class='st_p' style="position: absolute; width: 100%; height: 100%; top: 0px; display:none; left: 0px; z-index: -1; background-image: url(); background-repeat: no-repeat; background-position: center; background-size: contain;">
          <img id='st_p_t' src='' style="display:none">
        </div>
        <div class='st_view' style="position:absolute;bottom:10px;right:10px;color:#fff;font-size:13px !important">
        </div>
      </div>
</body>
  <script src="j.js?z" type="text/javascript"></script>
  <script src="js/so.js" type="text/javascript"></script>
  <script src="admin.js?x"></script>
</html>