import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

const config = {
  // Server Configuration
  server: {
    port: parseInt(process.env.PORT) || 9631,
    host: process.env.HOST || 'localhost',
    env: process.env.NODE_ENV || 'development',
    maxConnections: 1000,
    timeOffline: 400 // 20 hours in minutes
  },

  // Database Configuration
  database: {
    host: process.env.DB_HOST || 'localhost',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'gochat_db',
    port: parseInt(process.env.DB_PORT) || 3306,
    charset: 'utf8mb4',
    connectionLimit: 10,
    acquireTimeout: 60000,
    timeout: 60000
  },

  // Security Configuration
  security: {
    jwtSecret: process.env.JWT_SECRET || 'your-super-secret-key',
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS) || 12,
    sessionSecret: process.env.SESSION_SECRET || 'your-session-secret'
  },

  // SSL Configuration
  ssl: {
    enabled: process.env.SSL_ENABLED === 'true',
    keyPath: process.env.SSL_KEY_PATH || './pem/key.pem',
    certPath: process.env.SSL_CERT_PATH || './pem/cert.pem'
  },

  // File Upload Configuration
  upload: {
    maxFileSize: parseInt(process.env.MAX_FILE_SIZE) || 20 * 1024 * 1024, // 20MB
    uploadPath: process.env.UPLOAD_PATH || './uploads',
    allowedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    allowedVideoTypes: ['video/mp4', 'video/webm', 'video/mov', 'video/avi'],
    allowedAudioTypes: ['audio/mp3', 'audio/wav', 'audio/ogg']
  },

  // Rate Limiting
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 60000, // 1 minute
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100,
    skipSuccessfulRequests: false
  },

  // WebSocket Configuration
  websocket: {
    pingTimeout: parseInt(process.env.WS_PING_TIMEOUT) || 60000,
    pingInterval: parseInt(process.env.WS_PING_INTERVAL) || 25000,
    upgradeTimeout: 30000,
    maxHttpBufferSize: 1e8,
    connectionStateRecovery: {
      maxDisconnectionDuration: 2 * 60 * 1000,
      skipMiddlewares: true
    }
  },

  // CORS Configuration
  cors: {
    origin: process.env.ALLOWED_DOMAINS ? 
      process.env.ALLOWED_DOMAINS.split(',').map(domain => domain.trim()) : 
      ['http://localhost:9631', 'https://localhost:9631'],
    methods: ['GET', 'POST'],
    credentials: true
  },

  // Chat Configuration
  chat: {
    maxRooms: 500,
    maxUsers: 1000,
    maxMessages: 100,
    maxBroadcasts: 30,
    maxStates: 150,
    maxLogs: 300,
    maxFilters: 100,
    maxBots: 20,
    maxComments: 50,
    maxShortcuts: 3,
    timeLike: 1, // minutes
    maxDayMessage: 60 // minutes
  },

  // Admin Configuration
  admin: {
    username: process.env.ADMIN_USERNAME || 'admin',
    password: process.env.ADMIN_PASSWORD || 'admin123'
  },

  // Logging Configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || './logs/app.log',
    maxSize: '20m',
    maxFiles: '14d'
  },

  // External APIs
  apis: {
    youtube: {
      apiKey: process.env.YOUTUBE_API_KEY || ''
    }
  },

  // Default Power Settings
  defaultPower: {
    rank: 0,
    name: '',
    ico: '',
    kick: 0,
    delbc: 0,
    alert: 0,
    mynick: 0,
    unick: 0,
    ban: 0,
    publicmsg: 0,
    forcepm: 0,
    bootedit: 0,
    roomowner: 0,
    createroom: 0,
    rooms: 0,
    edituser: 0,
    setpower: 0,
    upgrades: 0,
    history: 0,
    cp: 0,
    stealth: 0,
    owner: 0,
    meiut: 0,
    loveu: 0,
    ulike: 0,
    flter: 0,
    subs: 0,
    shrt: 0,
    msgs: 0,
    broadcast: 0,
    camera: 0,
    grupes: 0,
    delcover: 0,
    report: 0,
    delmsg: 0,
    delpic: 0
  }
};

export default config;
