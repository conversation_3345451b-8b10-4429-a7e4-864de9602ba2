<!DOCTYPE html>
<html lang="ar" hreflang="ar-sa" style="height: 100%;">

<head>
  <meta content="text/html; charset=utf-8" http-equiv="Content-Type" />
  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
  <meta property="og:title" content="<%= title %>" />
  <meta property="og:description" content="<%= description %>" />
  <meta property="og:image" content="<%= logo %>" />
  <link rel="icon" type="image/x-icon" href="<%= logo %>" />
  <meta name="google" value="notranslate" />
  <meta name="HandheldFriendly" content="True" />
  <meta name="viewport" content=" user-scalable=0, width=device-width" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="mobile-web-app-capable" content="yes" />
  <title>
    <%= title %>
  </title>
  <meta name="description" content="<%= description %>" />
  <meta content="<%= keywords %>" name="keywords" />

<style>
 html{font-family:sans-serif;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}body{margin:0}article,aside,details,figcaption,figure,footer,header,hgroup,main,menu,nav,section,summary{display:block}audio,canvas,progress,video{display:inline-block;vertical-align:baseline}audio:not([controls]){display:none;height:0}[hidden],template{display:none}a{background-color:transparent}a:active,a:hover{outline:0}abbr[title]{border-bottom:none;text-decoration:underline;text-decoration:underline dotted}b,strong{font-weight:bold}dfn{font-style:italic}h1{font-size:2em;margin:0.67em 0}mark{background:#ff0;color:#000}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sup{top:-0.5em}sub{bottom:-0.25em}img{border:0}svg:not(:root){overflow:hidden}figure{margin:1em 40px}hr{-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;height:0}pre{overflow:auto}code,kbd,pre,samp{font-family:monospace, monospace;font-size:1em}button,input,optgroup,select,textarea{color:inherit;font:inherit;margin:0}button{overflow:visible}button,select{text-transform:none}button,html input[type="button"],input[type="reset"],input[type="submit"]{-webkit-appearance:button;cursor:pointer}button[disabled],html input[disabled]{cursor:default}button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}input{line-height:normal}input[type="checkbox"],input[type="radio"]{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;padding:0}input[type="number"]::-webkit-inner-spin-button,input[type="number"]::-webkit-outer-spin-button{height:auto}input[type="search"]{-webkit-appearance:textfield;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box}input[type="search"]::-webkit-search-cancel-button,input[type="search"]::-webkit-search-decoration{-webkit-appearance:none}fieldset{border:1px solid #c0c0c0;margin:0 2px;padding:0.35em 0.625em 0.75em}legend{border:0;padding:0}textarea{overflow:auto}optgroup{font-weight:bold}table{border-collapse:collapse;border-spacing:0}td,th{padding:0}*{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}*:before,*:after{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}html{font-size:10px;-webkit-tap-highlight-color:rgba(0,0,0,0)}body{font-family:"Helvetica Neue",Helvetica,Arial,sans-serif;font-size:14px;line-height:1.42857143;color:#333;background-color:#fff}input,button,select,textarea{font-family:inherit;font-size:inherit;line-height:inherit}a{color:#337ab7;text-decoration:none}a:hover,a:focus{color:#23527c;text-decoration:underline}a:focus{outline:5px auto -webkit-focus-ring-color;outline-offset:-2px}figure{margin:0}img{vertical-align:middle}.img-responsive{display:block;max-width:100%;height:auto}.img-rounded{border-radius:6px}.img-thumbnail{padding:4px;line-height:1.42857143;background-color:#fff;border:1px solid #ddd;border-radius:4px;-webkit-transition:all .2s ease-in-out;-o-transition:all .2s ease-in-out;transition:all .2s ease-in-out;display:inline-block;max-width:100%;height:auto}.img-circle{border-radius:50%}hr{margin-top:20px;margin-bottom:20px;border:0;border-top:1px solid #eee}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);border:0}.sr-only-focusable:active,.sr-only-focusable:focus{position:static;width:auto;height:auto;margin:0;overflow:visible;clip:auto}[role="button"]{cursor:pointer}fieldset{min-width:0;padding:0;margin:0;border:0}legend{display:block;width:100%;padding:0;margin-bottom:20px;font-size:21px;line-height:inherit;color:#333;border:0;border-bottom:1px solid #e5e5e5}label{display:inline-block;max-width:100%;margin-bottom:5px;font-weight:700}input[type="search"]{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:none;appearance:none}input[type="radio"],input[type="checkbox"]{margin:4px 0 0;margin-top:1px \9;line-height:normal}input[type="radio"][disabled],input[type="checkbox"][disabled],input[type="radio"].disabled,input[type="checkbox"].disabled,fieldset[disabled] input[type="radio"],fieldset[disabled] input[type="checkbox"]{cursor:not-allowed}input[type="file"]{display:block}input[type="range"]{display:block;width:100%}select[multiple],select[size]{height:auto}input[type="file"]:focus,input[type="radio"]:focus,input[type="checkbox"]:focus{outline:5px auto -webkit-focus-ring-color;outline-offset:-2px}output{display:block;padding-top:7px;font-size:14px;line-height:1.42857143;color:#555}.form-control{display:block;width:100%;height:34px;padding:6px 12px;font-size:14px;line-height:1.42857143;color:#555;background-color:#fff;background-image:none;border:1px solid #ccc;border-radius:4px;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);-webkit-transition:border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;-o-transition:border-color ease-in-out .15s, box-shadow ease-in-out .15s;transition:border-color ease-in-out .15s, box-shadow ease-in-out .15s}.form-control:focus{border-color:#66afe9;outline:0;-webkit-box-shadow:inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, 0.6);box-shadow:inset 0 1px 1px rgba(0, 0, 0, .075), 0 0 8px rgba(102, 175, 233, 0.6)}.form-control::-moz-placeholder{color:#999;opacity:1}.form-control:-ms-input-placeholder{color:#999}.form-control::-webkit-input-placeholder{color:#999}.form-control::-ms-expand{background-color:transparent;border:0}.form-control[disabled],.form-control[readonly],fieldset[disabled] .form-control{background-color:#eee;opacity:1}.form-control[disabled],fieldset[disabled] .form-control{cursor:not-allowed}textarea.form-control{height:auto}@media screen and (-webkit-min-device-pixel-ratio:0){input[type="date"].form-control,input[type="time"].form-control,input[type="datetime-local"].form-control,input[type="month"].form-control{line-height:34px}input[type="date"].input-sm,input[type="time"].input-sm,input[type="datetime-local"].input-sm,input[type="month"].input-sm,.input-group-sm input[type="date"],.input-group-sm input[type="time"],.input-group-sm input[type="datetime-local"],.input-group-sm input[type="month"]{line-height:30px}input[type="date"].input-lg,input[type="time"].input-lg,input[type="datetime-local"].input-lg,input[type="month"].input-lg,.input-group-lg input[type="date"],.input-group-lg input[type="time"],.input-group-lg input[type="datetime-local"],.input-group-lg input[type="month"]{line-height:46px}}.form-group{margin-bottom:15px}.radio,.checkbox{position:relative;display:block;margin-top:10px;margin-bottom:10px}.radio.disabled label,.checkbox.disabled label,fieldset[disabled] .radio label,fieldset[disabled] .checkbox label{cursor:not-allowed}.radio label,.checkbox label{min-height:20px;padding-left:20px;margin-bottom:0;font-weight:400;cursor:pointer}.radio input[type="radio"],.radio-inline input[type="radio"],.checkbox input[type="checkbox"],.checkbox-inline input[type="checkbox"]{position:absolute;margin-top:4px \9;margin-left:-20px}.radio+.radio,.checkbox+.checkbox{margin-top:-5px}.radio-inline,.checkbox-inline{position:relative;display:inline-block;padding-left:20px;margin-bottom:0;font-weight:400;vertical-align:middle;cursor:pointer}.radio-inline.disabled,.checkbox-inline.disabled,fieldset[disabled] .radio-inline,fieldset[disabled] .checkbox-inline{cursor:not-allowed}.radio-inline+.radio-inline,.checkbox-inline+.checkbox-inline{margin-top:0;margin-left:10px}.form-control-static{min-height:34px;padding-top:7px;padding-bottom:7px;margin-bottom:0}.form-control-static.input-lg,.form-control-static.input-sm{padding-right:0;padding-left:0}.input-sm{height:30px;padding:5px 10px;font-size:12px;line-height:1.5;border-radius:3px}select.input-sm{height:30px;line-height:30px}textarea.input-sm,select[multiple].input-sm{height:auto}.form-group-sm .form-control{height:30px;padding:5px 10px;font-size:12px;line-height:1.5;border-radius:3px}.form-group-sm select.form-control{height:30px;line-height:30px}.form-group-sm textarea.form-control,.form-group-sm select[multiple].form-control{height:auto}.form-group-sm .form-control-static{height:30px;min-height:32px;padding:6px 10px;font-size:12px;line-height:1.5}.input-lg{height:46px;padding:10px 16px;font-size:18px;line-height:1.3333333;border-radius:6px}select.input-lg{height:46px;line-height:46px}textarea.input-lg,select[multiple].input-lg{height:auto}.form-group-lg .form-control{height:46px;padding:10px 16px;font-size:18px;line-height:1.3333333;border-radius:6px}.form-group-lg select.form-control{height:46px;line-height:46px}.form-group-lg textarea.form-control,.form-group-lg select[multiple].form-control{height:auto}.form-group-lg .form-control-static{height:46px;min-height:38px;padding:11px 16px;font-size:18px;line-height:1.3333333}.has-feedback{position:relative}.has-feedback .form-control{padding-right:42.5px}.form-control-feedback{position:absolute;top:0;right:0;z-index:2;display:block;width:34px;height:34px;line-height:34px;text-align:center;pointer-events:none}.input-lg+.form-control-feedback,.input-group-lg+.form-control-feedback,.form-group-lg .form-control+.form-control-feedback{width:46px;height:46px;line-height:46px}.input-sm+.form-control-feedback,.input-group-sm+.form-control-feedback,.form-group-sm .form-control+.form-control-feedback{width:30px;height:30px;line-height:30px}.has-success .help-block,.has-success .control-label,.has-success .radio,.has-success .checkbox,.has-success .radio-inline,.has-success .checkbox-inline,.has-success.radio label,.has-success.checkbox label,.has-success.radio-inline label,.has-success.checkbox-inline label{color:#3c763d}.has-success .form-control{border-color:#3c763d;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);box-shadow:inset 0 1px 1px rgba(0,0,0,0.075)}.has-success .form-control:focus{border-color:#2b542c;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 6px #67b168;box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 6px #67b168}.has-success .input-group-addon{color:#3c763d;background-color:#dff0d8;border-color:#3c763d}.has-success .form-control-feedback{color:#3c763d}.has-warning .help-block,.has-warning .control-label,.has-warning .radio,.has-warning .checkbox,.has-warning .radio-inline,.has-warning .checkbox-inline,.has-warning.radio label,.has-warning.checkbox label,.has-warning.radio-inline label,.has-warning.checkbox-inline label{color:#8a6d3b}.has-warning .form-control{border-color:#8a6d3b;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);box-shadow:inset 0 1px 1px rgba(0,0,0,0.075)}.has-warning .form-control:focus{border-color:#66512c;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 6px #c0a16b;box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 6px #c0a16b}.has-warning .input-group-addon{color:#8a6d3b;background-color:#fcf8e3;border-color:#8a6d3b}.has-warning .form-control-feedback{color:#8a6d3b}.has-error .help-block,.has-error .control-label,.has-error .radio,.has-error .checkbox,.has-error .radio-inline,.has-error .checkbox-inline,.has-error.radio label,.has-error.checkbox label,.has-error.radio-inline label,.has-error.checkbox-inline label{color:#a94442}.has-error .form-control{border-color:#a94442;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075);box-shadow:inset 0 1px 1px rgba(0,0,0,0.075)}.has-error .form-control:focus{border-color:#843534;-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 6px #ce8483;box-shadow:inset 0 1px 1px rgba(0,0,0,0.075),0 0 6px #ce8483}.has-error .input-group-addon{color:#a94442;background-color:#f2dede;border-color:#a94442}.has-error .form-control-feedback{color:#a94442}.has-feedback label~.form-control-feedback{top:25px}.has-feedback label.sr-only~.form-control-feedback{top:0}.help-block{display:block;margin-top:5px;margin-bottom:10px;color:#737373}@media (min-width:768px){.form-inline .form-group{display:inline-block;margin-bottom:0;vertical-align:middle}.form-inline .form-control{display:inline-block;width:auto;vertical-align:middle}.form-inline .form-control-static{display:inline-block}.form-inline .input-group{display:inline-table;vertical-align:middle}.form-inline .input-group .input-group-addon,.form-inline .input-group .input-group-btn,.form-inline .input-group .form-control{width:auto}.form-inline .input-group>.form-control{width:100%}.form-inline .control-label{margin-bottom:0;vertical-align:middle}.form-inline .radio,.form-inline .checkbox{display:inline-block;margin-top:0;margin-bottom:0;vertical-align:middle}.form-inline .radio label,.form-inline .checkbox label{padding-left:0}.form-inline .radio input[type="radio"],.form-inline .checkbox input[type="checkbox"]{position:relative;margin-left:0}.form-inline .has-feedback .form-control-feedback{top:0}}.form-horizontal .radio,.form-horizontal .checkbox,.form-horizontal .radio-inline,.form-horizontal .checkbox-inline{padding-top:7px;margin-top:0;margin-bottom:0}.form-horizontal .radio,.form-horizontal .checkbox{min-height:27px}.form-horizontal .form-group{margin-right:-15px;margin-left:-15px}@media (min-width:768px){.form-horizontal .control-label{padding-top:7px;margin-bottom:0;text-align:right}}.form-horizontal .has-feedback .form-control-feedback{right:15px}@media (min-width:768px){.form-horizontal .form-group-lg .control-label{padding-top:11px;font-size:18px}}@media (min-width:768px){.form-horizontal .form-group-sm .control-label{padding-top:6px;font-size:12px}}.btn{display:inline-block;margin-bottom:0;font-weight:normal;text-align:center;white-space:nowrap;vertical-align:middle;-ms-touch-action:manipulation;touch-action:manipulation;cursor:pointer;background-image:none;border:1px solid transparent;padding:6px 12px;font-size:14px;line-height:1.42857143;border-radius:4px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.btn:focus,.btn:active:focus,.btn.active:focus,.btn.focus,.btn:active.focus,.btn.active.focus{outline:5px auto -webkit-focus-ring-color;outline-offset:-2px}.btn:hover,.btn:focus,.btn.focus{color:#333;text-decoration:none}.btn:active,.btn.active{background-image:none;outline:0;-webkit-box-shadow:inset 0 3px 5px rgba(0,0,0,0.125);box-shadow:inset 0 3px 5px rgba(0,0,0,0.125)}.btn.disabled,.btn[disabled],fieldset[disabled] .btn{cursor:not-allowed;filter:alpha(opacity=65);opacity:.65;-webkit-box-shadow:none;box-shadow:none}a.btn.disabled,fieldset[disabled] a.btn{pointer-events:none}.btn-default{color:#333;background-color:#fff;border-color:#ccc}.btn-default:focus,.btn-default.focus{color:#333;background-color:#e6e6e6;border-color:#8c8c8c}.btn-default:hover{color:#333;background-color:#e6e6e6;border-color:#adadad}.btn-default:active,.btn-default.active,.open>.dropdown-toggle.btn-default{color:#333;background-color:#e6e6e6;background-image:none;border-color:#adadad}.btn-default:active:hover,.btn-default.active:hover,.open>.dropdown-toggle.btn-default:hover,.btn-default:active:focus,.btn-default.active:focus,.open>.dropdown-toggle.btn-default:focus,.btn-default:active.focus,.btn-default.active.focus,.open>.dropdown-toggle.btn-default.focus{color:#333;background-color:#d4d4d4;border-color:#8c8c8c}.btn-default.disabled:hover,.btn-default[disabled]:hover,fieldset[disabled] .btn-default:hover,.btn-default.disabled:focus,.btn-default[disabled]:focus,fieldset[disabled] .btn-default:focus,.btn-default.disabled.focus,.btn-default[disabled].focus,fieldset[disabled] .btn-default.focus{background-color:#fff;border-color:#ccc}.btn-default .badge{color:#fff;background-color:#333}.btn-primary{color:#fff;background-color:#337ab7;border-color:#2e6da4}.btn-primary:focus,.btn-primary.focus{color:#fff;background-color:#286090;border-color:#122b40}.btn-primary:hover{color:#fff;background-color:#286090;border-color:#204d74}.btn-primary:active,.btn-primary.active,.open>.dropdown-toggle.btn-primary{color:#fff;background-color:#286090;background-image:none;border-color:#204d74}.btn-primary:active:hover,.btn-primary.active:hover,.open>.dropdown-toggle.btn-primary:hover,.btn-primary:active:focus,.btn-primary.active:focus,.open>.dropdown-toggle.btn-primary:focus,.btn-primary:active.focus,.btn-primary.active.focus,.open>.dropdown-toggle.btn-primary.focus{color:#fff;background-color:#204d74;border-color:#122b40}.btn-primary.disabled:hover,.btn-primary[disabled]:hover,fieldset[disabled] .btn-primary:hover,.btn-primary.disabled:focus,.btn-primary[disabled]:focus,fieldset[disabled] .btn-primary:focus,.btn-primary.disabled.focus,.btn-primary[disabled].focus,fieldset[disabled] .btn-primary.focus{background-color:#337ab7;border-color:#2e6da4}.btn-primary .badge{color:#337ab7;background-color:#fff}.btn-success{color:#fff;background-color:#5cb85c;border-color:#4cae4c}.btn-success:focus,.btn-success.focus{color:#fff;background-color:#449d44;border-color:#255625}.btn-success:hover{color:#fff;background-color:#449d44;border-color:#398439}.btn-success:active,.btn-success.active,.open>.dropdown-toggle.btn-success{color:#fff;background-color:#449d44;background-image:none;border-color:#398439}.btn-success:active:hover,.btn-success.active:hover,.open>.dropdown-toggle.btn-success:hover,.btn-success:active:focus,.btn-success.active:focus,.open>.dropdown-toggle.btn-success:focus,.btn-success:active.focus,.btn-success.active.focus,.open>.dropdown-toggle.btn-success.focus{color:#fff;background-color:#398439;border-color:#255625}.btn-success.disabled:hover,.btn-success[disabled]:hover,fieldset[disabled] .btn-success:hover,.btn-success.disabled:focus,.btn-success[disabled]:focus,fieldset[disabled] .btn-success:focus,.btn-success.disabled.focus,.btn-success[disabled].focus,fieldset[disabled] .btn-success.focus{background-color:#5cb85c;border-color:#4cae4c}.btn-success .badge{color:#5cb85c;background-color:#fff}.btn-info{color:#fff;background-color:#5bc0de;border-color:#46b8da}.btn-info:focus,.btn-info.focus{color:#fff;background-color:#31b0d5;border-color:#1b6d85}.btn-info:hover{color:#fff;background-color:#31b0d5;border-color:#269abc}.btn-info:active,.btn-info.active,.open>.dropdown-toggle.btn-info{color:#fff;background-color:#31b0d5;background-image:none;border-color:#269abc}.btn-info:active:hover,.btn-info.active:hover,.open>.dropdown-toggle.btn-info:hover,.btn-info:active:focus,.btn-info.active:focus,.open>.dropdown-toggle.btn-info:focus,.btn-info:active.focus,.btn-info.active.focus,.open>.dropdown-toggle.btn-info.focus{color:#fff;background-color:#269abc;border-color:#1b6d85}.btn-info.disabled:hover,.btn-info[disabled]:hover,fieldset[disabled] .btn-info:hover,.btn-info.disabled:focus,.btn-info[disabled]:focus,fieldset[disabled] .btn-info:focus,.btn-info.disabled.focus,.btn-info[disabled].focus,fieldset[disabled] .btn-info.focus{background-color:#5bc0de;border-color:#46b8da}.btn-info .badge{color:#5bc0de;background-color:#fff}.btn-warning{color:#fff;background-color:#f0ad4e;border-color:#eea236}.btn-warning:focus,.btn-warning.focus{color:#fff;background-color:#ec971f;border-color:#985f0d}.btn-warning:hover{color:#fff;background-color:#ec971f;border-color:#d58512}.btn-warning:active,.btn-warning.active,.open>.dropdown-toggle.btn-warning{color:#fff;background-color:#ec971f;background-image:none;border-color:#d58512}.btn-warning:active:hover,.btn-warning.active:hover,.open>.dropdown-toggle.btn-warning:hover,.btn-warning:active:focus,.btn-warning.active:focus,.open>.dropdown-toggle.btn-warning:focus,.btn-warning:active.focus,.btn-warning.active.focus,.open>.dropdown-toggle.btn-warning.focus{color:#fff;background-color:#d58512;border-color:#985f0d}.btn-warning.disabled:hover,.btn-warning[disabled]:hover,fieldset[disabled] .btn-warning:hover,.btn-warning.disabled:focus,.btn-warning[disabled]:focus,fieldset[disabled] .btn-warning:focus,.btn-warning.disabled.focus,.btn-warning[disabled].focus,fieldset[disabled] .btn-warning.focus{background-color:#f0ad4e;border-color:#eea236}.btn-warning .badge{color:#f0ad4e;background-color:#fff}.btn-danger{color:#fff;background-color:#d9534f;border-color:#d43f3a}.btn-danger:focus,.btn-danger.focus{color:#fff;background-color:#c9302c;border-color:#761c19}.btn-danger:hover{color:#fff;background-color:#c9302c;border-color:#ac2925}.btn-danger:active,.btn-danger.active,.open>.dropdown-toggle.btn-danger{color:#fff;background-color:#c9302c;background-image:none;border-color:#ac2925}.btn-danger:active:hover,.btn-danger.active:hover,.open>.dropdown-toggle.btn-danger:hover,.btn-danger:active:focus,.btn-danger.active:focus,.open>.dropdown-toggle.btn-danger:focus,.btn-danger:active.focus,.btn-danger.active.focus,.open>.dropdown-toggle.btn-danger.focus{color:#fff;background-color:#ac2925;border-color:#761c19}.btn-danger.disabled:hover,.btn-danger[disabled]:hover,fieldset[disabled] .btn-danger:hover,.btn-danger.disabled:focus,.btn-danger[disabled]:focus,fieldset[disabled] .btn-danger:focus,.btn-danger.disabled.focus,.btn-danger[disabled].focus,fieldset[disabled] .btn-danger.focus{background-color:#d9534f;border-color:#d43f3a}.btn-danger .badge{color:#d9534f;background-color:#fff}.btn-link{font-weight:400;color:#337ab7;border-radius:0}.btn-link,.btn-link:active,.btn-link.active,.btn-link[disabled],fieldset[disabled] .btn-link{background-color:transparent;-webkit-box-shadow:none;box-shadow:none}.btn-link,.btn-link:hover,.btn-link:focus,.btn-link:active{border-color:transparent}.btn-link:hover,.btn-link:focus{color:#23527c;text-decoration:underline;background-color:transparent}.btn-link[disabled]:hover,fieldset[disabled] .btn-link:hover,.btn-link[disabled]:focus,fieldset[disabled] .btn-link:focus{color:#777;text-decoration:none}.btn-lg,.btn-group-lg>.btn{padding:10px 16px;font-size:18px;line-height:1.3333333;border-radius:6px}.btn-sm,.btn-group-sm>.btn{padding:5px 10px;font-size:12px;line-height:1.5;border-radius:3px}.btn-xs,.btn-group-xs>.btn{padding:1px 5px;font-size:12px;line-height:1.5;border-radius:3px}.btn-block{display:block;width:100%}.btn-block+.btn-block{margin-top:5px}input[type="submit"].btn-block,input[type="reset"].btn-block,input[type="button"].btn-block{width:100%}.btn-group,.btn-group-vertical{position:relative;display:inline-block;vertical-align:middle}.btn-group>.btn,.btn-group-vertical>.btn{position:relative;float:left}.btn-group>.btn:hover,.btn-group-vertical>.btn:hover,.btn-group>.btn:focus,.btn-group-vertical>.btn:focus,.btn-group>.btn:active,.btn-group-vertical>.btn:active,.btn-group>.btn.active,.btn-group-vertical>.btn.active{z-index:2}.btn-group .btn+.btn,.btn-group .btn+.btn-group,.btn-group .btn-group+.btn,.btn-group .btn-group+.btn-group{margin-left:-1px}.btn-toolbar{margin-left:-5px}.btn-toolbar .btn,.btn-toolbar .btn-group,.btn-toolbar .input-group{float:left}.btn-toolbar>.btn,.btn-toolbar>.btn-group,.btn-toolbar>.input-group{margin-left:5px}.btn-group>.btn:not(:first-child):not(:last-child):not(.dropdown-toggle){border-radius:0}.btn-group>.btn:first-child{margin-left:0}.btn-group>.btn:first-child:not(:last-child):not(.dropdown-toggle){border-top-right-radius:0;border-bottom-right-radius:0}.btn-group>.btn:last-child:not(:first-child),.btn-group>.dropdown-toggle:not(:first-child){border-top-left-radius:0;border-bottom-left-radius:0}.btn-group>.btn-group{float:left}.btn-group>.btn-group:not(:first-child):not(:last-child)>.btn{border-radius:0}.btn-group>.btn-group:first-child:not(:last-child)>.btn:last-child,.btn-group>.btn-group:first-child:not(:last-child)>.dropdown-toggle{border-top-right-radius:0;border-bottom-right-radius:0}.btn-group>.btn-group:last-child:not(:first-child)>.btn:first-child{border-top-left-radius:0;border-bottom-left-radius:0}.btn-group .dropdown-toggle:active,.btn-group.open .dropdown-toggle{outline:0}.btn-group>.btn+.dropdown-toggle{padding-right:8px;padding-left:8px}.btn-group>.btn-lg+.dropdown-toggle{padding-right:12px;padding-left:12px}.btn-group.open .dropdown-toggle{-webkit-box-shadow:inset 0 3px 5px rgba(0,0,0,0.125);box-shadow:inset 0 3px 5px rgba(0,0,0,0.125)}.btn-group.open .dropdown-toggle.btn-link{-webkit-box-shadow:none;box-shadow:none}.btn .caret{margin-left:0}.btn-lg .caret{border-width:5px 5px 0;border-bottom-width:0}.dropup .btn-lg .caret{border-width:0 5px 5px}.btn-group-vertical>.btn,.btn-group-vertical>.btn-group,.btn-group-vertical>.btn-group>.btn{display:block;float:none;width:100%;max-width:100%}.btn-group-vertical>.btn-group>.btn{float:none}.btn-group-vertical>.btn+.btn,.btn-group-vertical>.btn+.btn-group,.btn-group-vertical>.btn-group+.btn,.btn-group-vertical>.btn-group+.btn-group{margin-top:-1px;margin-left:0}.btn-group-vertical>.btn:not(:first-child):not(:last-child){border-radius:0}.btn-group-vertical>.btn:first-child:not(:last-child){border-top-left-radius:4px;border-top-right-radius:4px;border-bottom-right-radius:0;border-bottom-left-radius:0}.btn-group-vertical>.btn:last-child:not(:first-child){border-top-left-radius:0;border-top-right-radius:0;border-bottom-right-radius:4px;border-bottom-left-radius:4px}.btn-group-vertical>.btn-group:not(:first-child):not(:last-child)>.btn{border-radius:0}.btn-group-vertical>.btn-group:first-child:not(:last-child)>.btn:last-child,.btn-group-vertical>.btn-group:first-child:not(:last-child)>.dropdown-toggle{border-bottom-right-radius:0;border-bottom-left-radius:0}.btn-group-vertical>.btn-group:last-child:not(:first-child)>.btn:first-child{border-top-left-radius:0;border-top-right-radius:0}.btn-group-justified{display:table;width:100%;table-layout:fixed;border-collapse:separate}.btn-group-justified>.btn,.btn-group-justified>.btn-group{display:table-cell;float:none;width:1%}.btn-group-justified>.btn-group .btn{width:100%}.btn-group-justified>.btn-group .dropdown-menu{left:auto}[data-toggle="buttons"]>.btn input[type="radio"],[data-toggle="buttons"]>.btn-group>.btn input[type="radio"],[data-toggle="buttons"]>.btn input[type="checkbox"],[data-toggle="buttons"]>.btn-group>.btn input[type="checkbox"]{position:absolute;clip:rect(0, 0, 0, 0);pointer-events:none}.input-group{position:relative;display:table;border-collapse:separate}.input-group[class*="col-"]{float:none;padding-right:0;padding-left:0}.input-group .form-control{position:relative;z-index:2;float:left;width:100%;margin-bottom:0}.input-group .form-control:focus{z-index:3}.input-group-lg>.form-control,.input-group-lg>.input-group-addon,.input-group-lg>.input-group-btn>.btn{height:46px;padding:10px 16px;font-size:18px;line-height:1.3333333;border-radius:6px}select.input-group-lg>.form-control,select.input-group-lg>.input-group-addon,select.input-group-lg>.input-group-btn>.btn{height:46px;line-height:46px}textarea.input-group-lg>.form-control,textarea.input-group-lg>.input-group-addon,textarea.input-group-lg>.input-group-btn>.btn,select[multiple].input-group-lg>.form-control,select[multiple].input-group-lg>.input-group-addon,select[multiple].input-group-lg>.input-group-btn>.btn{height:auto}.input-group-sm>.form-control,.input-group-sm>.input-group-addon,.input-group-sm>.input-group-btn>.btn{height:30px;padding:5px 10px;font-size:12px;line-height:1.5;border-radius:3px}select.input-group-sm>.form-control,select.input-group-sm>.input-group-addon,select.input-group-sm>.input-group-btn>.btn{height:30px;line-height:30px}textarea.input-group-sm>.form-control,textarea.input-group-sm>.input-group-addon,textarea.input-group-sm>.input-group-btn>.btn,select[multiple].input-group-sm>.form-control,select[multiple].input-group-sm>.input-group-addon,select[multiple].input-group-sm>.input-group-btn>.btn{height:auto}.input-group-addon,.input-group-btn,.input-group .form-control{display:table-cell}.input-group-addon:not(:first-child):not(:last-child),.input-group-btn:not(:first-child):not(:last-child),.input-group .form-control:not(:first-child):not(:last-child){border-radius:0}.input-group-addon,.input-group-btn{width:1%;white-space:nowrap;vertical-align:middle}.input-group-addon{padding:6px 12px;font-size:14px;font-weight:400;line-height:1;color:#555;text-align:center;background-color:#eee;border:1px solid #ccc;border-radius:4px}.input-group-addon.input-sm{padding:5px 10px;font-size:12px;border-radius:3px}.input-group-addon.input-lg{padding:10px 16px;font-size:18px;border-radius:6px}.input-group-addon input[type="radio"],.input-group-addon input[type="checkbox"]{margin-top:0}.input-group .form-control:first-child,.input-group-addon:first-child,.input-group-btn:first-child>.btn,.input-group-btn:first-child>.btn-group>.btn,.input-group-btn:first-child>.dropdown-toggle,.input-group-btn:last-child>.btn:not(:last-child):not(.dropdown-toggle),.input-group-btn:last-child>.btn-group:not(:last-child)>.btn{border-top-right-radius:0;border-bottom-right-radius:0}.input-group-addon:first-child{border-right:0}.input-group .form-control:last-child,.input-group-addon:last-child,.input-group-btn:last-child>.btn,.input-group-btn:last-child>.btn-group>.btn,.input-group-btn:last-child>.dropdown-toggle,.input-group-btn:first-child>.btn:not(:first-child),.input-group-btn:first-child>.btn-group:not(:first-child)>.btn{border-top-left-radius:0;border-bottom-left-radius:0}.input-group-addon:last-child{border-left:0}.input-group-btn{position:relative;font-size:0;white-space:nowrap}.input-group-btn>.btn{position:relative}.input-group-btn>.btn+.btn{margin-left:-1px}.input-group-btn>.btn:hover,.input-group-btn>.btn:focus,.input-group-btn>.btn:active{z-index:2}.input-group-btn:first-child>.btn,.input-group-btn:first-child>.btn-group{margin-right:-1px}.input-group-btn:last-child>.btn,.input-group-btn:last-child>.btn-group{z-index:2;margin-left:-1px}.nav{padding-left:0;margin-bottom:0;list-style:none}.nav>li{position:relative;display:block}.nav>li>a{position:relative;display:block;padding:10px 15px}.nav>li>a:hover,.nav>li>a:focus{text-decoration:none;background-color:#eee}.nav>li.disabled>a{color:#777}.nav>li.disabled>a:hover,.nav>li.disabled>a:focus{color:#777;text-decoration:none;cursor:not-allowed;background-color:transparent}.nav .open>a,.nav .open>a:hover,.nav .open>a:focus{background-color:#eee;border-color:#337ab7}.nav .nav-divider{height:1px;margin:9px 0;overflow:hidden;background-color:#e5e5e5}.nav>li>a>img{max-width:none}.nav-tabs{border-bottom:1px solid #ddd}.nav-tabs>li{float:left;margin-bottom:-1px}.nav-tabs>li>a{margin-right:2px;line-height:1.42857143;border:1px solid transparent;border-radius:4px 4px 0 0}.nav-tabs>li>a:hover{border-color:#eee #eee #ddd}.nav-tabs>li.active>a,.nav-tabs>li.active>a:hover,.nav-tabs>li.active>a:focus{color:#555;cursor:default;background-color:#fff;border:1px solid #ddd;border-bottom-color:transparent}.nav-tabs.nav-justified{width:100%;border-bottom:0}.nav-tabs.nav-justified>li{float:none}.nav-tabs.nav-justified>li>a{margin-bottom:5px;text-align:center}.nav-tabs.nav-justified>.dropdown .dropdown-menu{top:auto;left:auto}@media (min-width:768px){.nav-tabs.nav-justified>li{display:table-cell;width:1%}.nav-tabs.nav-justified>li>a{margin-bottom:0}}.nav-tabs.nav-justified>li>a{margin-right:0;border-radius:4px}.nav-tabs.nav-justified>.active>a,.nav-tabs.nav-justified>.active>a:hover,.nav-tabs.nav-justified>.active>a:focus{border:1px solid #ddd}@media (min-width:768px){.nav-tabs.nav-justified>li>a{border-bottom:1px solid #ddd;border-radius:4px 4px 0 0}.nav-tabs.nav-justified>.active>a,.nav-tabs.nav-justified>.active>a:hover,.nav-tabs.nav-justified>.active>a:focus{border-bottom-color:#fff}}.nav-pills>li{float:left}.nav-pills>li>a{border-radius:4px}.nav-pills>li+li{margin-left:2px}.nav-pills>li.active>a,.nav-pills>li.active>a:hover,.nav-pills>li.active>a:focus{color:#fff;background-color:#337ab7}.nav-stacked>li{float:none}.nav-stacked>li+li{margin-top:2px;margin-left:0}.nav-justified{width:100%}.nav-justified>li{float:none}.nav-justified>li>a{margin-bottom:5px;text-align:center}.nav-justified>.dropdown .dropdown-menu{top:auto;left:auto}@media (min-width:768px){.nav-justified>li{display:table-cell;width:1%}.nav-justified>li>a{margin-bottom:0}}.nav-tabs-justified{border-bottom:0}.nav-tabs-justified>li>a{margin-right:0;border-radius:4px}.nav-tabs-justified>.active>a,.nav-tabs-justified>.active>a:hover,.nav-tabs-justified>.active>a:focus{border:1px solid #ddd}@media (min-width:768px){.nav-tabs-justified>li>a{border-bottom:1px solid #ddd;border-radius:4px 4px 0 0}.nav-tabs-justified>.active>a,.nav-tabs-justified>.active>a:hover,.nav-tabs-justified>.active>a:focus{border-bottom-color:#fff}}.tab-content>.tab-pane{display:none}.tab-content>.active{display:block}.nav-tabs .dropdown-menu{margin-top:-1px;border-top-left-radius:0;border-top-right-radius:0}.label{display:inline;padding:.2em .6em .3em;font-size:75%;font-weight:700;line-height:1;color:#fff;text-align:center;white-space:nowrap;vertical-align:baseline;border-radius:.25em}a.label:hover,a.label:focus{color:#fff;text-decoration:none;cursor:pointer}.label:empty{display:none}.btn .label{position:relative;top:-1px}.label-default{background-color:#777}.label-default[href]:hover,.label-default[href]:focus{background-color:#5e5e5e}.label-primary{background-color:#337ab7}.label-primary[href]:hover,.label-primary[href]:focus{background-color:#286090}.label-success{background-color:#5cb85c}.label-success[href]:hover,.label-success[href]:focus{background-color:#449d44}.label-info{background-color:#5bc0de}.label-info[href]:hover,.label-info[href]:focus{background-color:#31b0d5}.label-warning{background-color:#f0ad4e}.label-warning[href]:hover,.label-warning[href]:focus{background-color:#ec971f}.label-danger{background-color:#d9534f}.label-danger[href]:hover,.label-danger[href]:focus{background-color:#c9302c}.badge{display:inline-block;min-width:10px;padding:3px 7px;font-size:12px;font-weight:bold;line-height:1;color:#fff;text-align:center;white-space:nowrap;vertical-align:middle;background-color:#777;border-radius:10px}.badge:empty{display:none}.btn .badge{position:relative;top:-1px}.btn-xs .badge,.btn-group-xs>.btn .badge{top:0;padding:1px 5px}a.badge:hover,a.badge:focus{color:#fff;text-decoration:none;cursor:pointer}.list-group-item.active>.badge,.nav-pills>.active>a>.badge{color:#337ab7;background-color:#fff}.list-group-item>.badge{float:right}.list-group-item>.badge+.badge{margin-right:5px}.nav-pills>li>a>.badge{margin-left:3px}.panel{margin-bottom:20px;background-color:#fff;border:1px solid transparent;border-radius:4px;-webkit-box-shadow:0 1px 1px rgba(0,0,0,0.05);box-shadow:0 1px 1px rgba(0,0,0,0.05)}.panel-body{padding:15px}.panel-heading{padding:10px 15px;border-bottom:1px solid transparent;border-top-left-radius:3px;border-top-right-radius:3px}.panel-heading>.dropdown .dropdown-toggle{color:inherit}.panel-title{margin-top:0;margin-bottom:0;font-size:16px;color:inherit}.panel-title>a,.panel-title>small,.panel-title>.small,.panel-title>small>a,.panel-title>.small>a{color:inherit}.panel-footer{padding:10px 15px;background-color:#f5f5f5;border-top:1px solid #ddd;border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.list-group,.panel>.panel-collapse>.list-group{margin-bottom:0}.panel>.list-group .list-group-item,.panel>.panel-collapse>.list-group .list-group-item{border-width:1px 0;border-radius:0}.panel>.list-group:first-child .list-group-item:first-child,.panel>.panel-collapse>.list-group:first-child .list-group-item:first-child{border-top:0;border-top-left-radius:3px;border-top-right-radius:3px}.panel>.list-group:last-child .list-group-item:last-child,.panel>.panel-collapse>.list-group:last-child .list-group-item:last-child{border-bottom:0;border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.panel-heading+.panel-collapse>.list-group .list-group-item:first-child{border-top-left-radius:0;border-top-right-radius:0}.panel-heading+.list-group .list-group-item:first-child{border-top-width:0}.list-group+.panel-footer{border-top-width:0}.panel>.table,.panel>.table-responsive>.table,.panel>.panel-collapse>.table{margin-bottom:0}.panel>.table caption,.panel>.table-responsive>.table caption,.panel>.panel-collapse>.table caption{padding-right:15px;padding-left:15px}.panel>.table:first-child,.panel>.table-responsive:first-child>.table:first-child{border-top-left-radius:3px;border-top-right-radius:3px}.panel>.table:first-child>thead:first-child>tr:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child{border-top-left-radius:3px;border-top-right-radius:3px}.panel>.table:first-child>thead:first-child>tr:first-child td:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child td:first-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:first-child,.panel>.table:first-child>thead:first-child>tr:first-child th:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child th:first-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:first-child{border-top-left-radius:3px}.panel>.table:first-child>thead:first-child>tr:first-child td:last-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:last-child,.panel>.table:first-child>tbody:first-child>tr:first-child td:last-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:last-child,.panel>.table:first-child>thead:first-child>tr:first-child th:last-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:last-child,.panel>.table:first-child>tbody:first-child>tr:first-child th:last-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:last-child{border-top-right-radius:3px}.panel>.table:last-child,.panel>.table-responsive:last-child>.table:last-child{border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.table:last-child>tbody:last-child>tr:last-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child{border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.table:last-child>tbody:last-child>tr:last-child td:first-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:first-child,.panel>.table:last-child>tfoot:last-child>tr:last-child td:first-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:first-child,.panel>.table:last-child>tbody:last-child>tr:last-child th:first-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:first-child,.panel>.table:last-child>tfoot:last-child>tr:last-child th:first-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:first-child{border-bottom-left-radius:3px}.panel>.table:last-child>tbody:last-child>tr:last-child td:last-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child td:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:last-child,.panel>.table:last-child>tbody:last-child>tr:last-child th:last-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child th:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:last-child{border-bottom-right-radius:3px}.panel>.panel-body+.table,.panel>.panel-body+.table-responsive,.panel>.table+.panel-body,.panel>.table-responsive+.panel-body{border-top:1px solid #ddd}.panel>.table>tbody:first-child>tr:first-child th,.panel>.table>tbody:first-child>tr:first-child td{border-top:0}.panel>.table-bordered,.panel>.table-responsive>.table-bordered{border:0}.panel>.table-bordered>thead>tr>th:first-child,.panel>.table-responsive>.table-bordered>thead>tr>th:first-child,.panel>.table-bordered>tbody>tr>th:first-child,.panel>.table-responsive>.table-bordered>tbody>tr>th:first-child,.panel>.table-bordered>tfoot>tr>th:first-child,.panel>.table-responsive>.table-bordered>tfoot>tr>th:first-child,.panel>.table-bordered>thead>tr>td:first-child,.panel>.table-responsive>.table-bordered>thead>tr>td:first-child,.panel>.table-bordered>tbody>tr>td:first-child,.panel>.table-responsive>.table-bordered>tbody>tr>td:first-child,.panel>.table-bordered>tfoot>tr>td:first-child,.panel>.table-responsive>.table-bordered>tfoot>tr>td:first-child{border-left:0}.panel>.table-bordered>thead>tr>th:last-child,.panel>.table-responsive>.table-bordered>thead>tr>th:last-child,.panel>.table-bordered>tbody>tr>th:last-child,.panel>.table-responsive>.table-bordered>tbody>tr>th:last-child,.panel>.table-bordered>tfoot>tr>th:last-child,.panel>.table-responsive>.table-bordered>tfoot>tr>th:last-child,.panel>.table-bordered>thead>tr>td:last-child,.panel>.table-responsive>.table-bordered>thead>tr>td:last-child,.panel>.table-bordered>tbody>tr>td:last-child,.panel>.table-responsive>.table-bordered>tbody>tr>td:last-child,.panel>.table-bordered>tfoot>tr>td:last-child,.panel>.table-responsive>.table-bordered>tfoot>tr>td:last-child{border-right:0}.panel>.table-bordered>thead>tr:first-child>td,.panel>.table-responsive>.table-bordered>thead>tr:first-child>td,.panel>.table-bordered>tbody>tr:first-child>td,.panel>.table-responsive>.table-bordered>tbody>tr:first-child>td,.panel>.table-bordered>thead>tr:first-child>th,.panel>.table-responsive>.table-bordered>thead>tr:first-child>th,.panel>.table-bordered>tbody>tr:first-child>th,.panel>.table-responsive>.table-bordered>tbody>tr:first-child>th{border-bottom:0}.panel>.table-bordered>tbody>tr:last-child>td,.panel>.table-responsive>.table-bordered>tbody>tr:last-child>td,.panel>.table-bordered>tfoot>tr:last-child>td,.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>td,.panel>.table-bordered>tbody>tr:last-child>th,.panel>.table-responsive>.table-bordered>tbody>tr:last-child>th,.panel>.table-bordered>tfoot>tr:last-child>th,.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>th{border-bottom:0}.panel>.table-responsive{margin-bottom:0;border:0}.panel-group{margin-bottom:20px}.panel-group .panel{margin-bottom:0;border-radius:4px}.panel-group .panel+.panel{margin-top:5px}.panel-group .panel-heading{border-bottom:0}.panel-group .panel-heading+.panel-collapse>.panel-body,.panel-group .panel-heading+.panel-collapse>.list-group{border-top:1px solid #ddd}.panel-group .panel-footer{border-top:0}.panel-group .panel-footer+.panel-collapse .panel-body{border-bottom:1px solid #ddd}.panel-default{border-color:#ddd}.panel-default>.panel-heading{color:#333;background-color:#f5f5f5;border-color:#ddd}.panel-default>.panel-heading+.panel-collapse>.panel-body{border-top-color:#ddd}.panel-default>.panel-heading .badge{color:#f5f5f5;background-color:#333}.panel-default>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#ddd}.panel-primary{border-color:#337ab7}.panel-primary>.panel-heading{color:#fff;background-color:#337ab7;border-color:#337ab7}.panel-primary>.panel-heading+.panel-collapse>.panel-body{border-top-color:#337ab7}.panel-primary>.panel-heading .badge{color:#337ab7;background-color:#fff}.panel-primary>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#337ab7}.panel-success{border-color:#d6e9c6}.panel-success>.panel-heading{color:#3c763d;background-color:#dff0d8;border-color:#d6e9c6}.panel-success>.panel-heading+.panel-collapse>.panel-body{border-top-color:#d6e9c6}.panel-success>.panel-heading .badge{color:#dff0d8;background-color:#3c763d}.panel-success>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#d6e9c6}.panel-info{border-color:#bce8f1}.panel-info>.panel-heading{color:#31708f;background-color:#d9edf7;border-color:#bce8f1}.panel-info>.panel-heading+.panel-collapse>.panel-body{border-top-color:#bce8f1}.panel-info>.panel-heading .badge{color:#d9edf7;background-color:#31708f}.panel-info>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#bce8f1}.panel-warning{border-color:#faebcc}.panel-warning>.panel-heading{color:#8a6d3b;background-color:#fcf8e3;border-color:#faebcc}.panel-warning>.panel-heading+.panel-collapse>.panel-body{border-top-color:#faebcc}.panel-warning>.panel-heading .badge{color:#fcf8e3;background-color:#8a6d3b}.panel-warning>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#faebcc}.panel-danger{border-color:#ebccd1}.panel-danger>.panel-heading{color:#a94442;background-color:#f2dede;border-color:#ebccd1}.panel-danger>.panel-heading+.panel-collapse>.panel-body{border-top-color:#ebccd1}.panel-danger>.panel-heading .badge{color:#f2dede;background-color:#a94442}.panel-danger>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#ebccd1}.close{float:right;font-size:21px;font-weight:bold;line-height:1;color:#000;text-shadow:0 1px 0 #fff;filter:alpha(opacity=20);opacity:.2}.close:hover,.close:focus{color:#000;text-decoration:none;cursor:pointer;filter:alpha(opacity=50);opacity:.5}button.close{padding:0;cursor:pointer;background:transparent;border:0;-webkit-appearance:none;appearance:none}.modal-open{overflow:hidden}.modal{position:fixed;top:0;right:0;bottom:0;left:0;z-index:1050;display:none;overflow:hidden;-webkit-overflow-scrolling:touch;outline:0}.modal.fade .modal-dialog{-webkit-transform:translate(0, -25%);-ms-transform:translate(0, -25%);-o-transform:translate(0, -25%);transform:translate(0, -25%);-webkit-transition:-webkit-transform 0.3s ease-out;-o-transition:-o-transform 0.3s ease-out;transition:transform 0.3s ease-out}.modal.in .modal-dialog{-webkit-transform:translate(0, 0);-ms-transform:translate(0, 0);-o-transform:translate(0, 0);transform:translate(0, 0)}.modal-open .modal{overflow-x:hidden;overflow-y:auto}.modal-dialog{position:relative;width:auto;margin:10px}.modal-content{position:relative;background-color:#fff;-webkit-background-clip:padding-box;background-clip:padding-box;border:1px solid #999;border:1px solid rgba(0,0,0,0.2);border-radius:6px;-webkit-box-shadow:0 3px 9px rgba(0,0,0,0.5);box-shadow:0 3px 9px rgba(0,0,0,0.5);outline:0}.modal-backdrop{position:fixed;top:0;right:0;bottom:0;left:0;z-index:1040;background-color:#000}.modal-backdrop.fade{filter:alpha(opacity=0);opacity:0}.modal-backdrop.in{opacity: 0.65;}.modal-header{padding:15px;border-bottom:1px solid #e5e5e5}.modal-header .close{margin-top:-2px}.modal-title{margin:0;line-height:1.42857143}.modal-body{position:relative;padding:15px}.modal-footer{padding:15px;text-align:right;border-top:1px solid #e5e5e5}.modal-footer .btn+.btn{margin-bottom:0;margin-left:5px}.modal-footer .btn-group .btn+.btn{margin-left:-1px}.modal-footer .btn-block+.btn-block{margin-left:0}.modal-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}@media (min-width:768px){.modal-dialog{width:600px;margin:30px auto}.modal-content{-webkit-box-shadow:0 5px 15px rgba(0,0,0,0.5);box-shadow:0 5px 15px rgba(0,0,0,0.5)}.modal-sm{width:300px}}@media (min-width:992px){.modal-lg{width:900px}}.clearfix:before,.clearfix:after,.form-horizontal .form-group:before,.form-horizontal .form-group:after,.btn-toolbar:before,.btn-toolbar:after,.btn-group-vertical>.btn-group:before,.btn-group-vertical>.btn-group:after,.nav:before,.nav:after,.panel-body:before,.panel-body:after,.modal-header:before,.modal-header:after,.modal-footer:before,.modal-footer:after{display:table;content:" "}.clearfix:after,.form-horizontal .form-group:after,.btn-toolbar:after,.btn-group-vertical>.btn-group:after,.nav:after,.panel-body:after,.modal-header:after,.modal-footer:after{clear:both}.center-block{display:block;margin-right:auto;margin-left:auto}.pull-right{float:right !important}.pull-left{float:left !important}.hide{display:none !important}.show{display:block !important}.invisible{visibility:hidden}.text-hide{font:0/0 a;color:transparent;text-shadow:none;background-color:transparent;border:0}.hidden{display:none !important}.affix{position:fixed}
 .fa-youtube-play:before{content:"\f16a"}.fa::before{font-size: 92%;}
 </style>
<style>
@font-face{font-family:'FontAwesome';src:url(../fonts/fontawesome-webfont3295.eot?v=4.5.0);src:url(../fonts/fontawesome-webfontd41d.eot?#iefix&v=4.5.0) format("embedded-opentype"),url(../fonts/fontawesome-webfont3295.woff2?v=4.5.0) format("woff2"),url(../fonts/fontawesome-webfont3295.woff?v=4.5.0) format("woff"),url(../fonts/fontawesome-webfont3295.ttf?v=4.5.0) format("truetype"),url(../fonts/fontawesome-webfont3295.svg?v=4.5.0#fontawesomeregular) format("svg");font-weight:400;font-style:normal}.fa{display:inline-block;font:normal normal normal 14px/1 FontAwesome;font-size:inherit;text-rendering:auto;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.fa-lg{font-size:1.33333333em;line-height:.75em;vertical-align:-15%}.fa-2x{font-size:2em}.fa-3x{font-size:3em}.fa-4x{font-size:4em}.fa-5x{font-size:5em}.fa-fw{width:1.28571429em;text-align:center}.fa-ul{padding-left:0;margin-left:2.14285714em;list-style-type:none}.fa-ul>li{position:relative}.fa-li{position:absolute;left:-2.14285714em;width:2.14285714em;top:.14285714em;text-align:center}.fa-li.fa-lg{left:-1.85714286em}.fa-border{padding:.2em .25em .15em;border:solid .08em #eee;border-radius:.1em}.fa-pull-left{float:left}.fa-pull-right{float:right}.fa.fa-pull-left{margin-right:.3em}.fa.fa-pull-right{margin-left:.3em}.pull-right{float:right}.pull-left{float:left}.fa.pull-left{margin-right:.3em}.fa.pull-right{margin-left:.3em}.fa-spin{-webkit-animation:fa-spin 2s infinite linear;animation:fa-spin 2s infinite linear}.fa-pulse{-webkit-animation:fa-spin 1s infinite steps(8);animation:fa-spin 1s infinite steps(8)}@-webkit-keyframes fa-spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}@keyframes fa-spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}.fa-rotate-90{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=1);-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg)}.fa-rotate-180{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=2);-webkit-transform:rotate(180deg);-ms-transform:rotate(180deg);transform:rotate(180deg)}.fa-rotate-270{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=3);-webkit-transform:rotate(270deg);-ms-transform:rotate(270deg);transform:rotate(270deg)}.fa-flip-horizontal{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=0,mirror=1);-webkit-transform:scale(-1,1);-ms-transform:scale(-1,1);transform:scale(-1,1)}.fa-flip-vertical{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=2,mirror=1);-webkit-transform:scale(1,-1);-ms-transform:scale(1,-1);transform:scale(1,-1)}:root .fa-rotate-90,:root .fa-rotate-180,:root .fa-rotate-270,:root .fa-flip-horizontal,:root .fa-flip-vertical{filter:none}.fa-stack{position:relative;display:inline-block;width:2em;height:2em;line-height:2em;vertical-align:middle}.fa-stack-1x,.fa-stack-2x{position:absolute;left:0;width:100%;text-align:center}.fa-stack-1x{line-height:inherit}.fa-stack-2x{font-size:2em}.fa-inverse{color:#fff}.fa-glass:before{content:"\f000"}.fa-music:before{content:"\f001"}.fa-search:before{content:"\f002"}.fa-envelope-o:before{content:"\f003"}.fa-heart:before{content:"\f004"}.fa-star:before{content:"\f005"}.fa-star-o:before{content:"\f006"}.fa-user:before{content:"\f007"}.fa-film:before{content:"\f008"}.fa-th-large:before{content:"\f009"}.fa-th:before{content:"\f00a"}.fa-th-list:before{content:"\f00b"}.fa-check:before{content:"\f00c"}.fa-remove:before,.fa-close:before,.fa-times:before{content:"\f00d"}.fa-search-plus:before{content:"\f00e"}.fa-search-minus:before{content:"\f010"}.fa-power-off:before{content:"\f011"}.fa-signal:before{content:"\f012"}.fa-gear:before,.fa-cog:before{content:"\f013"}.fa-trash-o:before{content:"\f014"}.fa-home:before{content:"\f015"}.fa-file-o:before{content:"\f016"}.fa-clock-o:before{content:"\f017"}.fa-road:before{content:"\f018"}.fa-download:before{content:"\f019"}.fa-arrow-circle-o-down:before{content:"\f01a"}.fa-arrow-circle-o-up:before{content:"\f01b"}.fa-inbox:before{content:"\f01c"}.fa-play-circle-o:before{content:"\f01d"}.fa-rotate-right:before,.fa-repeat:before{content:"\f01e"}.fa-refresh:before{content:"\f021"}.fa-list-alt:before{content:"\f022"}.fa-lock:before{content:"\f023"}.fa-flag:before{content:"\f024"}.fa-headphones:before{content:"\f025"}.fa-volume-off:before{content:"\f026"}.fa-volume-down:before{content:"\f027"}.fa-volume-up:before{content:"\f028"}.fa-qrcode:before{content:"\f029"}.fa-barcode:before{content:"\f02a"}.fa-tag:before{content:"\f02b"}.fa-tags:before{content:"\f02c"}.fa-book:before{content:"\f02d"}.fa-bookmark:before{content:"\f02e"}.fa-print:before{content:"\f02f"}.fa-camera:before{content:"\f030"}.fa-font:before{content:"\f031"}.fa-bold:before{content:"\f032"}.fa-italic:before{content:"\f033"}.fa-text-height:before{content:"\f034"}.fa-text-width:before{content:"\f035"}.fa-align-left:before{content:"\f036"}.fa-align-center:before{content:"\f037"}.fa-align-right:before{content:"\f038"}.fa-align-justify:before{content:"\f039"}.fa-list:before{content:"\f03a"}.fa-dedent:before,.fa-outdent:before{content:"\f03b"}.fa-indent:before{content:"\f03c"}.fa-video-camera:before{content:"\f03d"}.fa-photo:before,.fa-image:before,.fa-picture-o:before{content:"\f03e"}.fa-pencil:before{content:"\f040"}.fa-map-marker:before{content:"\f041"}.fa-adjust:before{content:"\f042"}.fa-tint:before{content:"\f043"}.fa-edit:before,.fa-pencil-square-o:before{content:"\f044"}.fa-share-square-o:before{content:"\f045"}.fa-check-square-o:before{content:"\f046"}.fa-arrows:before{content:"\f047"}.fa-step-backward:before{content:"\f048"}.fa-fast-backward:before{content:"\f049"}.fa-backward:before{content:"\f04a"}.fa-play:before{content:"\f04b"}.fa-pause:before{content:"\f04c"}.fa-stop:before{content:"\f04d"}.fa-forward:before{content:"\f04e"}.fa-fast-forward:before{content:"\f050"}.fa-step-forward:before{content:"\f051"}.fa-eject:before{content:"\f052"}.fa-chevron-left:before{content:"\f053"}.fa-chevron-right:before{content:"\f054"}.fa-plus-circle:before{content:"\f055"}.fa-minus-circle:before{content:"\f056"}.fa-times-circle:before{content:"\f057"}.fa-check-circle:before{content:"\f058"}.fa-question-circle:before{content:"\f059"}.fa-info-circle:before{content:"\f05a"}.fa-crosshairs:before{content:"\f05b"}.fa-times-circle-o:before{content:"\f05c"}.fa-check-circle-o:before{content:"\f05d"}.fa-ban:before{content:"\f05e"}.fa-arrow-left:before{content:"\f060"}.fa-arrow-right:before{content:"\f061"}.fa-arrow-up:before{content:"\f062"}.fa-arrow-down:before{content:"\f063"}.fa-mail-forward:before,.fa-share:before{content:"\f064"}.fa-expand:before{content:"\f065"}.fa-compress:before{content:"\f066"}.fa-plus:before{content:"\f067"}.fa-minus:before{content:"\f068"}.fa-asterisk:before{content:"\f069"}.fa-exclamation-circle:before{content:"\f06a"}.fa-gift:before{content:"\f06b"}.fa-leaf:before{content:"\f06c"}.fa-fire:before{content:"\f06d"}.fa-eye:before{content:"\f06e"}.fa-eye-slash:before{content:"\f070"}.fa-warning:before,.fa-exclamation-triangle:before{content:"\f071"}.fa-plane:before{content:"\f072"}.fa-calendar:before{content:"\f073"}.fa-random:before{content:"\f074"}.fa-comment:before{content:"\f075"}.fa-magnet:before{content:"\f076"}.fa-chevron-up:before{content:"\f077"}.fa-chevron-down:before{content:"\f078"}.fa-retweet:before{content:"\f079"}.fa-shopping-cart:before{content:"\f07a"}.fa-folder:before{content:"\f07b"}.fa-folder-open:before{content:"\f07c"}.fa-arrows-v:before{content:"\f07d"}.fa-arrows-h:before{content:"\f07e"}.fa-bar-chart-o:before,.fa-bar-chart:before{content:"\f080"}.fa-twitter-square:before{content:"\f081"}.fa-facebook-square:before{content:"\f082"}.fa-camera-retro:before{content:"\f083"}.fa-key:before{content:"\f084"}.fa-gears:before,.fa-cogs:before{content:"\f085"}.fa-comments:before{content:"\f086"}.fa-thumbs-o-up:before{content:"\f087"}.fa-thumbs-o-down:before{content:"\f088"}.fa-star-half:before{content:"\f089"}.fa-heart-o:before{content:"\f08a"}.fa-sign-out:before{content:"\f08b"}.fa-linkedin-square:before{content:"\f08c"}.fa-thumb-tack:before{content:"\f08d"}.fa-external-link:before{content:"\f08e"}.fa-sign-in:before{content:"\f090"}.fa-trophy:before{content:"\f091"}.fa-github-square:before{content:"\f092"}.fa-upload:before{content:"\f093"}.fa-lemon-o:before{content:"\f094"}.fa-phone:before{content:"\f095"}.fa-square-o:before{content:"\f096"}.fa-bookmark-o:before{content:"\f097"}.fa-phone-square:before{content:"\f098"}.fa-twitter:before{content:"\f099"}.fa-facebook-f:before,.fa-facebook:before{content:"\f09a"}.fa-github:before{content:"\f09b"}.fa-unlock:before{content:"\f09c"}.fa-credit-card:before{content:"\f09d"}.fa-feed:before,.fa-rss:before{content:"\f09e"}.fa-hdd-o:before{content:"\f0a0"}.fa-bullhorn:before{content:"\f0a1"}.fa-bell:before{content:"\f0f3"}.fa-certificate:before{content:"\f0a3"}.fa-hand-o-right:before{content:"\f0a4"}.fa-hand-o-left:before{content:"\f0a5"}.fa-hand-o-up:before{content:"\f0a6"}.fa-hand-o-down:before{content:"\f0a7"}.fa-arrow-circle-left:before{content:"\f0a8"}.fa-arrow-circle-right:before{content:"\f0a9"}.fa-arrow-circle-up:before{content:"\f0aa"}.fa-arrow-circle-down:before{content:"\f0ab"}.fa-globe:before{content:"\f0ac"}.fa-wrench:before{content:"\f0ad"}.fa-tasks:before{content:"\f0ae"}.fa-filter:before{content:"\f0b0"}.fa-briefcase:before{content:"\f0b1"}.fa-arrows-alt:before{content:"\f0b2"}.fa-group:before,.fa-users:before{content:"\f0c0"}.fa-chain:before,.fa-link:before{content:"\f0c1"}.fa-cloud:before{content:"\f0c2"}.fa-flask:before{content:"\f0c3"}.fa-cut:before,.fa-scissors:before{content:"\f0c4"}.fa-copy:before,.fa-files-o:before{content:"\f0c5"}.fa-paperclip:before{content:"\f0c6"}.fa-save:before,.fa-floppy-o:before{content:"\f0c7"}.fa-square:before{content:"\f0c8"}.fa-navicon:before,.fa-reorder:before,.fa-bars:before{content:"\f0c9"}.fa-list-ul:before{content:"\f0ca"}.fa-list-ol:before{content:"\f0cb"}.fa-strikethrough:before{content:"\f0cc"}.fa-underline:before{content:"\f0cd"}.fa-table:before{content:"\f0ce"}.fa-magic:before{content:"\f0d0"}.fa-truck:before{content:"\f0d1"}.fa-pinterest:before{content:"\f0d2"}.fa-pinterest-square:before{content:"\f0d3"}.fa-google-plus-square:before{content:"\f0d4"}.fa-google-plus:before{content:"\f0d5"}.fa-money:before{content:"\f0d6"}.fa-caret-down:before{content:"\f0d7"}.fa-caret-up:before{content:"\f0d8"}.fa-caret-left:before{content:"\f0d9"}.fa-caret-right:before{content:"\f0da"}.fa-columns:before{content:"\f0db"}.fa-unsorted:before,.fa-sort:before{content:"\f0dc"}.fa-sort-down:before,.fa-sort-desc:before{content:"\f0dd"}.fa-sort-up:before,.fa-sort-asc:before{content:"\f0de"}.fa-envelope:before{content:"\f0e0"}.fa-linkedin:before{content:"\f0e1"}.fa-rotate-left:before,.fa-undo:before{content:"\f0e2"}.fa-legal:before,.fa-gavel:before{content:"\f0e3"}.fa-dashboard:before,.fa-tachometer:before{content:"\f0e4"}.fa-comment-o:before{content:"\f0e5"}.fa-comments-o:before{content:"\f0e6"}.fa-flash:before,.fa-bolt:before{content:"\f0e7"}.fa-sitemap:before{content:"\f0e8"}.fa-umbrella:before{content:"\f0e9"}.fa-paste:before,.fa-clipboard:before{content:"\f0ea"}.fa-lightbulb-o:before{content:"\f0eb"}.fa-exchange:before{content:"\f0ec"}.fa-cloud-download:before{content:"\f0ed"}.fa-cloud-upload:before{content:"\f0ee"}.fa-user-md:before{content:"\f0f0"}.fa-stethoscope:before{content:"\f0f1"}.fa-suitcase:before{content:"\f0f2"}.fa-bell-o:before{content:"\f0a2"}.fa-coffee:before{content:"\f0f4"}.fa-cutlery:before{content:"\f0f5"}.fa-file-text-o:before{content:"\f0f6"}.fa-building-o:before{content:"\f0f7"}.fa-hospital-o:before{content:"\f0f8"}.fa-ambulance:before{content:"\f0f9"}.fa-medkit:before{content:"\f0fa"}.fa-fighter-jet:before{content:"\f0fb"}.fa-beer:before{content:"\f0fc"}.fa-h-square:before{content:"\f0fd"}.fa-plus-square:before{content:"\f0fe"}.fa-angle-double-left:before{content:"\f100"}.fa-angle-double-right:before{content:"\f101"}.fa-angle-double-up:before{content:"\f102"}.fa-angle-double-down:before{content:"\f103"}.fa-angle-left:before{content:"\f104"}.fa-angle-right:before{content:"\f105"}.fa-angle-up:before{content:"\f106"}.fa-angle-down:before{content:"\f107"}.fa-desktop:before{content:"\f108"}.fa-laptop:before{content:"\f109"}.fa-tablet:before{content:"\f10a"}.fa-mobile-phone:before,.fa-mobile:before{content:"\f10b"}.fa-circle-o:before{content:"\f10c"}.fa-quote-left:before{content:"\f10d"}.fa-quote-right:before{content:"\f10e"}.fa-spinner:before{content:"\f110"}.fa-circle:before{content:"\f111"}.fa-mail-reply:before,.fa-reply:before{content:"\f112"}.fa-github-alt:before{content:"\f113"}.fa-folder-o:before{content:"\f114"}.fa-folder-open-o:before{content:"\f115"}.fa-smile-o:before{content:"\f118"}.fa-frown-o:before{content:"\f119"}.fa-meh-o:before{content:"\f11a"}.fa-gamepad:before{content:"\f11b"}.fa-keyboard-o:before{content:"\f11c"}.fa-flag-o:before{content:"\f11d"}.fa-flag-checkered:before{content:"\f11e"}.fa-terminal:before{content:"\f120"}.fa-code:before{content:"\f121"}.fa-mail-reply-all:before,.fa-reply-all:before{content:"\f122"}.fa-star-half-empty:before,.fa-star-half-full:before,.fa-star-half-o:before{content:"\f123"}.fa-location-arrow:before{content:"\f124"}.fa-crop:before{content:"\f125"}.fa-code-fork:before{content:"\f126"}.fa-unlink:before,.fa-chain-broken:before{content:"\f127"}.fa-question:before{content:"\f128"}.fa-info:before{content:"\f129"}.fa-exclamation:before{content:"\f12a"}.fa-superscript:before{content:"\f12b"}.fa-subscript:before{content:"\f12c"}.fa-eraser:before{content:"\f12d"}.fa-puzzle-piece:before{content:"\f12e"}.fa-microphone:before{content: "🎤️"} .fa-mutum:before{content: "🔇"}.fa-microphone-slash:before{content:"\f131"}.fa-shield:before{content:"\f132"}.fa-calendar-o:before{content:"\f133"}.fa-fire-extinguisher:before{content:"\f134"}.fa-rocket:before{content:"\f135"}.fa-maxcdn:before{content:"\f136"}.fa-chevron-circle-left:before{content:"\f137"}.fa-chevron-circle-right:before{content:"\f138"}.fa-chevron-circle-up:before{content:"\f139"}.fa-chevron-circle-down:before{content:"\f13a"}.fa-html5:before{content:"\f13b"}.fa-css3:before{content:"\f13c"}.fa-anchor:before{content:"\f13d"}.fa-unlock-alt:before{content:"\f13e"}.fa-bullseye:before{content:"\f140"}.fa-ellipsis-h:before{content:"\f141"}.fa-ellipsis-v:before{content:"\f142"}.fa-rss-square:before{content:"\f143"}.fa-play-circle:before{content:"\f144"}.fa-ticket:before{content:"\f145"}.fa-minus-square:before{content:"\f146"}.fa-minus-square-o:before{content:"\f147"}.fa-level-up:before{content:"\f148"}.fa-level-down:before{content:"\f149"}.fa-check-square:before{content:"\f14a"}.fa-pencil-square:before{content:"\f14b"}.fa-external-link-square:before{content:"\f14c"}.fa-share-square:before{content:"\f14d"}.fa-compass:before{content:"\f14e"}.fa-toggle-down:before,.fa-caret-square-o-down:before{content:"\f150"}.fa-toggle-up:before,.fa-caret-square-o-up:before{content:"\f151"}.fa-toggle-right:before,.fa-caret-square-o-right:before{content:"\f152"}.fa-euro:before,.fa-eur:before{content:"\f153"}.fa-gbp:before{content:"\f154"}.fa-dollar:before,.fa-usd:before{content:"\f155"}.fa-rupee:before,.fa-inr:before{content:"\f156"}.fa-cny:before,.fa-rmb:before,.fa-yen:before,.fa-jpy:before{content:"\f157"}.fa-ruble:before,.fa-rouble:before,.fa-rub:before{content:"\f158"}.fa-won:before,.fa-krw:before{content:"\f159"}.fa-bitcoin:before,.fa-btc:before{content:"\f15a"}.fa-file:before{content:"\f15b"}.fa-file-text:before{content:"\f15c"}.fa-sort-alpha-asc:before{content:"\f15d"}.fa-sort-alpha-desc:before{content:"\f15e"}.fa-sort-amount-asc:before{content:"\f160"}.fa-sort-amount-desc:before{content:"\f161"}.fa-sort-numeric-asc:before{content:"\f162"}.fa-sort-numeric-desc:before{content:"\f163"}.fa-thumbs-up:before{content:"\f164"}.fa-thumbs-down:before{content:"\f165"}.fa-youtube-square:before{content:"\f166"}.fa-youtube:before{content:"\f167"}.fa-xing:before{content:"\f168"}.fa-xing-square:before{content:"\f169"}.fa-youtube-play:before{content:"\f16a"}.fa-dropbox:before{content:"\f16b"}.fa-stack-overflow:before{content:"\f16c"}.fa-instagram:before{content:"\f16d"}.fa-flickr:before{content:"\f16e"}.fa-adn:before{content:"\f170"}.fa-bitbucket:before{content:"\f171"}.fa-bitbucket-square:before{content:"\f172"}.fa-tumblr:before{content:"\f173"}.fa-tumblr-square:before{content:"\f174"}.fa-long-arrow-down:before{content:"\f175"}.fa-long-arrow-up:before{content:"\f176"}.fa-long-arrow-left:before{content:"\f177"}.fa-long-arrow-right:before{content:"\f178"}.fa-apple:before{content:"\f179"}.fa-windows:before{content:"\f17a"}.fa-android:before{content:"\f17b"}.fa-linux:before{content:"\f17c"}.fa-dribbble:before{content:"\f17d"}.fa-skype:before{content:"\f17e"}.fa-foursquare:before{content:"\f180"}.fa-trello:before{content:"\f181"}.fa-female:before{content:"\f182"}.fa-male:before{content:"\f183"}.fa-gittip:before,.fa-gratipay:before{content:"\f184"}.fa-sun-o:before{content:"\f185"}.fa-moon-o:before{content:"\f186"}.fa-archive:before{content:"\f187"}.fa-bug:before{content:"\f188"}.fa-vk:before{content:"\f189"}.fa-weibo:before{content:"\f18a"}.fa-renren:before{content:"\f18b"}.fa-pagelines:before{content:"\f18c"}.fa-stack-exchange:before{content:"\f18d"}.fa-arrow-circle-o-right:before{content:"\f18e"}.fa-arrow-circle-o-left:before{content:"\f190"}.fa-toggle-left:before,.fa-caret-square-o-left:before{content:"\f191"}.fa-dot-circle-o:before{content:"\f192"}.fa-wheelchair:before{content:"\f193"}.fa-vimeo-square:before{content:"\f194"}.fa-turkish-lira:before,.fa-try:before{content:"\f195"}.fa-plus-square-o:before{content:"\f196"}.fa-space-shuttle:before{content:"\f197"}.fa-slack:before{content:"\f198"}.fa-envelope-square:before{content:"\f199"}.fa-wordpress:before{content:"\f19a"}.fa-openid:before{content:"\f19b"}.fa-institution:before,.fa-bank:before,.fa-university:before{content:"\f19c"}.fa-mortar-board:before,.fa-graduation-cap:before{content:"\f19d"}.fa-yahoo:before{content:"\f19e"}.fa-google:before{content:"\f1a0"}.fa-reddit:before{content:"\f1a1"}.fa-reddit-square:before{content:"\f1a2"}.fa-stumbleupon-circle:before{content:"\f1a3"}.fa-stumbleupon:before{content:"\f1a4"}.fa-delicious:before{content:"\f1a5"}.fa-digg:before{content:"\f1a6"}.fa-pied-piper:before{content:"\f1a7"}.fa-pied-piper-alt:before{content:"\f1a8"}.fa-drupal:before{content:"\f1a9"}.fa-joomla:before{content:"\f1aa"}.fa-language:before{content:"\f1ab"}.fa-fax:before{content:"\f1ac"}.fa-building:before{content:"\f1ad"}.fa-child:before{content:"\f1ae"}.fa-paw:before{content:"\f1b0"}.fa-spoon:before{content:"\f1b1"}.fa-cube:before{content:"\f1b2"}.fa-cubes:before{content:"\f1b3"}.fa-behance:before{content:"\f1b4"}.fa-behance-square:before{content:"\f1b5"}.fa-steam:before{content:"\f1b6"}.fa-steam-square:before{content:"\f1b7"}.fa-recycle:before{content:"\f1b8"}.fa-automobile:before,.fa-car:before{content:"\f1b9"}.fa-cab:before,.fa-taxi:before{content:"\f1ba"}.fa-tree:before{content:"\f1bb"}.fa-spotify:before{content:"\f1bc"}.fa-deviantart:before{content:"\f1bd"}.fa-soundcloud:before{content:"\f1be"}.fa-database:before{content:"\f1c0"}.fa-file-pdf-o:before{content:"\f1c1"}.fa-file-word-o:before{content:"\f1c2"}.fa-file-excel-o:before{content:"\f1c3"}.fa-file-powerpoint-o:before{content:"\f1c4"}.fa-file-photo-o:before,.fa-file-picture-o:before,.fa-file-image-o:before{content:"\f1c5"}.fa-file-zip-o:before,.fa-file-archive-o:before{content:"\f1c6"}.fa-file-sound-o:before,.fa-file-audio-o:before{content:"\f1c7"}.fa-file-movie-o:before,.fa-file-video-o:before{content:"\f1c8"}.fa-file-code-o:before{content:"\f1c9"}.fa-vine:before{content:"\f1ca"}.fa-codepen:before{content:"\f1cb"}.fa-jsfiddle:before{content:"\f1cc"}.fa-life-bouy:before,.fa-life-buoy:before,.fa-life-saver:before,.fa-support:before,.fa-life-ring:before{content:"\f1cd"}.fa-circle-o-notch:before{content:"\f1ce"}.fa-ra:before,.fa-rebel:before{content:"\f1d0"}.fa-ge:before,.fa-empire:before{content:"\f1d1"}.fa-git-square:before{content:"\f1d2"}.fa-git:before{content:"\f1d3"}.fa-y-combinator-square:before,.fa-yc-square:before,.fa-hacker-news:before{content:"\f1d4"}.fa-tencent-weibo:before{content:"\f1d5"}.fa-qq:before{content:"\f1d6"}.fa-wechat:before,.fa-weixin:before{content:"\f1d7"}.fa-send:before,.fa-paper-plane:before{content:"\f1d8"}.fa-send-o:before,.fa-paper-plane-o:before{content:"\f1d9"}.fa-history:before{content:"\f1da"}.fa-circle-thin:before{content:"\f1db"}.fa-header:before{content:"\f1dc"}.fa-paragraph:before{content:"\f1dd"}.fa-sliders:before{content:"\f1de"}.fa-share-alt:before{content:"\f1e0"}.fa-share-alt-square:before{content:"\f1e1"}.fa-bomb:before{content:"\f1e2"}.fa-soccer-ball-o:before,.fa-futbol-o:before{content:"\f1e3"}.fa-tty:before{content:"\f1e4"}.fa-binoculars:before{content:"\f1e5"}.fa-plug:before{content:"\f1e6"}.fa-slideshare:before{content:"\f1e7"}.fa-twitch:before{content:"\f1e8"}.fa-yelp:before{content:"\f1e9"}.fa-newspaper-o:before{content:"\f1ea"}.fa-wifi:before{content:"\f1eb"}.fa-calculator:before{content:"\f1ec"}.fa-paypal:before{content:"\f1ed"}.fa-google-wallet:before{content:"\f1ee"}.fa-cc-visa:before{content:"\f1f0"}.fa-cc-mastercard:before{content:"\f1f1"}.fa-cc-discover:before{content:"\f1f2"}.fa-cc-amex:before{content:"\f1f3"}.fa-cc-paypal:before{content:"\f1f4"}.fa-cc-stripe:before{content:"\f1f5"}.fa-bell-slash:before{content:"\f1f6"}.fa-bell-slash-o:before{content:"\f1f7"}.fa-trash:before{content:"\f1f8"}.fa-copyright:before{content:"\f1f9"}.fa-at:before{content:"\f1fa"}.fa-eyedropper:before{content:"\f1fb"}.fa-paint-brush:before{content:"\f1fc"}.fa-birthday-cake:before{content:"\f1fd"}.fa-area-chart:before{content:"\f1fe"}.fa-pie-chart:before{content:"\f200"}.fa-line-chart:before{content:"\f201"}.fa-lastfm:before{content:"\f202"}.fa-lastfm-square:before{content:"\f203"}.fa-toggle-off:before{content:"\f204"}.fa-toggle-on:before{content:"\f205"}.fa-bicycle:before{content:"\f206"}.fa-bus:before{content:"\f207"}.fa-ioxhost:before{content:"\f208"}.fa-angellist:before{content:"\f209"}.fa-cc:before{content:"\f20a"}.fa-shekel:before,.fa-sheqel:before,.fa-ils:before{content:"\f20b"}.fa-meanpath:before{content:"\f20c"}.fa-buysellads:before{content:"\f20d"}.fa-connectdevelop:before{content:"\f20e"}.fa-dashcube:before{content:"\f210"}.fa-forumbee:before{content:"\f211"}.fa-leanpub:before{content:"\f212"}.fa-sellsy:before{content:"\f213"}.fa-shirtsinbulk:before{content:"\f214"}.fa-simplybuilt:before{content:"\f215"}.fa-skyatlas:before{content:"\f216"}.fa-cart-plus:before{content:"\f217"}.fa-cart-arrow-down:before{content:"\f218"}.fa-diamond:before{content:"\f219"}.fa-ship:before{content:"\f21a"}.fa-user-secret:before{content:"\f21b"}.fa-motorcycle:before{content:"\f21c"}.fa-street-view:before{content:"\f21d"}.fa-heartbeat:before{content:"\f21e"}.fa-venus:before{content:"\f221"}.fa-mars:before{content:"\f222"}.fa-mercury:before{content:"\f223"}.fa-intersex:before,.fa-transgender:before{content:"\f224"}.fa-transgender-alt:before{content:"\f225"}.fa-venus-double:before{content:"\f226"}.fa-mars-double:before{content:"\f227"}.fa-venus-mars:before{content:"\f228"}.fa-mars-stroke:before{content:"\f229"}.fa-mars-stroke-v:before{content:"\f22a"}.fa-mars-stroke-h:before{content:"\f22b"}.fa-neuter:before{content:"\f22c"}.fa-genderless:before{content:"\f22d"}.fa-facebook-official:before{content:"\f230"}.fa-pinterest-p:before{content:"\f231"}.fa-whatsapp:before{content:"\f232"}.fa-server:before{content:"\f233"}.fa-user-plus:before{content:"\f234"}.fa-user-times:before{content:"\f235"}.fa-hotel:before,.fa-bed:before{content:"\f236"}.fa-viacoin:before{content:"\f237"}.fa-train:before{content:"\f238"}.fa-subway:before{content:"\f239"}.fa-medium:before{content:"\f23a"}.fa-yc:before,.fa-y-combinator:before{content:"\f23b"}.fa-optin-monster:before{content:"\f23c"}.fa-opencart:before{content:"\f23d"}.fa-expeditedssl:before{content:"\f23e"}.fa-battery-4:before,.fa-battery-full:before{content:"\f240"}.fa-battery-3:before,.fa-battery-three-quarters:before{content:"\f241"}.fa-battery-2:before,.fa-battery-half:before{content:"\f242"}.fa-battery-1:before,.fa-battery-quarter:before{content:"\f243"}.fa-battery-0:before,.fa-battery-empty:before{content:"\f244"}.fa-mouse-pointer:before{content:"\f245"}.fa-i-cursor:before{content:"\f246"}.fa-object-group:before{content:"\f247"}.fa-object-ungroup:before{content:"\f248"}.fa-sticky-note:before{content:"\f249"}.fa-sticky-note-o:before{content:"\f24a"}.fa-cc-jcb:before{content:"\f24b"}.fa-cc-diners-club:before{content:"\f24c"}.fa-clone:before{content:"\f24d"}.fa-balance-scale:before{content:"\f24e"}.fa-hourglass-o:before{content:"\f250"}.fa-hourglass-1:before,.fa-hourglass-start:before{content:"\f251"}.fa-hourglass-2:before,.fa-hourglass-half:before{content:"\f252"}.fa-hourglass-3:before,.fa-hourglass-end:before{content:"\f253"}.fa-hourglass:before{content:"\f254"}.fa-hand-grab-o:before,.fa-hand-rock-o:before{content:"\f255"}.fa-hand-stop-o:before,.fa-hand-paper-o:before{content:"\f256"}.fa-hand-scissors-o:before{content:"\f257"}.fa-hand-lizard-o:before{content:"\f258"}.fa-hand-spock-o:before{content:"\f259"}.fa-hand-pointer-o:before{content:"\f25a"}.fa-hand-peace-o:before{content:"\f25b"}.fa-trademark:before{content:"\f25c"}.fa-registered:before{content:"\f25d"}.fa-creative-commons:before{content:"\f25e"}.fa-gg:before{content:"\f260"}.fa-gg-circle:before{content:"\f261"}.fa-tripadvisor:before{content:"\f262"}.fa-odnoklassniki:before{content:"\f263"}.fa-odnoklassniki-square:before{content:"\f264"}.fa-get-pocket:before{content:"\f265"}.fa-wikipedia-w:before{content:"\f266"}.fa-safari:before{content:"\f267"}.fa-chrome:before{content:"\f268"}.fa-firefox:before{content:"\f269"}.fa-opera:before{content:"\f26a"}.fa-internet-explorer:before{content:"\f26b"}.fa-tv:before,.fa-television:before{content:"\f26c"}.fa-contao:before{content:"\f26d"}.fa-500px:before{content:"\f26e"}.fa-amazon:before{content:"\f270"}.fa-calendar-plus-o:before{content:"\f271"}.fa-calendar-minus-o:before{content:"\f272"}.fa-calendar-times-o:before{content:"\f273"}.fa-calendar-check-o:before{content:"\f274"}.fa-industry:before{content:"\f275"}.fa-map-pin:before{content:"\f276"}.fa-map-signs:before{content:"\f277"}.fa-map-o:before{content:"\f278"}.fa-map:before{content:"\f279"}.fa-commenting:before{content:"\f27a"}.fa-commenting-o:before{content:"\f27b"}.fa-houzz:before{content:"\f27c"}.fa-vimeo:before{content:"\f27d"}.fa-black-tie:before{content:"\f27e"}.fa-fonticons:before{content:"\f280"}.fa-reddit-alien:before{content:"\f281"}.fa-edge:before{content:"\f282"}.fa-credit-card-alt:before{content:"\f283"}.fa-codiepie:before{content:"\f284"}.fa-modx:before{content:"\f285"}.fa-fort-awesome:before{content:"\f286"}.fa-usb:before{content:"\f287"}.fa-product-hunt:before{content:"\f288"}.fa-mixcloud:before{content:"\f289"}.fa-scribd:before{content:"\f28a"}.fa-pause-circle:before{content:"\f28b"}.fa-pause-circle-o:before{content:"\f28c"}.fa-stop-circle:before{content:"\f28d"}.fa-stop-circle-o:before{content:"\f28e"}.fa-shopping-bag:before{content:"\f290"}.fa-shopping-basket:before{content:"\f291"}.fa-hashtag:before{content:"\f292"}.fa-bluetooth:before{content:"\f293"}.fa-bluetooth-b:before{content:"\f294"}.fa-percent:before{content:"\f295"} </style>
<style>.borderg1 { border: 1px solid #ccc; padding: 10px; margin: 10px; border-radius: 5px; background-color: #fff; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); } .table1 { display: table; width: 100%; } .table-row1 { display: table-row; } .table-cell1 { display: table-cell; padding: 8px; border-bottom: 1px solid #ccc; } .table-header1 { font-weight: bold; background-color: #f2f2f2; } .username { color: #e74c3c; } .topic { color: #3498db; } .ip { color: #27ae60; } .device { color: #f39c12; } button.btn.fl.fa.fa-youtube-play { display: flex; }button.btns { color: black; padding: 0px 4px !important; margin-bottom: -21px !important; min-height: 32px; border-radius: 1px; display: inline-block; margin-bottom: 0; font-weight: normal; text-align: center; white-space: nowrap; vertical-align: middle; -ms-touch-action: manipulation; touch-action: manipulation; cursor: pointer; background-image: none; border: 1px solid transparent; padding: 6px 12px; font-size: 14px; line-height: 1.42857143; border-radius: 4px; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none;  display: flex !important; }button.btn.fa.fa-youtube { display: flex !important; }.uzr.d-flex.hmsg.mm button { display: none; } .uzr.d-flex.mm button { display: none; }.ppop.light.border.break.ppop-color-container { left: -1px !important; } @supports not ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) { .bgf { background-color: rgba(0, 0, 0, .5); } } .nav-pills li a{border-radius: 0px!important;} @keyframes bounce { 0%, 60%, 100% { transform: translateY(0); } 30% { transform: translateY(-4px); } } @supports not ((-webkit-backdrop-filter: none) or (backdrop-filter: none)) { .bgf { background-color: rgba(0, 0, 0, .5); } }td{overflow-y: hidden!important;} .uhtml{ text-align:left;width:100%;margin:0px!important;border-bottom:1px solid #efefef;padding-right:1px;background-color: #ffffff; } .uhtml .di1 .u-pic{ min-width:52px;width:52px;min-height:50px;max-height:90px;background-size: cover;background-position: center; } .uhtml .co.ico{ width:20px;height:20px; } .uhtml .di1 span.muted{ color:indianred;display: none;width: 16px;margin-right: -16px; } .uhtml .di1 img.ustat{ width:4px;min-height:100%; } .uhtml div.di1{ padding-right: 16px;width: 100%; } .uhtml .di1 .di2{ padding: 0px 3px;overflow: hidden; } .uhtml .di1 .di2 .d-flex{ width:100%;display: flex;margin-top:1px;height: 21px; } .uhtml .di1 .di2 .u-msg{ color:#888;padding:1px; text-align: center; } .uhtml .di1 .di2 .u-topic{ padding-top:1px; max-width:100%;border-radius: 2px; } .uhtml .di1 .di2 .u-ico{ min-height: 14px;margin-top:1px; } .uhtml .di3{ margin-left:-32px; } .uhtml .di3 .uhash{ color:grey;font-size:70%!important; }.pstorycdesclass{ position: fixed; width: 100%; height: 100%; background: rgba(0,0,0,0.6); z-index: 9999; top: 0; left: 0; } .pstycdesclass{ position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); padding: 5px; z-index: 99999; background: #fff; }.popover{position:absolute;top:0;left:0;z-index:1060;display:none;max-width:276px;padding:1px;font-family:"Helvetica Neue",Helvetica,Arial,sans-serif;font-size:14px;font-style:normal;font-weight:400;line-height:1.42857143;text-align:left;text-align:start;text-decoration:none;text-shadow:none;text-transform:none;letter-spacing:normal;word-break:normal;word-spacing:normal;word-wrap:normal;white-space:normal;background-color:#fff;-webkit-background-clip:padding-box;background-clip:padding-box;border:1px solid #ccc;border:1px solid rgba(0,0,0,0.2);border-radius:6px;-webkit-box-shadow:0 5px 10px rgba(0,0,0,0.2);box-shadow:0 5px 10px rgba(0,0,0,0.2);line-break:auto}.popover.top{margin-top:-10px}.popover.right{margin-left:10px}.popover.bottom{margin-top:10px}.popover.left{margin-left:-10px}.popover-title{padding:8px 14px;margin:0;font-size:14px;background-color:#f7f7f7;border-bottom:1px solid #ebebeb;border-radius:5px 5px 0 0}.popover{height:250px;overflow-y:auto;white-space:pre-wrap}.popover.right{margin-left:-111px;min-width:339px!important;border-radius:0;min-height:559px;margin-top:-300px}.popover.right .btn.hand.borderg.corner{min-width:min-content;padding:4px!important;height:32px}.popover.right{margin-left:-111px!important;min-width:339px!important;border-radius:0;min-height:559px;margin-top:-300px}.popover.right .btn.hand.borderg.corner{min-width:min-content;padding:4px!important;height:32px}.popover.fade.top{width:700px!important;height:300px!important;min-width:300px!important;border-radius:0!important}.popover-content{padding:1px}.popover > .arrow,.popover > .arrow:after{position:absolute;display:block;width:0;height:0;border-color:transparent;border-style:solid}.popover > .arrow{border-width:11px}.popover > .arrow:after{content:"";border-width:10px}.popover.top > .arrow{bottom:-11px;left:50%;margin-left:-11px;border-top-color:#999;border-top-color:rgba(0,0,0,0.25);border-bottom-width:0}.popover.top > .arrow:after{bottom:1px;margin-left:-10px;content:" ";border-top-color:#fff;border-bottom-width:0}.popover.right > .arrow{top:50%;left:-11px;margin-top:-11px;border-right-color:#999;border-right-color:rgba(0,0,0,0.25);border-left-width:0}.popover.right > .arrow:after{bottom:-10px;left:1px;content:" ";border-right-color:#fff;border-left-width:0}.popover.bottom > .arrow{top:-11px;left:50%;margin-left:-11px;border-top-width:0;border-bottom-color:#999;border-bottom-color:rgba(0,0,0,0.25)}.popover.bottom > .arrow:after{top:1px;margin-left:-10px;content:" ";border-top-width:0;border-bottom-color:#fff}.popover.left > .arrow{top:50%;right:-11px;margin-top:-11px;border-right-width:0;border-left-color:#999;border-left-color:rgba(0,0,0,0.25)}.popover.left > .arrow:after{right:1px;bottom:-10px;content:" ";border-right-width:0;border-left-color:#fff}.d2.light.fl.d2bc.filh.break{height:fit-content!important}.notification{min-width:180px;max-width:260px;border:1px solid #000;z-index:2000;background-color:#efefef;position:relative;padding:5px;border-radius:5px;margin:.7rem auto 0}.notification__sender__avatar{border-radius:5px;margin-right:.1em}.notification__sender__name{margin:.2em 0;font-size:.6rem;font-weight:700;padding:.15em 0 0;max-width:80%;color:#000;display:inline-block}.notification p{margin-bottom:.4em}.username__wrapper{margin-top:.01em;padding:2.1px 1px;display:inline-block;white-space:nowrap;overflow:hidden!important;text-overflow:ellipsis;max-width:99%;margin-left:3PX;direction:initial;-webkit-user-select:none;font-size:15PX;margin-bottom:2px}.notification__message{font-size:.6rem;font-weight:700;margin-top:.2em;width:100%;padding:0 .2em;word-break:break-word}.notification__time{position:absolute;top:.2rem;right:.2rem;font-size:.5rem}.notification__title{font-size:.6rem;text-align:center;margin:-14px auto 0;width:50%;border:1px solid #000;border-radius:5px;line-height:1.6}.cc.noflow.nosel.hand.break{width:101%!important;padding:2px}.is_speaking{-webkit-animation:ripple .7s linear infinite;animation:ripple .7s linear infinite}@-webkit-keyframes ripple{0%{box-shadow:0 0 0 0 rgba(0,200,45,0),0 0 0 1px rgba(0,200,45,0.3),0 0 0 2px rgba(0,200,45,0.3),0 0 0 3px rgba(0,200,45,0.3)}to{box-shadow:0 0 0 1px rgba(0,200,45,0.3),0 0 0 4px rgba(0,200,45,0.3),0 0 0 5px rgba(0,200,45,0.3),0 0 0 6px rgba(0,200,45,0)}}@keyframes ripple{0%{box-shadow:0 0 0 0 rgba(255,0,0,0)0 0 0 1px rgba(0,200,45,0.3),0 0 0 2px rgba(0,200,45,0.3),0 0 0 3px rgba(0,200,45,0.3)}to{box-shadow:0 0 0 1px rgba(0,200,45,0.3),0 0 0 4px rgba(0,200,45,0.3),0 0 0 5px rgba(0,200,45,0.3),0 0 0 6px rgba(255,3,3,0)}}.ico_pl{color:#fff;background:#000;padding:4px}table > thead{background:#6495ed;color:#fff}.activepower{border:2px solid #000;background:#ffe4c4}.fa-windows:before,.fa-linux:before,.fa-apple:before,.fa-times-circle:before,.fa-th-large:before{margin-left:16px}.fa-chrome:before,.fa-edge:before,.fa-firefox:before,.fa-internet-explorer:before,.fa-opera:before,.fa-safari:before,.fa-android:before,.fa-scribd:before{margin-left:16px}#StoryProgress{width:100%;background-color:#ddd}#BarStory{width:0;height:3px;background-color:#6495ed;margin-bottom:15px}.desginliste{overflow-x:auto;overflow-y:hidden;display:flex;margin:-2px -36px;padding:2px 0 0 32px;width:112%;height:100px;margin-bottom:14px}.desginliste img{border:2px solid #000;width:100%;height:79px;margin-left:5px}.desginlist{overflow-x:auto;overflow-y:hidden;display:flex;margin:auto;margin-top:-14px;margin-bottom:0!important}.desginlist img{border:2px solid #000;width:100px;height:100px;margin-left:5px}@keyframes show_message_box{0%{transform:translateX(-100%)}40%{transform:translateX(10%)}80%,to{transform:translateX(0.8333333333rem)}}@keyframes hide_message_box{0%{transform:translateX(0.8333333333rem)}40%{transform:translateX(10%)}80%,to{opacity:0;pointer-events:none;transform:translateX(-100%)}}.message-box{animation:show_message_box 2s ease forwards;left:.8333333333rem;position:absolute;top:.8333333333rem;z-index:10000000}.message-box .box.green{border-color:#2ecc71}.message-box .box{height:33px;align-items:center;background:#fff;border-left:5px solid #2ecc71;border-radius:.4166666667rem;box-shadow:.0416666667rem .2916666667rem .5833333333rem -.2083333333rem rgba(0,0,0,0.15);display:flex;justify-content:space-between;padding:.4rem .6rem .4rem .4rem;width:auto}.message-box .box .content,.message-box .content .icon{align-items:center;display:flex}.message-box .box.green .content .icon{background:#2ecc71}.message-box .content .icon{padding:12px!important;background:#2ecc71;border-radius:50%;color:#fff;font-size:1.0416666667rem;height:1rem;justify-content:center;line-height:1rem;text-align:center;width:1rem}.message-box .content .icon .fa{font-size:.6rem;margin-top:.1rem}.message-box .content .details{direction:rtl;margin-left:.3rem;margin-top:13px}.message-box.hiden{animation:hide_message_box 2s ease forwards}.box.orange .icon{background:#df7a22}textarea#roomSearchInput::placeholder{color:#fff}textarea#usearch::placeholder{color:#fff}.box.red{border-color:#d44646}.box.red .icon{background:#d44646;color:#fff}.box.red .icon:before{content:"";position:absolute;top:4px;right:0;left:23px;width:2px;height:23px;background:#c70e0e;transform:rotate(45deg)}.box.orange{border-color:#df7a22}.d-color-container{display:none}.label-info{background-color:mediumseagreen}.fl.mini.u-msg.dots{color:#888!important;margin-top:0!important;float:initial!important}div#cooment .popover{left:20px!important;background:#fff;border:1px solid;box-shadow:3px 3px 6px #9b9595}a.comm.btn.minix.btn-danger.fa.fa-comments.fr{background:#<%=colors.btcolor %>!important}.fa-volume-high:before,.fa-volume-up:before{content:"\f028"}.panel{margin-bottom:20px;background-color:#fff;border:1px solid transparent;border-radius:4px;-webkit-box-shadow:0 1px 1px rgba(0,0,0,0.05);box-shadow:0 1px 1px rgba(0,0,0,0.05)}.panel-body{padding:15px}.panel-heading{padding:10px 15px;border-bottom:1px solid transparent;border-top-left-radius:3px;border-top-right-radius:3px}.panel-heading>.dropdown .dropdown-toggle{color:inherit}.panel-title{margin-top:0;margin-bottom:0;font-size:16px;color:inherit}.panel-title>a,.panel-title>small,.panel-title>.small,.panel-title>small>a,.panel-title>.small>a{color:inherit}.panel-footer{padding:10px 15px;background-color:#f5f5f5;border-top:1px solid #ddd;border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.list-group,.panel>.panel-collapse>.list-group{margin-bottom:0}.panel>.list-group .list-group-item,.panel>.panel-collapse>.list-group .list-group-item{border-width:1px 0;border-radius:0}.panel>.list-group:first-child .list-group-item:first-child,.panel>.panel-collapse>.list-group:first-child .list-group-item:first-child{border-top:0;border-top-left-radius:3px;border-top-right-radius:3px}.panel>.list-group:last-child .list-group-item:last-child,.panel>.panel-collapse>.list-group:last-child .list-group-item:last-child{border-bottom:0;border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.panel-heading+.panel-collapse>.list-group .list-group-item:first-child{border-top-left-radius:0;border-top-right-radius:0}.panel-heading+.list-group .list-group-item:first-child{border-top-width:0}.list-group+.panel-footer{border-top-width:0}.panel>.table,.panel>.table-responsive>.table,.panel>.panel-collapse>.table{margin-bottom:0}.panel>.table caption,.panel>.table-responsive>.table caption,.panel>.panel-collapse>.table caption{padding-right:15px;padding-left:15px}.panel>.table:first-child,.panel>.table-responsive:first-child>.table:first-child{border-top-left-radius:3px;border-top-right-radius:3px}.panel>.table:first-child>thead:first-child>tr:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child{border-top-left-radius:3px;border-top-right-radius:3px}.panel>.table:first-child>thead:first-child>tr:first-child td:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child td:first-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:first-child,.panel>.table:first-child>thead:first-child>tr:first-child th:first-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:first-child,.panel>.table:first-child>tbody:first-child>tr:first-child th:first-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:first-child{border-top-left-radius:3px}.panel>.table:first-child>thead:first-child>tr:first-child td:last-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child td:last-child,.panel>.table:first-child>tbody:first-child>tr:first-child td:last-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child td:last-child,.panel>.table:first-child>thead:first-child>tr:first-child th:last-child,.panel>.table-responsive:first-child>.table:first-child>thead:first-child>tr:first-child th:last-child,.panel>.table:first-child>tbody:first-child>tr:first-child th:last-child,.panel>.table-responsive:first-child>.table:first-child>tbody:first-child>tr:first-child th:last-child{border-top-right-radius:3px}.panel>.table:last-child,.panel>.table-responsive:last-child>.table:last-child{border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.table:last-child>tbody:last-child>tr:last-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child{border-bottom-right-radius:3px;border-bottom-left-radius:3px}.panel>.table:last-child>tbody:last-child>tr:last-child td:first-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:first-child,.panel>.table:last-child>tfoot:last-child>tr:last-child td:first-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:first-child,.panel>.table:last-child>tbody:last-child>tr:last-child th:first-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:first-child,.panel>.table:last-child>tfoot:last-child>tr:last-child th:first-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:first-child{border-bottom-left-radius:3px}.panel>.table:last-child>tbody:last-child>tr:last-child td:last-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child td:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child td:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child td:last-child,.panel>.table:last-child>tbody:last-child>tr:last-child th:last-child,.panel>.table-responsive:last-child>.table:last-child>tbody:last-child>tr:last-child th:last-child,.panel>.table:last-child>tfoot:last-child>tr:last-child th:last-child,.panel>.table-responsive:last-child>.table:last-child>tfoot:last-child>tr:last-child th:last-child{border-bottom-right-radius:3px}.panel>.panel-body+.table,.panel>.panel-body+.table-responsive,.panel>.table+.panel-body,.panel>.table-responsive+.panel-body{border-top:1px solid #ddd}.panel>.table>tbody:first-child>tr:first-child th,.panel>.table>tbody:first-child>tr:first-child td{border-top:0}.panel>.table-bordered,.panel>.table-responsive>.table-bordered{border:0}.panel>.table-bordered>thead>tr>th:first-child,.panel>.table-responsive>.table-bordered>thead>tr>th:first-child,.panel>.table-bordered>tbody>tr>th:first-child,.panel>.table-responsive>.table-bordered>tbody>tr>th:first-child,.panel>.table-bordered>tfoot>tr>th:first-child,.panel>.table-responsive>.table-bordered>tfoot>tr>th:first-child,.panel>.table-bordered>thead>tr>td:first-child,.panel>.table-responsive>.table-bordered>thead>tr>td:first-child,.panel>.table-bordered>tbody>tr>td:first-child,.panel>.table-responsive>.table-bordered>tbody>tr>td:first-child,.panel>.table-bordered>tfoot>tr>td:first-child,.panel>.table-responsive>.table-bordered>tfoot>tr>td:first-child{border-left:0}.panel>.table-bordered>thead>tr>th:last-child,.panel>.table-responsive>.table-bordered>thead>tr>th:last-child,.panel>.table-bordered>tbody>tr>th:last-child,.panel>.table-responsive>.table-bordered>tbody>tr>th:last-child,.panel>.table-bordered>tfoot>tr>th:last-child,.panel>.table-responsive>.table-bordered>tfoot>tr>th:last-child,.panel>.table-bordered>thead>tr>td:last-child,.panel>.table-responsive>.table-bordered>thead>tr>td:last-child,.panel>.table-bordered>tbody>tr>td:last-child,.panel>.table-responsive>.table-bordered>tbody>tr>td:last-child,.panel>.table-bordered>tfoot>tr>td:last-child,.panel>.table-responsive>.table-bordered>tfoot>tr>td:last-child{border-right:0}.panel>.table-bordered>thead>tr:first-child>td,.panel>.table-responsive>.table-bordered>thead>tr:first-child>td,.panel>.table-bordered>tbody>tr:first-child>td,.panel>.table-responsive>.table-bordered>tbody>tr:first-child>td,.panel>.table-bordered>thead>tr:first-child>th,.panel>.table-responsive>.table-bordered>thead>tr:first-child>th,.panel>.table-bordered>tbody>tr:first-child>th,.panel>.table-responsive>.table-bordered>tbody>tr:first-child>th{border-bottom:0}.panel>.table-bordered>tbody>tr:last-child>td,.panel>.table-responsive>.table-bordered>tbody>tr:last-child>td,.panel>.table-bordered>tfoot>tr:last-child>td,.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>td,.panel>.table-bordered>tbody>tr:last-child>th,.panel>.table-responsive>.table-bordered>tbody>tr:last-child>th,.panel>.table-bordered>tfoot>tr:last-child>th,.panel>.table-responsive>.table-bordered>tfoot>tr:last-child>th{border-bottom:0}.panel>.table-responsive{margin-bottom:0;border:0}.panel-group{margin-bottom:20px}.panel-group .panel{margin-bottom:0;border-radius:4px}.panel-group .panel+.panel{margin-top:5px}.panel-group .panel-heading{border-bottom:0}.panel-group .panel-heading+.panel-collapse>.panel-body,.panel-group .panel-heading+.panel-collapse>.list-group{border-top:1px solid #ddd}.panel-group .panel-footer{border-top:0}.panel-group .panel-footer+.panel-collapse .panel-body{border-bottom:1px solid #ddd}.panel-default{border-color:#ddd}.fa-volume-high:before,.fa-volume-up:before{content:"\f028"}.style{background:#5cb85c;color:#fff;border:1px solid}@media (max-width: 767px){#dpnl{border:1px solid;max-width:340px;min-width:300px;top:0;right:0;position:fixed;bottom:36px;width:calc(100vw - 104px)}}@media (min-width: 768px){#dpnl{border:1px solid;max-width:340px;min-width:300px;top:0;right:0;position:fixed;bottom:36px;width:340px}}@media (max-width: 767px){.label.label-danger.border.nosel.fa.fa-close.fr{margin-left:260px}}@media (min-width: 768px){.label.label-danger.border.nosel.fa.fa-close.fr{margin-left:300px}}.panel-default>.panel-heading{color:#333;background-color:#f5f5f5;border-color:#ddd}.panel-default>.panel-heading+.panel-collapse>.panel-body{border-top-color:#ddd}.panel-default>.panel-heading .badge{color:#f5f5f5;background-color:#333}.panel-default>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#ddd}.panel-primary{border-color:#337ab7}.panel-primary>.panel-heading{color:#fff;background-color:#337ab7;border-color:#337ab7}.panel-primary>.panel-heading+.panel-collapse>.panel-body{border-top-color:#337ab7}.panel-primary>.panel-heading .badge{color:#337ab7;background-color:#fff}.panel-primary>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#337ab7}.panel-success{border-color:#d6e9c6}.panel-success>.panel-heading{color:#3c763d;background-color:#dff0d8;border-color:#d6e9c6}.panel-success>.panel-heading+.panel-collapse>.panel-body{border-top-color:#d6e9c6}.panel-success>.panel-heading .badge{color:#dff0d8;background-color:#3c763d}.panel-success>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#d6e9c6}.panel-info{border-color:#bce8f1}.panel-info>.panel-heading{color:#31708f;background-color:#d9edf7;border-color:#bce8f1}.panel-info>.panel-heading+.panel-collapse>.panel-body{border-top-color:#bce8f1}.panel-info>.panel-heading .badge{color:#d9edf7;background-color:#31708f}.panel-info>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#bce8f1}.panel-warning{border-color:#faebcc}.panel-warning>.panel-heading{color:#8a6d3b;background-color:#fcf8e3;border-color:#faebcc}.panel-warning>.panel-heading+.panel-collapse>.panel-body{border-top-color:#faebcc}.panel-warning>.panel-heading .badge{color:#fcf8e3;background-color:#8a6d3b}.panel-warning>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#faebcc}.panel-danger{border-color:#ebccd1}.panel-danger>.panel-heading{color:#a94442;background-color:#f2dede;border-color:#ebccd1}.panel-danger>.panel-heading+.panel-collapse>.panel-body{border-top-color:#ebccd1}.panel-danger>.panel-heading .badge{color:#f2dede;background-color:#a94442}.panel-danger>.panel-footer+.panel-collapse>.panel-body{border-bottom-color:#ebccd1}.broadcasters{width:100%;border:none;padding:2px;height:54px;background:#<%=colors.btcolor %>}.broadcasters>div{max-width:350px}.broadcasters>div>.prod{float:right;color:#113233;background-image:url(imgs/mic.png);position:relative;left:3px;text-align:center;width:56px;border:2px solid #afbfc2!important;background-color:#fff;border-radius:30% 1%!important;margin:1px!important;height:47px!important;background-position:center!important;background-repeat:no-repeat!important;background-size:cover;border-radius:30% 1%!important}.broadcasters>div>.prod>hr{margin:0;border-top-color:#<%=colors.btcolor %>}.broadcasters>div>.prod>.evant>i{position:relative;font-size:16px!important;text-align:center;color:#<%=colors.btcolor %>;top:36px;height:18px;border-radius:50%;background-color:#ffffffc7;border:1px solid #d9534f70}.fr{float:right}.fl{float:left}.tbox{overflow:scroll;border-radius:2px;border:1px solid #d3d3d3;padding:6px;max-height:32px;min-height:32px;height:32px;font-weight:700;overflow:hidden;resize:none}.hid{display:none}.noflow{overflow:hidden}.hand{cursor:hand}img{vertical-align:middle}.break{overflow:auto;word-wrap:break-word;overflow-x:hidden;overflow-wrap:break-word}#cp .tab-content .tab-pane{min-width:400px!important}.u-ico{margin-top:1px;max-height:18px;object-fit:scale-down}.emoi{max-width:240px;max-height:20px}.unread{background-color:#ffc89d!important}.object-fit{object-fit:contain;object-position:center right}table,th,td{border:0}th,td{padding:0}.loginItms{position:absolute;display:none;border:2px solid #af020b;background-color:#f93634;border-radius:25px 0 0 25px;background-image:url(imgs/banner.png);background-repeat:no-repeat;background-size:contain;right:0;top:30px;width:250px}.loginImg{float:right;width:36px;height:36px;border:1px solid #ed5555;margin:1px;background-size:cover;background-repeat:no-repeat;border-radius:50%}.loginLogo{float:right;margin:1px;margin-top:-20px}.loginIco{float:right;margin:0 1px 1px;max-height:18px;background-color:#fff;padding:1px;border-radius:2px}.loginFlog{float:left;margin:2px 0 5px 5px;max-height:15px}.loginUserName{font-size:15px!important;float:right;font-family:arial;font-weight:9000;max-width:170px;min-width:100px;text-align:right;text-shadow:-1px 1px 2px #fff;color:#842c2a;white-space:nowrap;overflow:hidden!important;text-overflow:ellipsis}table{border-spacing:0;border-collapse:collapse}.mini{font-size: 13.68px !important;}.corner{border-radius:5px}.minix{ font-size: 12.16px !important;}.nosel,.u-ico,.u-pic,.tago{-webkit-touch-callout:none;-webkit-user-select:none;-khtml-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;cursor:default}.center{margin:0 auto}.dots{display:inline-block;white-space:nowrap;overflow:hidden!important;text-overflow:ellipsis;max-width:100%}.borderg{border:1px solid #f4f4f4}.border{border:1px solid #000}.fitimg{background-size:contain;background-repeat:no-repeat;background-position:50% 50%}.bord{border-inline:4px solid #1e90ff!important}html{height:100vh;width:100vw}.fa{text-align:left}.modal-header{padding:5px}.badge{padding:0 3px}.item3{margin-left:45px}button.btn{padding:5px}.d2{padding-bottom:2px}.d2,#rooms,#users{background-color:#<%= colors.hicolor %>!important}.u-msg{line-height:1.35!important}.ae{border:1px solid #000!important;border-radius:2px!important;float:left;text-align:center;width:20%;max-width:80px;padding:4px 0;margin:1px;margin-bottom:2px}.pmsgc{background-color:rgba(0,77,255,0.08)!important}.ppmsgc{background-color:#f1f1ff!important}.hmsg{background-color:#faf0e6!important}.label-primary,.btn-primary,.bg-primary,.label-primary:hover,.btn-primary:hover,.btn-primary:focus{background-color:#<%=colors.btcolor %>!important;background-image:none}.bg{background-color:#<%=colors.bgcolor %>!important}.bgg{background-color:#789}.pophead{background-color:slategrey}.light{background-color:#<%= colors.hicolor %>!important}.light.fl.pro.center.break{background-color:#<%= colors.hicolor %>!important}.label,.btn{border-radius:1px}.label-primary,.btn-primary{background-color:#000}.tablebox.d-flex.footer.fl.light{background-color:#<%= colors.hicolor %>!important}.fl.nosel.label.pnhead{background-color:#<%= colors.bgcolor %>!important}.label-primary{background-color:#337ab7}.label-primary[href]:hover,.label-primary[href]:focus{background-color:#286090}.label-success{background-color:#5cb85c}.hid{display:none}#mic .mic,#muteall{background:#<%=colors.btcolor %>}.typingIndicatorBubbleDot{width:4px;height:4px;margin-right:4px;background-color:#57585a;border-radius:50%;animation-name:bounce;animation-duration:1.3s;animation-timing-function:linear;animation-iteration-count:infinite}.mic{width:53px;height:50px;margin:1px;border-radius:4px;background-image:url(imgs/mic.png);background-size:cover;background-repeat:no-repeat;background-position:center}.typingIndicatorBubbleDot:first-of-type{margin:0 4px}.typingIndicatorBubbleDot:nth-of-type(2){animation-delay:.15s}.typingIndicatorBubbleDot:nth-of-type(3){animation-delay:.3s}.flex-grow-1{flex-grow:1!important}.c-flex{display:flex;flex-direction:column}.d-flex{display:flex}.flex-fill{flex:1 1 auto!important}@supports not ((-webkit-backdrop-filter: none) or (backdrop-filter: none)){.bgfbackground-color:rgba(0,0,0,.5)}.nav-pills li a{border-radius:0!important}@keyframes bounce{0%,60%,100%{transform:translateY(0)}30%{transform:translateY(-4px)}}td{border:1px solid grey;font-weight:700}th{background-color:#6495ed;color:#fff;padding:5px}.tab-pane{padding:0;width:100%}.pgr{-webkit-appearance:none;-moz-appearance:none;appearance:none}html{overflow:hidden}.tc{border-radius:3px!important}.bgf{backdrop-filter:blur(8px);-webkit-backdrop-filter:blur(8px)}@supports not ((-webkit-backdrop-filter: none) or (backdrop-filter: none)){.bgfbackground-color:rgba(0,0,0,.5)}td{overflow-y:hidden!important}.uzr .fitimg{background-size:cover}.uhtml{text-align:left;width:100%;margin:0!important;border-bottom:1px solid #e6e6fa;padding-right:1px;background-color:#ffffff}.uhtml .u-pic{min-width:52px;width:52px;min-height:48px;max-height:62px;background-color:#f3f3f3;margin-top:1px}.uhtml .co.ico{width:20px;height:20px;border-radius:1px;margin-top:-2px;margin-left:auto}.emoi{max-width:240px;max-height:20px}.emoi{height:20px;margin:3px;cursor:pointer}.emoii{width:22px;max-width:22px;height:22px;margin:0;padding:5px}</style>
 <style> *{font-family:serif;font-weight:700;text-shadow:none!important;font-size:15.2px!important;line-height:1.4!important;font-weight:700!important;text-shadow:none!important}</style></head>
<body class="bg" style="background-color:#40404f;height:100%;max-height:100%;min-height:100%; margin: 0px; padding: 0px; overflow: hidden; ">
  <div id="toast_container"><div id="message-box" class="message-box hide"><div class="box"><div class="content"><div class="icon"><i class="fa fa-wifi"></i></div><div class="details"><span>تم الإتصال</span><p></p></div></div></div></div><div id="message-box1" class="message-box hide"><div class="box orange"><div class="content"><div class="icon"><i class="fa fa-wifi"></i></div><div class="details"><span>يتم الإتصال بالسيرفر...</span><p></p></div></div></div></div><div id="message-box2" class="message-box hide"><div class="box red"><div class="content"><div class="icon"><i class="fa fa-wifi"></i></div><div class="details"><span>فشل الإتصال بالسيرفر...</span><p></p></div></div></div></div></div>
  <div style="width:100%; height:100%;max-height:100%;max-width:394px;padding:0px;" class="center-block bg dad">
    <div id="tlogins" class="border light fr break c-flex"
      style="z-index:1000;margin-left:-4px;height:100%;width:100%;max-width:390px;min-height:100%;">
      <a href="/" class="label bg d-flex fl" style="width:100%;padding:6px;border-radius:0px;text-align: left;"><img
          src="<%= logo %>" alt="" class="fl" style="margin-right: 2px; width:24px;height: 24px;"><span
          class="flex-grow-1 dots">
          <%= istite %>
        </span><span class="btn fr btn-success fa fa-refresh"
          style="margin:0px;margin-top:-2px;width: 39px;"></span></a>
      <% if(ifbanner){ %>
        <img src='/site/<%= host %>banner.png' class="isbnr fr" >
        <% } %>
          <ul class="nav  nav-tabs fl" style="margin:0px;background-color:white;height:44px;width: 100%;">
            <li class="active"><a data-toggle="tab" style="padding: 10px 6px;" class="  fa fa-user" href="#l1">دخول
                الزوار</a></li>
            <li><a data-toggle="tab" style="padding: 10px 6px;" class=" fa fa-user" href="#l2">دخول الاعضاء</a></li>
            <li><a data-toggle="tab" style="padding: 10px 6px;" class=" fa fa-user-plus" href="#l3"> تسجيل عضويه</a>
            </li>
          </ul>
          <div style="width:100%;height: 74px; background-color:  #<%= colors.hicolor %> !important;" class="tab-content fl">
            <div id="l1" style="padding:4px;width:100%;" class="tab-pane in active">
              <input style="width: 206px;margin-top:33px;padding:5px;border-radius: 2px;height:33px;" autocomplete="off"
                type="search" class="border " id="u1" placeholder="أكتب الاسم المستعار">
              <button style="margin-top:1px;" onclick="Login_(1);" aria-label="enter"
                class="btn btn-primary">دخول</button>
            </div>
            <div id="l2" style="padding:4px;width:100%;" class="tab-pane hid">
              <input style="width: 206px;padding:5px;border-radius: 2px;height:33px;" id="u2" class="border "
                placeholder="اكتب اسم العضو">
              <input style="width: 206px;padding:5px;border-radius: 2px;height:33px;" id="pass1" class="border "
                type="password" placeholder="اكتب كلمه المرور">
              <button onclick="Login_(2);" class="btn btn-primary">دخول</button>
              <span
                onclick="$(this).toggleClass('label-info');$('#stealth').prop('checked',$(this).hasClass('label-info'));"
                style="color: black;padding:4px;margin:2px;border-radius:4px;" class="btn fr fa fa-eye"></span>
              <input style="display: none;" id="stealth" type="checkbox" value="">
            </div>
            <div id="l3" style="padding:4px;width:100%;" class="tab-pane hid">
              <input style="width: 206px;padding:5px;border-radius: 2px;height:33px;" id="u3" class="border "
                placeholder="اكتب اسم العضو">
              <input style="width: 206px;padding:5px;border-radius: 2px;height:33px;" id="pass2" class="border "
                type="password" placeholder="اكتب كلمه المرور">
              <button onclick="Login_(3);" class="btn btn-primary">دخول</button>
            </div>
            <h6 class="hid">
              <%= title %>
            </h6>
            <a class="hid" href="/">
              <%= description %>

            </a>
          </div>
          <div title="المتواجدين الآن" class="bg mini fl"
            style="width:100%;border-radius:0px;padding: 0px;height: 25px;">
            <label id="loginstat" class="fl label loginstat label-info"
              style="border-radius: 1px;margin: 0px;height: 25px;min-width: 120px;">يتم الإتصال ..</label>
            <span id="s1" class="s1 fr fa fa-user label badgex label-success" style="border-radius: 0px;min-width: 52px;height: 25px;">
              <%= online %>
            </span>
          </div>
          <div class="lonline light break flex-grow-1" style="width:100%;outline: lightgray solid 1px;padding-left:1px;background-color: #fafafa;"></div>
          <%- namehost %>
        </div>
    <div id="room" style="height: 100%; width: 100%;display:none" class="break c-flex fr">
      <div class="broadcasters"></div>
      <div id="d2" onclick="$('.dpnl').hide();" class="d2  flex-grow-1 light    break" style="width: 100%; display: block;"></div>
      <div class="isreply" class="fl hid" style="background: aliceblue;"></div>
      <div onclick="$('.dpnl').hide();" class="tablebox d-flex footer fl light" style="width: 100%;height: 42px;padding: 4px;flex: 0 0 auto;">
        <button onclick="SEND_EVENT_EMIT('SEND_EVENT_EMIT_LEAVED_ROOM',{});" style=" margin-left:-2px;margin-top:2px;border-radius: 2px;" class="fa fa-sign-out fl btn btn-primary">&nbsp;</button>
        <img tabindex="0" role="button" data-toggle="popover" data-trigger="focus" class="fl nosel emobox"
          style="padding: 5px; width: 34px;" src="imgs/emoii.gif" />
        <textarea id="tbox"
          onclick="$('.pop').pop('hide');setTimeout(function(){$('#d2').scrollTop($('#d2')[0].scrollHeight);},250);"
          placeholder="اكتب رسالتك هنا" class="fl  tbox filw"
          style=" text-align: left;  text-align: left;
          margin-top: 2px;
          flex: 1 0 auto;  margin-top: 2px; background-color: rgb(170, 170, 175);"></textarea>
        <button onclick="Tsend();" style="margin-left: 2px; margin-top: 2px;"
          class="fa fa-send fl btn btn-primary">إرسال</button>
		<!-- <button onclick="Tclear();" style="margin-left: 2px; margin-top: 6px;padding:1px;outline:none" class="fl btncl btn btn-primary">مسح</button -->
      </div>
      <div id="d0" class="nosel fl  bg" style="padding-left: 1px; margin-top: 1px; width: 100%;  ">
        <button title="المتواجدين" href="#"
          onclick="$('#dpnl').show();$('#dpnl').find('.pnhead').text($(this).attr('title'));setTimeout(function(){$('#users').scrollTop(0);},100);$('#usearch').val('');$('#rsearch').val('');"
          data-toggle="tab" data-target="#users" class="ae fa label label-primary fa-user" style="width: 18%;"><span
            id="busers" style="padding:1px 4px;"></span></button>
        <button title="المحادثات الخاصه" href="#"
          onclick="$('#dpnl').find('.pnhead').text($(this).attr('title'));hl('#pmb','primary');setTimeout(function(){$('#users').scrollTop(0);},100);$('#dpnl').show();"
          data-toggle="tab" data-target="#chats" class="ae fa chats label label-primary fa-comment"
          style="width: 21%;" id="pmb"><span style="padding:1px 4px;"></span>خاص</button>
        <button title="غرف الدردشه" id="brooms" href="#"
          onclick="$('#dpnl').find('.pnhead').text($(this).attr('title'));$('#usearch').val('');$('#rsearch').val('');$('#dpnl').show();" data-toggle="tab"
          data-target="#rooms" class="ae fa label label-primary fa-users" style="width: 19%;"><span
            style="padding:1px 4px;"></span>الغرف</button>
        <button title="الحائط" href="#"
          onclick="$('#dpnl').find('.pnhead').text($(this).attr('title'));$('#dpnl').show();setTimeout(function(){$('#d2bc').scrollTop(0);});bcc=0;$(this).css('color', '').find('#bwall').text('');"
          data-toggle="tab" data-target="#wall" class="ae fa label label-primary fa-commenting"><span id="bwall"
            style="padding:1px 4px;"></span>الحائط</button>
        <button title="الإعدادات" href="#" onclick="$('#dpnl').find('.pnhead').text($(this).attr('title'));$('#dpnl').show();"
          data-toggle="tab" data-target="#settings" class="ae label label-primary fa fa-gear" style="width: 18%;"><span
            style="padding:1px 4px;"></span>الضبط</button>
      </div>
    </div>
    <div id="dpnl" class="dpnl bg tab-content c-flex" style="display: none; height: 590px!important;">
      <label class="fl nosel label pnhead"
      style="margin: 3px;padding-left: 9px;padding-right: 333px;">المتواجدين</label>
      <button onclick="$(this).parent().hide();" data-toggle="tab" data-target="#settings"
        class="label label-danger border nosel fa fa-close fr"
        style="margin-bottom: 0px; margin-right: 2px; border: 1px solid black; border-radius: 6px; padding: 6px 8px; width: 39px; margin-top: -35px;">
        &nbsp;
      </button>
      <div id="users" style="height: 100%; width: 100%;" class="light break tab-pane active">
        <textarea type="search" id="usearch" placeholder=".. البحث" class="tbox bg border"
          style="width: 100%; padding-left: 5px; display: block;"></textarea>
        <label
          style="margin: 0px !important; width: 100%; margin: 0px; border: none; padding: 4px; border-radius: 0px; display: none;"
          class="nosel ninr fl uzr label label-primary">المتواجدين في الدردشه</label>
        <div class='usr'></div>
      </div>
      <div id="mic" style="height: 100%; width: 100%;" class="break light tab-pane border"></div>
      <div id="chats" style="height: 100%; width: 100%;" class="break light tab-pane border"></div>
      <div id="top10" style="height: 100%; width: 100%;4%" class="break border light tab-pane">
        <p class="pane__back" style="position:absolute">
          <button class="btn btn-success fa fa-arrow-left" title="الحائط" href="#"
            onclick="$('.pnhead').text($(this).attr('title'));$('.dpnl').show();" data-toggle="tab" data-target="#wall"
            style=" margin-top: -23px; " class="ae label label-primary fa fa-gear">عودة</button>
        </p>
        <div class='tutop10' style="display:flex;justify-content: center;padding-top: 30px;">
          <div style='text-align: center;'>
            <img class='fitimg u-top2'
              style='padding: 2px;border-radius:50%;width:80px;height:80px;border:2px solid #000;margin: 10px 25px 10px 10px'><br>
            <img style='width: 50px;display:block;margin-top: -50px;margin-left: 24px;' src='imgs/silver.png?z1'>
            <span style='position: relative;font-family: cursive;width:100px;left: -7px' class='u-topic2 break'></span>
          </div>
          <div style='text-align: center;'>
            <img class='fitimg u-top1'
              style='padding: 2px;border-radius:50%;width:80px;height:80px;border:2px solid #000'>
            <img style='width: 45px;margin-block-start: -196px;' src='imgs/crown.png?z1'>
            <img style='width: 50px;display:block;margin-top: -60px;margin-left: 15px;' src='imgs/gold.png?z1'>
            <span style='position:relative;font-family: cursive;width:100px' class='u-topic1 break'></span>
          </div>
          <div style='text-align: center;'>
            <img class='fitimg u-top3'
              style='padding: 2px;border-radius:50%;width:80px;height:80px;border:2px solid #000;margin: 20px 10px 10px 25px'>
            <img style='width: 50px;display:block;margin-top: -48px;margin-left: 40px;' src='imgs/bronz.png?z1'>
            <span style='position: relative;font-family: cursive;width:100px;left: 9px' class='break u-topic3'></span>
          </div>
        </div>
        <div style="display:flex;text-align:center;color:#fff">
          <div
            style='width:100px;height:90px;background:#47ad61;box-shadow: 0px -10px 2px #7eca68;-webkit-text-stroke: thick;margin-top:10px'>
            <span style='position:relative;font-size:1cm !important'>2<br>
              <div class='rankt2' style="font-family: airal;font-size: 14px !important;-webkit-text-stroke: snow;">-
              </div>
            </span>
          </div>
          <div
            style='width:111px;height:100px;background:#69bb55;box-shadow: 0px -10px 2px #7eca68;-webkit-text-stroke: thick;'>
            <span style='position:relative;font-size:1cm !important'>1<div class='rankt1'
                style="font-family: airal;font-size: 14px !important;-webkit-text-stroke: snow;">-</div></span>
          </div>
          <div
            style='width:100px;height:80px;background:#47ad61;box-shadow: 0px -10px 2px #7eca68;-webkit-text-stroke: thick;margin-top:20px;'>
            <span style='position:relative;font-size:1cm !important'>3<div class='rankt3'
                style="font-family: airal;font-size: 14px !important;-webkit-text-stroke: snow;">-</div></span>
          </div>
        </div>
        <div class='ltop' style='height: auto;background:#fff'>
        </div>
      </div>
      <div id="wall"  class="break tab-pane border" style=" height: 100%; width: 101%; background: #fff;">
        <label title="مبدع الحائط" href="#"
          onclick="$('.pnhead').text($(this).attr('title'));$('.dpnl').show();GET_TOP_BAR()" data-toggle="tab" style="width: 49%; padding: 4px 10px 7px 11px; margin: 1px 0px 0px 0px; text-align: center; padding-left: 23px; min-height: 25px; color: #fff; min-width: 50%; background-color:#<%=colors.btcolor %>!important; height: 29px; float: right;"
          data-target="#top10" class="top wall"> <span class="fl fa fa-area-chart"></span> مبدع الحائط
        </label>
        <label  title="القصص" href="#" onclick="$('.pnhead').text($(this).attr('title'));$('.dpnl').show()" data-toggle="tab" style="width: 49%; padding: 4px 10px 7px 11px; margin: 1px 0px 0px 1px; text-align: center; color: #fff; padding-left: 23px; min-width: 49%; min-height: 25px; background-color:#<%=colors.btcolor %>!important; height: 29px;" data-target="#story" class=" story"><span class="fl fa fa-history"></span>القصص</label> 
        <div class="youtubeSearch" style="position: relative; float: right; width: 100%; top: 0;">
          <div class="youtubeLoad"
            style="display: none; text-align: center; position: absolute; right: 0; left: 0; background-color: #ffffff80;">
            <img style="margin-top: -9px; width: 60px;" src="imgs/icon.gif" />
          </div>
          <div style="display: flex; background-color: white; padding: 2px 0; border-bottom: 1px solid;">
            <i onclick="SEND_EVENT_EMIT('SEND_EVENT_EMIT_YOUTUBE', {'search':$('.youtubeVal').val()});setTimeout(()=>{$('.youtubeLoad').hide()},10000);$('.youtubeLoad').show()"
              style="float: left; font-size: 24px !important; color: #6d6b6b; font-weight: 100 !important; margin: 3px;"
              class="fa fa-search" aria-hidden="true"></i>

            <input style="text-align: center; float: right;" type="text" class="form-control youtubeVal"
              placeholder="البحث في يوتيوب" />
            <i style="float: right; font-size: 30px !important; color: red; font-weight: 100 !important; margin: 3px;"
              class="fa fa-youtube" aria-hidden="true"></i>
          </div>
          <div style="display: none;" class="vieYoutube">
            <img style="min-height: 60px; width: 100%; float: left;" alt="" />
            <span
              style="margin-top: -56px; float: left; background-color: #ffffffa1; color: red; padding: 2px 0; text-align: center; width: 100%;"
              class="dots youtubeTitl"></span>
            <button style="margin: 0 2px; width: 48%; margin-top: -28px; text-align: center;"
              class="youtubeSend fa fa-send fr btn btn-primary">ارسال الى الحائط</button>
            <button onclick="$('.vieYoutube').hide();"
              style="margin: 0 2px; width: 48%; margin-top: -28px; text-align: center;"
              class="youtubeCloos fa fl btn btn-primary">الغاء</button>
          </div>
        </div>
        <div id="d2bc" class="d2 light fl d2bc filh break" style="width: 100%; height: 438px!important;">
          <button class="fa fa-comments btn border rsall btn-info" onclick="$(this).parent().scrollTop(0);$(this).hide();"
            style="position: absolute; top: 80px; z-index: 1; left: 50px; width: 70%; text-align: center;display:none"
            id="bcmore">رسائل جديدة</button>
        </div>
        <div class="tablebox fl light" style="width: 100%;padding: 4px;position: absolute;bottom: 0; display: flex;">
          <button onclick="SEND_BC_UP(true);" style="margin-left: 2px; margin-right: 3px; margin-top: 2px;"
            class="fr fa fa-share-alt sndfilebc fl btn btn-primary"></button>
          <img tabindex="0" role="button" data-toggle="popover" data-trigger="focus" class="fl emobc"
            style="padding: 5px; width: 34px;" src="imgs/emoii.gif" />
          <textarea placeholder="اكتب رسالتك هنا" class="fl tbox  tboxbc filw"
            style="width: 49px;  text-align: left;margin-top: 2px; flex: 1 0 auto; "></textarea>
          <button onclick="SEND_BC_UP();" style="margin-top: 2px; margin-left: 2px;"
            class="fa fa-send sndbc fl btn btn-primary">  إرسال </button>
        </div>
      </div>
      <div id="rooms" style="height: 100%; width: 100%;" class="light border break tab-pane">
        <div style="width: 100%; margin: 0px; border: none; border-radius: 0px; display: flex;" class="nosel label-primary fl bgg">
          <textarea type="search" id="roomSearchInput" placeholder="البحث عن رقم الغرفه #.." class="tbox-custom sm flex-grow-1 tbox bg border" style=" height:37px!important;padding-left:5px;display:inline;border-radius: 0px; "></textarea>
          <button onclick="mkr(); changeRandomNumber();" class="border btn label label-success fl fa fa-plus" style="margin: 0px;">غرفه
            جديدة</button> </div>
        <div class='rmr'></div>
      </div>
      <div id="chats" style="height: 100%; width: 100%;" class="light break border tab-pane"></div>
      <div id="story" style="height: 100%; width: 100%;" class="light border break tab-pane">
        <p class="pane__back" style="margin: 0 0 0px;">
          <button class="btn btn-success fa fa-arrow-left" title="الحائط" href="#"
            onclick="$('.pnhead').text($(this).attr('title'));$('.dpnl').show();" data-toggle="tab"
            data-target="#wall" style=" margin-top: 0px; " class="ae label label-primary fa fa-gear">عودة</button>
        </p>
        <div style="width:295px" id='f1e'></div>
        <div class='itemstory' style="display:inline-flex;flex-wrap: wrap;">
          <div class='addstory' style="display:none;text-align: center;margin-left: 5px;margin-top: 10px;width: 65px;">
            <img class='img_str' style="border-radius:50%;width:60px;height:60px;border:1.5px solid red;padding:2px">
            <i onclick="SEND_Story()" class='fa fa-plus-circle'
              style="font-size: 20px !important;position: absolute;margin-top: 35px;margin-left: -15px;background: #fff;border-radius: 50%;"></i>
          </div>
        </div>
      </div>

      <div id="settings" style="height: 100%;width:100%;padding:0px;" class="break light tab-pane ">
        <div class="borderg corner" style="background-color: white;width: 100%;margin-top:-2px;">
        <div style="display: inline-block;width: 132px;margin-top: 2px;height: 25px;padding:1px;" class=" label label-primary  ">الزخرفه</div>
        <br> <input class="stopic dots" style="width:99%;">
        <br>
        <div style="display: inline-block;width: 132px;margin-top: 1px;height: 25px;padding:1px;" class="   label label-primary">الحاله</div>
        <br><input class="smsg dots" style="width:99%;">
        <br>
        <div class="label label-primary " style=" display: inline-block;width: 132px;margin-top: 1px;height: 25px;padding:1px;">رابط اليوتيوب</div>
        <br />
        <input placeholder="  ادخل رابط الفيديو هنا" class="urluto" style="padding: 5px; margin: 1px;width: 80%!important;border-radius: 0px!important;border: 0px solid #9E9E9E!important;background: #e8e8e8!important;">
        <a class="border label mini label-success handfa fa fa-plus fr setsave" style="width: 18%;text-align: center;background: #1dba0a;margin: 1px 3px 0px 0px;height: 31px;border: 0;padding: 7px;display: block;" onclick="SEND_EVENT_EMIT('PROFILE_ADD_YOUTUBE',{youtube: $('.urluto').val()});"></a>
        <br />
        <div style="display: flex; margin-top: .5rem">
          <div style="display: inline-block;width: 132px;margin-top: 1px;height: 25px;padding:1px;"
            class="label label-primary">لون الإسم</div>
          <%- include('partials/color-template.ejs', {name: 'scolor' })%>
        </div>
        <div style="display: flex; margin-top: 4px">
          <div style="display: inline-block;width: 132px;margin-top: 1px;height: 25px;padding:1px;"
            class="label label-primary">لون الخط</div>
          <%- include('partials/color-template.ejs', {name: 'mcolor' })%>
        </div>

        <div style="display: flex; margin-top: 4px">
          <div style="display: inline-block;width: 132px;margin-top: 1px;height: 25px;padding:1px;"
            class="label label-primary">لون الخلفيه</div>
          <%- include('partials/color-template.ejs', {name: 'sbg' })%>
        </div>
        
        <div style="display: flex; margin-top: 4px">
          <div style="display: inline-block;width: 132px;margin-top: 1px;height: 25px;padding:1px;"
            class="label label-primary">حدود الصوره</div>
          <%- include('partials/color-template.ejs', {name: 'scopic' })%>
        </div>
        <div style="display: inline-flex; margin-top: 4px">
          <div style="display: inline-block;width: 132px;margin-top: 1px;height: 25px;padding:1px;"
            class="label label-primary">لون الحاله</div>
          <%- include('partials/color-template.ejs', {name: 'mscolor' })%>
        </div>
        <button class="border btn mini btn-success hand fl" style="padding:6px;width: 98%;margin-left:3px;margin-top: 2px;margin-bottom: 4px;" onclick="setprofile();">
        <span class="fl fa fa-edit"></span>
        حـفـظ</button>
        </div>
        <button class="border label mini label-success hand fa fa-edit fr prof" style="width: 98%;text-align: center;margin: 1px 4px;height: 37px;padding: 9px;background-color: crimson !important;border: 1px solid rgb(255, 255, 255) !important;color: white;font-size: 14px !important;font-weight: 900 !important;" onclick="$('.listHome').slideToggle(); atars()"> صمم عضويتك بنفسك </button>
        <div style="display: none;" class="listHome">
        <div class="listitem">
        <div onclick="$('.listHome').slideToggle();" class="getborder4 desginliste mb-4">
        <img src="no.png" onclick="addback(' ',1)"/>
        <div id="back" style="display: flex;"></div>
        </div>
        <div onclick="$('.listHome').slideToggle();" class="getborder4 desginlist mb-4" >
        <img src="no.png" onclick="addback(' ',2)"  />
        <div id="atar" style="display: flex;"></div>
        </div>
        </div>
</div>
        <select id="zoom" style="width: 98%;margin:1px 4px;" class="fl btn btn-primary" onchange="document.body.style.zoom=$(this).val();setv('zoom',$(this).val());fixSize();">
        <option seleceted="seleceted" value="1">%100 - حجم الخطوط</option>
        <option value="1.20">%120 - حجم الخطوط</option>
        <option value="1.10">%110 - حجم الخطوط</option>
        <option value="1.05">%105 - حجم الخطوط</option>
        <option value="0.95">%95 - حجم الخطوط</option>
        <option value="0.9">%90 - حجم الخطوط</option>
        </select>
        <select id="turn_server" style="width: 98%;margin:1px 4px;" class="fl btn btn-primary" onchange="turn_server=parseInt($(this).val());setv('turn_server',$(this).val());">
        <option seleceted="seleceted" value="1">السيرفر الصوتي: الاساسي</option>
        <option value="2">السيرفر الصوتي: TCP</option>
        <option value="3">السيرفر الصوتي: UDP</option>
        <option value="4">السيرفر الصوتي: TCP+Relay</option>
        <option value="5">السيرفر الصوتي: UDP+Relay</option>
        <option value="6">السيرفر الصوتي: openrelay</option>
        </select>
        <label onclick="S_PIC('user');" style="background-color: ghostwhite;color: black;margin:1px 4px; padding:6px;width:98%;" class="label changepic tc border  btn  label-info fl"><img style="width:32px;height:32px;margin:-6px;background-size: cover;background-position: center;" class="fitimg fl borderg spic corner hand">تغير الصوره</label>
        <label onclick="SEND_EVENT_EMIT('SEND_EVENT_EMIT_PIC',{pic: 'site/<%= host %>pic.png?z'});" style="color: black;margin:1px 4px; padding:6px;width:98%;" class="label tc border btn  label-danger fl"><span class="fl fa fa-user-times"></span>حذف الصوره</label>
        <label onclick="if (nopm){nopm=false;$(this).find('span').removeClass('fa-check');}else{nopm=true;$(this).find('span').addClass('fa-check');};SEND_EVENT_EMIT('SEND_EVENT_EMIT_BUSY',{busy:nopm});" style="background-color: ghostwhite;color: black;margin:1px 4px; padding:6px;width:98%;" class="label tc border  btn   fl"><span class="fa fl"></span>تعطيل المحادثات الخاصه</label>
        <br>
        <label onclick="if (nonot){nonot=false;$(this).find('span').removeClass('fa-check');}else{nonot=true;$(this).find('span').addClass('fa-check');} ;" style="background-color: ghostwhite;color: black;margin:1px 4px; padding:6px;width:98%;" class="label tc border  btn  bb4 fl"><span class="fa fl"></span>تعطيل التنبيهات</label>
        <br>
        <label onclick="pmsg();" style="background-color: ghostwhite;color: black;margin:1px 4px; padding:6px;width:98%;" class="label tc border pmsg  btn  label-info fl"><span class="fl fa fa-send"></span>إرسال إعلان</label>
        <br>
        <label onclick="if(M_ROOM!=null){redit(M_ROOM);}" style="background-color: ghostwhite;color: black;margin:1px 4px; padding:6px;width:98%;" class="label tc border redit  btn  label-info fl"><span class="fl fa fa-home"></span>إداره الغرفه</label>
        <br>
        <label  onclick="window.open('/cp?'+MY_T,'_blank')" style="background-color: ghostwhite;color: black;margin:1px 4px; padding:6px;width:98%;" class="label tc border cp  btn  label-danger fl"><span class="fl fa fa-star"></span>لوحه التحكم</label>
        <br>
        <label onclick="logout();" style="margin:1px 4px; padding:6px;width:98%;" class="label border btn  label-danger tc fl"><span class="fl fa fa-sign-out"></span>تسجيل خروج</label>
        </div>
        </div>

        <div class="modal" id="upro" role="dialog" style="z-index:2100">
          <div class="modal-dialog ">
            <div class="modal-content" style="width:340px;margin:-1px;">
              <div style="color:white;margin-top:-1px;" onclick="$(this).parent().parent().parent().modal('hide');"
                class="modal-header label-primary">
                <span class="pull-right clickable badge"><i class="fa fa-times"></i></span>
                <label style="margin:1px;max-width:90%;" class="mini dots nosel modal-title">إنشاء غرفه جديدة</label>
              </div>
              <div class="modal-body" style="padding:1px;">
                <div class="light fl pro break" style="width:100%;border:1px solid lightgrey; padding:0px;margin:0px;">
                  <center>
                    <div style="width:100%;height:200px;background-size: cover;" class="fitimg fl u-pic2 ">
                      <div style="width:100%;height:100%;" class="fitimg bgf fl u-pic "></div>
                      <img style="width: 38px; padding: 0; position: absolute; right: 0; margin: 166px 2px 0px 0px;display:none" class="isyoutube" src="imgs/img1.gif">
                    </div>
                    <label style="width:100%;text-align:end;margin-bottom:0px;" class=""> 
                      <div style="width:100%;padding:2px;text-align: center;" class="fl u-msg"></div>
                      <div class="fl mini u-co" style="margin:4px;"></div>
                      <div class="fr u-room"></div>
                    </label>
                  </center>
        
                  <span class="fl fa fa-comment  btn upm borderg"
                    style="color:black;margin:2px;width: 106px;text-align: center;">محادثه خاصه</span>
                  <span class="fl fa fa-envelope-o btn unot borderg"
                    style="color:black;margin:2px;width: 106px;text-align: center;">تنبيه</span>
                  <span class="fl fa fa-heart btn ulike borderg"
                    style="margin:2px;color:red;width: 106px;text-align: center;">0</span>
                  <span class="fl fa fa-diamond btn ugift borderg"
                    style="color:blue;margin:2px;margin-top:4px;width: 106px;text-align: center;">ارسل هديه</span>
                    <span class="fl fa fa-mutum  btn meiutbc  borderg"
                    style="color:black;margin:2px;margin-top:4px;width: 106px;text-align: center;"> اسكات الحائط</span>
                  <span class="fl fa fa-mutum  btn meiut  borderg"
                    style="color:black;margin:2px;margin-top:4px;width: 106px;text-align: center;">اسكات</span>
                   <span class="fl fa fa-star btn ubnr borderg"
                    style="color:deeppink;margin:2px;margin-top:4px;width: 106px;text-align: center;">البنر</span>
                    <span class="fl fa fa-search btn uh borderg"
                    style="color:black;margin:2px;margin-top:4px;width: 106px;text-align: center;">كشف النكات</span>                
                  <span class="fl fa fa-ban btn udelpic borderg"
                    style="color:maroon;margin:2px;margin-top:4px;width: 106px;text-align: center;">حذف الصوره</span>
                  <span class="fl fa fa-user-times btn urkick borderg"
                    style="color:darkorchid;margin:2px;margin-top:4px;width: 106px;text-align: center;">طرد من الغرفه</span>
                  <span class="fl fa fa-ban btn ukick borderg"
                    style="color:crimson;margin:2px;margin-top:4px;width: 106px;text-align: center;">طرد</span>
                  <span class="fl fa fa-ban btn uban borderg"
                    style="color:crimson;margin:2px;margin-top:4px;width: 106px;text-align: center;">باند</span>
                  <span class="fl fa fa-warning btn ureport borderg"
                    style="color:black;margin:2px;margin-top:4px;width: 106px;text-align: center;">تبليغ</span>
                  <span class="fl fa fa-ban btn umute borderg"
                    style="color:red;margin:2px;margin-top:4px;width: 106px;text-align: center;">تجاهل</span>
                  <span class="fl fa fa-check btn uunmute borderg"
                    style="color:red;margin:2px;margin-top:4px;width: 106px;text-align: center;">إلغاء التجاهل</span>
                  <div class="border nickbox fl" style="padding:4px;margin-top:2px;width:100%;">
                    <label class="label fl label-primary" style="height: 32px;padding: 8px;">الزخرفه</label>
                    <textarea class="borderg corner  fl u-topic"
                      style="height:32px;padding:4px;width:60%;resize:none;"></textarea>
                    <label class="btn btn-primary u-nickc fr fa fa-save">تغير</label>
                  </div>
                  <div class="border fl likebox" style="width:100%;padding:4px;margin-top:2px;">
                    <label style="color:red;">♥الايكات</label>
                    <input type="number" style="width: 160px;" class="likec">
                    <span class="fa fr fa-check btn ulikec border" style="margin:2px;">حفظ</span>
                    
                  </div>
                  <div class="border fl evalbox" style="width:100%;padding:4px;margin-top:2px;">
                  <label style="color:blueviolet;">النقاط</label>
                  <input type="number" style="width: 160px;" class="eval">
                  <span class="fa fr fa-check btn uevac border" style="margin:2px;">حفظ</span>
                </div>
                <div class="border fl msgbox" style="width:100%;padding:4px;margin-top:2px;">
                  <label style="color:seagreen;">الحاله</label>
                  <input type="text" style="width: 160px;" class="usmsg">
                  <span class="fa fr fa-check btn usmsgs border" style="margin:2px;">حفظ</span>
                </div>
                <div class="border fl youbox" style="width:100%;padding:4px;margin-top:2px;">
                  <label style="color:saddlebrown;">يوتيوب ▶</label>
                  <input type="text" style="width: 160px;" class="uyou">
                  <span class="fa fr fa-check btn uyoutu border" style="margin:2px;">حفظ</span>
                </div>
                  <div class="border fl powerbox" style="width:100%;padding:4px;margin-top:2px;">
                    <label>المجموعه</label><br>
                    <input id="upsearch" style="width:200px;display: block;margin-bottom: 2px;" placeholder="البحث في الصلاحيات"> 
                    <select style="width:200px;display:inline;" class="userpower selbox form-control">
                    </select><br>
                    <label>المده بالأيام</label><br>
                    <input type="number" class="userdays">
                    <span class="fa fr fa-check btn upower border" style="margin:2px;">حفظ</span>
        
                  </div>
                  <div class="border fl roomzbox" style="width:100%;padding:4px;margin-top:2px;">
                    <label>الغرفه</label><br>
                    <select style="width:200px;display:inline;" class="roomz selbox form-control">
                    </select><br>
                    <label>كلمه المرور(إختياري)</label><br>
                    <input type="text" class="rpwd">
                    <span class="fa fr fa-check btn uroomz border" style="margin:2px;">نقل</span> 
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      <x id="uhtml" style="display:none;">
        <div class="fl hand nosel uzr uhtml">
        <div class="d-flex fl di1">
        <img class="ustat" alt>
        <img class="fitimg fl imgbaqer borderg" style="display: none;" />
        <div class="fitimg u-pic">
        </div>
        <div class="di2 flex-grow-1 break">
        <div class="d-flex dots">
        <span class="muted fa"></span>
        <img class="u-ico" alt>
        <div class="flex-grow-1"><span class="u-topic"></span></div>
        </div>
        <div style="color:#888;padding:1px;max-height: 40px; text-align: center;" class="mini u-msg"> </div>
        </div>
        </div>
        <div class="di3 c-flex fr">
        <img class="co ico" alt>
        <span class="uhash"></span>
        </div>
        </div>
        </x>
        <x id="rhtml" style="display:none;">
          <div class="nosel d-flex room"
            style="text-align:left;width:100%; padding:0px;border-bottom:1px solid #efefef;">
     
                <div
                  style="width: 80px;min-width: 80px;min-height: 64px;max-height: 110px;border: 2px solid rgb(255, 255, 255);background-color: rgba(32, 32, 32, 0.12);background-position: center;background-size: cover;" class="d-flex fitimg u-pic" />
                  <div class="st flex-grow-1" style="color:white;text-align: left;height: 20px;"></div>
                  <div class="roomhas" style="color:#fafafa;text-align: left;padding: 0px 4px;background-color: #2020204f;height: 20px;"></div>
                </div>
                <div class="c-flex flex-grow-1 break" >
                  <div class="d-flex">
                    <span  style=" color:whitesmoke;background-color: #20202008;padding-left:2px;"
                    class="u-topic dots flex-grow-1"></span> 
                    <span class=" label label-primary fa fa-user uc"
                    style="z-index:10;padding:1px;border-radius: 1px;;width: 70px;text-align: center;margin-left:auto;margin-bottom:-24px;margin-right:1px;height: 21px;"></span>
                  </div> 
                  <div style="color:#888; text-align: center; background-color: #dfdfdf1f;" class="mini u-msg flex-grow-1"></div>
                </div>  
          </div>
        </x>
        <x id="callnot" style="display:none;">
          <div class="border bgg" style="position:absolute;top:60px;margin-left:60px;z-index:9999;width:260px;padding:4px;">
          <div class="uzer">
          </div>
          <div id="audiocall">
            <button style="position: absolute;
            right: 0;
            top: 60px;" class="label bg-warning callstat">..</button>
          </div>
          <video style="display: none;" id="remote-video"></video>
          <video style="display: none;"id="local-video"></video>
          <button class="btn btn-success callaccept fa fa-phone">قبول</button>
          <button class="btn btn-danger calldeny fa fa-phone">رفض</button>
          <button onclick="meuted()" class="btn fa fa-microphone-slash meutee"></button>
          <button onclick="volumeup();" class="btn fa fa-volume-high vulomeup"></button>
          </div>
          </x>
        <x id="utop" style="display: none;">
          <div class="hand nosel fl uzr borderg"
            style="padding:5px;text-align: left; background-color: white; border-radius: 0px !important; width: 100%; margin: 0px !important; border-top: 0px; padding-right: 1px;">
            <span class="fl ntop"
              style="color:#666;padding: 10px;text-align: center;font-weight: bold;font-family:sans-serif,'trebuchet ms','lucida grande','lucida sans unicode',arial,helvetica">1</span>
            <img
              style="min-width: 52px; width: 52px; height: 46px; background-color: rgb(243, 243, 243);border-radius: 50%;"
              class="fitimg fl u-pic borderg" />
            <text class="fr co"
              style="padding:10px;color:#666;text-align: right;font-weight: bold;font-family:sans-serif,'trebuchet ms','lucida grande','lucida sans unicode',arial,helvetica" /></text>
            <div style="width: 150px; padding: 2px 4px;" class="fl break">
              <div style="width: 100%; margin-top: -2px; display: flex;" class="fl">
                <div class="fl" style="width: 82%;"><span
                    style="font-family:sans-serif,'trebuchet ms','lucida grande','lucida sans unicode',arial,helvetica;margin-top: 10px; padding: 0px 2px; max-width: 100%; border-radius: 3px;font-size:13px !important"
                    class=" u-topic dots"></span></div>
              </div>
            </div>
          </div>
        </x>
        <x id="pop" class="hid">
          <div class="bgg "style="outline: 1px solid gray; overflow-y: hidden; display: none; position: absolute; top: 1px; min-height: 180px; max-height: 500px; height: 50%; width: 99%; max-width: 500px; padding-bottom: 25px; z-index: 10;">
            <div style="width: 100%; height: 30px;" class="head nosel bg fl">
              <label class="label fl hand fa fa-info title" style="margin: 2px; margin-right: 2px;">&nbsp;</label>
              <label style="padding: 8px;" onclick="$(this).parent().parent().remove();"
                class="btn minix btn-danger pphide fr border fa fa-close">
                &nbsp;&nbsp;
              </label>
            </div>
            <div class="body fl filh" style="min-height: 100%; width: 100%; height: 100%; overflow: hidden;"></div>
          </div>
        </x>

        <x id="cw" class="hid">
          <div class="c-flex bgg border"
            style="border-radius: 2px;display:none;position:absolute;top:1px;min-height:190px;max-height:500px;bottom:46%;width:99.8%;max-width:500px; ">
            <div style="width:100%;height:30px;" class="head d-flex nosel bg fl">
              <label class="label fl hand fa border fa-user" style="margin:2px;margin-right: 2px;">&nbsp;</label>
              <div class="fl d-flex uzr flex-grow-1 dots" style="margin: 3px; flex: 0 1 auto;">
                <img class="fl ustat" style="width:4px;height:22px;" src="imgs/s0.png">
                <img style="width: 22px; height: 22px; background-image: url();" class="fitimg fl hand u-pic ">
                <div style="width: 100%; margin-top: 0px; flex: 0 1 auto;" class="fl dots d-flex flex-grow-1">
                  <img class="fl u-ico" alt="" src="">
                  <span style="max-width: 100%; padding: 0px 4px; border-radius: 0px; margin-left: 1px; color: rgb(0, 0, 0);" class=" nosel flex-grow-1 u-topic dots"></span>
                  <span class="uhash" style="color: white;padding: 2px;"></span>
                  
                </div>
              </div>
              <label style="padding:8px;"
                onclick="var pp=$(this).parent().parent();if($(this).hasClass('fa-expand')){pp.css('bottom','114px');}else{pp.css('bottom','46%');}$(this).toggleClass('fa-expand fa-compress');fixSize();"
                class="btn   btn-info   fr border fa fa-expand">&nbsp;&nbsp;</label>
              <label style="padding:8px;" class="btn minix btn-danger phide fr border fa fa-minus">&nbsp;&nbsp;</label>
            </div>
            <div class="cont break fr c-flex flex-grow-1" style="width:100%;">
              <div class="d2 flex-grow-1 break light">
              </div> 
              <div class="tablebox footer d-flex light fl" style="width:100%;height:42px;padding:4px;">
                <div class="typ" style="display: flex;position: absolute;right:20px;margin-top:-12px;">
                  <div class="typingIndicatorBubbleDot"></div>
                  <div class="typingIndicatorBubbleDot"></div>
                  <div class="typingIndicatorBubbleDot"></div>
                </div>
                <span class="btn btn-success fa fa-phone callx call" style="margin-right:3px;margin-top:2px;border-radius: 2px;"></span>
                <button style="margin-right:3px;margin-top:2px;border-radius: 2px;"
                  class="fr fa fa-share-alt sndfile fl btn btn-primary">&nbsp;&nbsp;&nbsp;&nbsp;</button>
                  <button style="margin-top: 2px; margin-left: 2px;"
                  class="fa fa-microphone microphone sot fl btn btn-success">&nbsp;&nbsp;&nbsp;</button>
                <button style="margin-top: 2px; margin-left: 2px; display: none;"
                  class="fa fa-stop stopmico fl btn btn-success">&nbsp;&nbsp;&nbsp;</button>
                  <img tabindex="0" role="button" data-toggle="popover" data-trigger="focus" class="fl nosel emob"
                  style="padding: 5px; width: 34px;" src="imgs/emoii.gif" />
                <textarea placeholder="اكتب رسالتك هنا" class="fl flex-fill tbox"></textarea>
                <button style="margin-left:2px;" class="fa fa-send sndpm fl btn btn-primary">&nbsp;&nbsp;&nbsp;</button>
              </div>
            </div>
          </div>
        </x>
        <div class="modal" id="cooment" role="dialog" style="margin: 5px;">
          <div class="modal-dialog fr break" style="height: 50%; min-height: 300px; overflow: visible; margin-top: 2px;">
          <div class="modal-content c-flex break" style="width: 306px; height: 100%; position: relative; right: 70px;">
          <div style="color: white;" onclick="$(this).parent().parent().parent().modal('hide');" class="modal-header bg-primary">
          <span class="pull-right clickable badge"><i class="fa fa-times"></i></span><label style="margin: 1px;" class="mini fa fa-comments modal-title">التعليقات</label>
          </div>
          <div class="modal-body break flex-grow-1 c-flex" style="display: contents; padding: 0px;">
          <div style="width: 100%; border-bottom: 1px solid black;" id="rmsg"></div>
          <div class="break flex-grow-1 r rplaymsg" style="height: 156px; background-color: #efefef; border-left: 4px solid grey;"></div>
          <div id="coomentes" class="tablebox footer d-flex light fl" style="width: 100%; height: 42px; padding: 4px;">
          <img role="button" class="fl nosel emox" style="padding: 5px; width: 34px; height: 34px; " src="imgs/emoii.gif" data-toggle="popover" />
          <textarea placeholder="اكتب رسالتك هنا" class="fl flex-fill tbox"></textarea>
          <button style="margin-left: 2px;" class="fa fa-send sndpm fl btn btn-primary">
          &nbsp;&nbsp;&nbsp;
          </button>
          </div>
          </div>
          </div>
          </div>
          </div>
          <x id="umsg" style="display:none;">
            <div class="uzr d-flex"
              style="border-bottom:1px solid lavender;margin:0px;width:100%;padding:0px;background-color: #fafafa;padding-left: 2px;">
              <div style="min-width:52px;width:52px;height:46px; background-color:#f3f3f3;"
                class="fitimg u-pic borderg">
              </div>
              <div class="uzr flex-fill break" style="padding-top: 1px;padding-left:1px;">
                <div style="width:100%;display: flex;height: 21px;" class="d-flex">
                 <div class="d-flex flex-grow-1" style="overflow: hidden;"> <img class="u-ico" style="min-height: 14px;">
                  <span style="padding:1px 4px;display:block;margin-left:1px;border-radius: 2px;"
                    class="nosel u-topic dots hand"></span></div>
                  <div class="flex-grow-1 d-flex" style="height: 21px;overflow-y: visible;"> 
                    <span class="flex-grow-1 "></span> 
                    <span style="padding:0px 2px;margin-right:4px;color:grey;text-align: end;"
                      class="minix tago">..</span>
                  </div>
                </div>
                <div style="padding:0px 5px; width:100%;padding-right:1px;padding-bottom: 1px;" class="u-msg break"></div>
                <div class="d-flex fr" style="max-width: 92px;height: 21px;">
                  <button style="padding:3px;width: 28px;"
                  class="blike corner btn minix btn-danger fa fa-heart"></button>
                <button style="padding:3px;width: 28px;"
                  class="breply corner btn minix btn-primary fa fa-comments"></button>
                <button style="padding:3px;width: 26px;"
                  class="bdel corner btn minix btn-primary fa fa-times"></button>
                </div> 
                <div class="u-rply" class="hid fl">
                </div>
              </div>
            </div>
          </x> 

        <x id="not" class="hid">
          <div onclick="$(this).remove();" style="min-width:180px;max-width:260px;border:1px solid black;z-index:2110;background-color:#efefef;position:absolute;top:30%;margin-left:30px;padding:5px; " class="hand corner nosel">
          <center>
          <div  class="corner border label label-primary" style="padding-top:6px;padding-left:15px;width:50%;padding-right:15px;">تنبيه</div>
          </center>
          </div>
          </x>

        <div class="modal" id="mnot" role="dialog" style="z-index: 5001 !important">
          <div class="modal-dialog">
            <div class="modal-content" style="width: 310px;">
              <div style="color: white;" onclick="$(this).parent().parent().parent().modal('hide') ;"
                class="modal-header label-primary">
                <span class="pull-right clickable badge"><i class="fa fa-times"></i></span>
                <label style="margin: 1px;" class="mini fa fa-comments modal-title">إعلان</label>
              </div>
              <div class="modal-body" style="padding: 1px;">
                <div class="break" style="background-color: #efefef; padding: 5px;">
                  <textarea placeholder="اكتب رسالتك هنا" class="fl "
                    style="width: 100%; resize: none; direction: rtl; text-align: left;"></textarea>
                  <label class="checkbox-inline"><input class="ispp" type="checkbox" value="" />إعلان خاص
                    للسوابر؟</label><button class="rsave btn btn-primary fr"><span
                      class="fa fa-send">إرسال</span></button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="modal" id="mkr" role="dialog" style="z-index: 6000">
          <div class="modal-dialog">
            <div class="modal-content" style="width: 310px;">
              <div style="color: white;" onclick="$(this).parent().parent().parent().modal('hide') ;"
                class="modal-header label-primary">
                <span class="pull-right clickable badge"><i class="fa fa-times"></i></span>
                <label style="margin: 1px;" class="mini fa fa-comments modal-title">إنشاء غرفه جديدة</label>
              </div>
              <div class="modal-body" style="padding: 1px;">
                <div class="break" style="background-color: #efefef; padding: 5px;">
                  <input class="rtopic" style="width: 200px;" type="text" placeholder="عنوان الغرفه" />
                  <div style="position: absolute; display:inline-block; margin-left: 4px">
                  </div>
                  <div style="position: absolute; display:inline-block; margin-left: 4px">
                  </div>
                  <img class="picroom" onclick="S_PIC('room')" style="position: absolute; width: 80px; height: 80px; top: 56px; border: 2px solid; margin-left: 9px;" src='site/<%= host %>room.png'>
							<button style="width: 84px;position: absolute;top: 140px;padding: 2px !important;height: 25px;right: 8px;" onclick='$(".picroom").attr("src", "site/<%= host %>room.png");' class=" borderbtn">بدون صورة</button>
                  <input class="rabout" style="width: 200px;" type="text" placeholder="الوصف" />
                  <input class="rwelcome" style="width: 200px;" type="text" placeholder="رساله الترحيب" />
                  <input class="rlike" style="width: 200px;" type="number" placeholder="عدد لايكات الدخول للغرفة" min="0"
                    max="1000000000000" />
                  <input class="rpwd" style="width: 200px;" type="password" placeholder="كلمه المرور" />
                  <input class="rmax" style="width: 200px;" type="number" placeholder="حجم الغرفه من 2 ألى 40" min="2" max="1000" />
                    <input class="rhas" style="width: 200px;" type="number" placeholder="" min="1" max="100" />
                    <div style="display: flex; margin-top: .5rem">
                      <div style="display: inline-block;width: 132px;margin-top: 1px;height: 25px;padding:1px;"
                        class="label label-primary">لون اسم الغرفة</div>
                      <%- include('partials/color-template.ejs', {name: 'roomcolor' })%>
                    </div>
                    <div style="display: flex; margin-top: 4px">
                      <div style="display: inline-block;width: 132px;margin-top: 1px;height: 25px;padding:1px;"
                        class="label label-primary"> حدود الصورة</div>
                      <%- include('partials/color-template.ejs', {name: 'roomcolorpic' })%>
                    </div>
                    <div style="display: flex; margin-top: 4px">
                      <div style="display: inline-block;width: 132px;margin-top: 1px;height: 25px;padding:1px;"
                        class="label label-primary"> لون الوصف</div>
                      <%- include('partials/color-template.ejs', {name: 'colormsgroom' })%>
                    </div>
                    <div style="display: flex; margin-top: 4px">
                      <div style="display: inline-block;width: 132px;margin-top: 1px;height: 25px;padding:1px;"
                        class="label label-primary">  لون الخلفية </div>
                      <%- include('partials/color-template.ejs', {name: 'baccolor' })%>
                    </div>
                    <label class="checkbox-inline" style="position: relative !important; display: inline-block !important; padding-left: 20px; margin-bottom: 0;  vertical-align: middle; cursor: pointer; top: 13px;"><input class="rdel" type="checkbox" value="">تثبيت
                      الغرفه</label><br />
                                        <br />
                  <label class="checkbox-inline"><input class="broadcast" type="checkbox" value="" />تشغيل
                    المايك</label><br />
                    <label class="checkbox-inline"><input class="nohide" type="checkbox" value="" />منع المخفي</label><br />
                  <button class="rmake btn btn-primary fl"><span class="fa fa-plus">إنشاء الغرفه</span></button>
                  <button class="rsave btn btn-primary fl"><span class="fa fa-edit">حفظ التعديلات</span></button>
                  <button class="rdelete btn btn-danger fr"><span class="fa fa-times">حذف</span></button>
                  <div class="break border " id="ops" style="width: 100%; padding: 2px;"></div>
                </div>
              </div>
            </div>
          </div>
        </div>


        <x id="broadcasters" style="display: none;">

          <div style="margin-left: 3rem">
            <span class="fitimg prod" data="1" style="display: flex; justify-content: center; align-items: center">
              <div class="ismute"></div>
              <div class="evant" style="display: none; width: 100%; height: 100%; text-align: center;">
                <span id="name"
                    style="position: relative;display: block;font-size: 12px !important;width: 100%;background: rgba(222, 222, 222, 0.5);margin-top: 26px;border-radius: 30% 1% !important;"></span>
                <div id="showpf"
                  style="position: absolute; display: none; background: gray; margin-top: 6px; z-index: 1; padding: 5px; margin-left: -23px; border-radius: 10px;">
                  <i class="stopmic btn btn-danger" style="width: 80px; font-size: 9px !important; display: none;">سحب
                    المايك</i>
                  <i class="sounds btn btn-info" style="width: 80px; font-size: 9px !important;">إيقاف الصوت</i><br />
                  <i class="lockmic btn btn-warning" style="width: 80px; font-size: 9px !important; display: none;">قفل المايك</i><br />
                  <i class="profiles btn btn-primary" style="width: 80px; font-size: 9px !important;">فتح البروفايل</i>
                </div>
              </div>
            </span>
            <span class="fitimg prod" data="2" style="display: flex; justify-content: center; align-items: center">
              <div class="ismute"></div>
              <div class="evant" style="display: none; width: 100%; height: 100%; text-align: center;">
                <span id="name"
                    style="position: relative;display: block;font-size: 12px !important;width: 100%;background: rgba(222, 222, 222, 0.5);margin-top: 26px;border-radius: 30% 1% !important;"></span>
                <div id="showpf"
                  style="position: absolute; display: none; background: gray; margin-top: 45px; z-index: 1; padding: 5px; margin-left: -23px; border-radius: 10px;">
                  <i class="stopmic btn btn-danger" style="width: 80px; font-size: 9px !important; display: none;">سحب
                    المايك</i>
                  <i class="sounds btn btn-info" style="width: 80px; font-size: 9px !important;">إيقاف الصوت</i><br />
                  <i class="lockmic btn btn-warning" style="width: 80px; font-size: 9px !important; display: none;">قفل المايك</i><br />
                  <i class="profiles btn btn-primary" style="width: 80px; font-size: 9px !important;">فتح البروفايل</i>
                </div>
              </div>
            </span>
            <span class="fitimg prod" data="3" style="display: flex; justify-content: center; align-items: center">
              <div class="ismute"></div>
              <div class="evant" style="display: none; width: 100%; height: 100%; text-align: center;">
                <span id="name"
    style="position: relative;display: block;font-size: 12px !important;width: 100%;background: rgba(222, 222, 222, 0.5);margin-top: 26px;border-radius: 30% 1% !important;"></span>
                <div id="showpf"
                  style="position: absolute; display: none; background: gray; margin-top: 6px; z-index: 1; padding: 5px; margin-left: -23px; border-radius: 10px;">
                  <i class="stopmic btn btn-danger" style="width: 80px; font-size: 9px !important; display: none;">سحب
                    المايك</i>
                  <i class="sounds btn btn-info" style="width: 80px; font-size: 9px !important;">إيقاف الصوت</i><br />
                  <i class="lockmic btn btn-warning" style="width: 80px; font-size: 9px !important; display: none;">قفل المايك</i><br />
                  <i class="profiles btn btn-primary" style="width: 80px; font-size: 9px !important;">فتح البروفايل</i>
                </div>
              </div>
 
            </span>
            <span class="fitimg prod" data="4" style="display: flex; justify-content: center; align-items: center">
              <div class="ismute"></div>
              <div class="evant" style="display: none; width: 100%; height: 100%; text-align: center;">
                <span id="name"
                    style="position: relative;display: block;font-size: 12px !important;width: 100%;background: rgba(222, 222, 222, 0.5);margin-top: 26px;border-radius: 30% 1% !important;"></span>
                <div id="showpf"
                  style="position: absolute; display: none; background: gray; margin-top: 6px; z-index: 1; padding: 5px; margin-left: -23px; border-radius: 10px;">
                  <i class="stopmic btn btn-danger" style="width: 80px; font-size: 9px !important; display: none;">سحب
                    المايك</i>
                  <i class="sounds btn btn-info" style="width: 80px; font-size: 9px !important;">إيقاف الصوت</i><br />
                  <i class="lockmic btn btn-warning" style="width: 80px; font-size: 9px !important; display: none;">قفل المايك</i><br />
                  <i class="profiles btn btn-primary" style="width: 80px; font-size: 9px !important;">فتح البروفايل</i>
                </div>
              </div>
 
            </span>
            <span class="fitimg prod" data="5" style="display: flex; justify-content: center; align-items: center">
              <div class="ismute"></div>
              <div class="evant" style="display: none; width: 100%; height: 100%; text-align: center;">
                <span id="name"
                    style="position: relative;display: block;font-size: 12px !important;width: 100%;background: rgba(222, 222, 222, 0.5);margin-top: 26px;border-radius: 30% 1% !important;"></span>
                <div id="showpf"
                  style="position: absolute; display: none; background: gray; margin-top: 6px; z-index: 1; padding: 5px; margin-left: -23px; border-radius: 10px;">
                  <i class="stopmic btn btn-danger" style="width: 80px; font-size: 9px !important; display: none;">سحب
                    المايك</i>
                  <i class="sounds btn btn-info" style="width: 80px; font-size: 9px !important;">إيقاف الصوت</i><br />
                  <i class="lockmic btn btn-warning" style="width: 80px; font-size: 9px !important; display: none;">قفل المايك</i><br />
                  <i class="profiles btn btn-primary" style="width: 80px; font-size: 9px !important;">فتح البروفايل</i>
                </div>
              </div>
            </span>
            <span class="fitimg prod" data="6" style="display: flex; justify-content: center; align-items: center">
              <div class="ismute"></div>
              <div class="evant" style="display: none; width: 100%; height: 100%; text-align: center;">
                <span id="name"
                    style="position: relative;display: block;font-size: 12px !important;width: 100%;background: rgba(222, 222, 222, 0.5);margin-top: 26px;border-radius: 30% 1% !important;"></span>
                <div id="showpf"
                  style="position: absolute; display: none; background: gray; margin-top: 6px; z-index: 1; padding: 5px; margin-left: -23px; border-radius: 10px;">
                  <i class="stopmic btn btn-danger" style="width: 80px; font-size: 9px !important; display: none;">سحب
                    المايك</i>
                  <i class="sounds btn btn-info" style="width: 80px; font-size: 9px !important;">إيقاف الصوت</i><br />
                  <i class="lockmic btn btn-warning" style="width: 80px; font-size: 9px !important; display: none;">قفل المايك</i><br />
                  <i class="profiles btn btn-primary" style="width: 80px; font-size: 9px !important;">فتح البروفايل</i>
                </div>
              </div>
            </span>
 <div id="muteall" onclick="mutedall();" style="width: 50px;height: 50px;color: black;background-color: mediumseagreen;margin: -30px;position: absolute;left: 31px;top: 32px;border-radius: 10%;" class="corner fl border ">
<span style="margin-left: 10%;font-size: 38px!important;margin-top:-2px" class="fa fa-volume-up"></span>
</div> 
          </div>
        </x>

        <div id="StoryPanel"
          style="position:fixed;display:none;width:100%;height:100%;background:#000;z-index:99999999;left:0;top:0;padding:10px">
          <div id="StoryProgress">
            <div id="BarStory" class='brsy'></div>
          </div>
          <img class='st_pic fl' style="border-radius:50%;width:50px;height:50px;border:2px solid #fff">

          <div style="float: left; padding: 10px; color: #fff; font-size: 15px !important; font-family: cursive; top: 25px; left: 55px;"> <span class='st_topic'></span><br> <span class='st_time' style="font-size: 11px !important; color: darkgray; top: -6px; font-family: cursive; position: relative;"></span> </div>
          <div class='fr' style="right:10px">
            <i class='fa fa-trash supstory'
              style="display:none;color:#fff;margin-right:20px;font-size:20px !important"></i>
            <i class='storymuted fa fa-volume-up'
              style="display:none;color:#fff;margin-right:20px;font-size:20px !important"></i>
            <i class='storyplay fa fa-play'
              style="display:none;color:#fff;margin-right:20px;font-size:20px !important"></i>
            <i class='fa fa-close' onclick="HideStory()"
              style="color:#fff;font-size:20px !important;margin-right:10px"></i>
          </div>
          <div style="z-index: 11; font-size: 50px !important; color: #fff; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);" class='loadstory fa fa-spinner fa-pulse'></div>
          <video class='st_v' id="st_v_p" style="position: absolute; width: 100%; height: 100%; top: 0px; display:none; left: 0px; z-index: -1;" autoplay> <source> </video> <div class='st_p' style="position: absolute; width: 100%; height: 100%; top: 0px; display:none; left: 0px; z-index: -1; background-image: url(); background-repeat: no-repeat; background-position: center; background-size: contain;">
            <img id='st_p_t' src='' style="display:none">
          </div>
          <div class='st_view' style="position:absolute;bottom:10px;right:10px;color:#fff;font-size:13px !important">
          </div>
        </div>
      </body>
      <script src="jq.js?aegfdccxcؤa"></script>
      <script src="js/fingerprint-lib.js"></script>
      <script src="js/mic_lock.js"></script>
      <script src="js/fingerprint.js"></script>
      <script src="x2.js?atefgrrs"></script>
      <script src="color.js?dfhc"></script>
      <% if(script){ %>
        <script type="text/javascript">
              <%- script %>
        </script>
        <% } %>
      <script>
        // تهيئة بصمة الجهاز عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
          // إرسال بصمة الجهاز إلى الخادم عند تحميل الصفحة
          setTimeout(function() {
            if (window.DeviceFingerprint) {
              window.DeviceFingerprint.send();
            }
          }, 1000);
        });
      </script>
</html>