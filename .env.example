# Server Configuration
NODE_ENV=development
PORT=9631
HOST=localhost

# Database Configuration
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=gochat_db
DB_PORT=3306

# Security
JWT_SECRET=your_super_secret_jwt_key_here
BCRYPT_ROUNDS=12

# SSL Configuration (for production)
SSL_KEY_PATH=./pem/key.pem
SSL_CERT_PATH=./pem/cert.pem

# File Upload
MAX_FILE_SIZE=20971520
UPLOAD_PATH=./uploads

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# WebSocket Configuration
WS_PING_TIMEOUT=60000
WS_PING_INTERVAL=25000

# Allowed Domains (comma separated)
ALLOWED_DOMAINS=localhost,127.0.0.1,your-domain.com

# Admin Configuration
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_admin_password

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# External APIs
YOUTUBE_API_KEY=your_youtube_api_key
