var socket = null,clickop = null, isbust, limitpage = 50,atar = [],sico = [],dro3 = [],back = [], pws = [] , C_L_R = [] , MY_T = null, jstp = [];
function allload(){
var trans =
"WebSocket" in window || "MozWebSocket" in window
  ? ["websocket"]
  : ["polling", "websocket"];
socket = io("", {
reconnection: false,
transports: trans,
closeOnBeforeunload: false,
});
MY_T = location.href.replace("https://"+location.host,"").replace("/cp?","");
socket.emit("token",location.href.replace("https://"+location.host,"").replace("/cp?",""));
socket.emit("psw","t");
socket.on("seoo",function (d){
  
    if(d.cmd=="dro3"){
        dro3 = d.data;
    }else if(d.cmd == "atar"){
        atar = d.data;
    }else if(d.cmd == "room"){
      C_L_R = d.data;
  }else if(d.cmd == "power"){
  jstp = d.data;
  PowerRef(d.data);
}else if (d.cmd == "not"){
  seotnbeh = `<div onclick="$(this).remove();" style="min-width: 180px; max-width: 260px; border: 1px solid black; z-index: 2110; background-color: #efefef; position: absolute; top: 30%; margin-left: 30px; padding: 5px;z-index: 5001" class="hand ">
  <center>
  <div id="isfil" class=" border label label-primary" style="margin-top: -10px; padding-top: 10px; padding-left: 15px; width: 50%; padding-right: 15px;">تنبيه</div>
  </center>
  <div style="width:100%;display:block;padding:0px 5px;" class="break fl">`+d.data.msg+`</div></div>`;
  $("body").append(seotnbeh)
}else if(d.cmd == "sico"){
        sico = d.data;
    }else if(d.cmd == "back"){
        back = d.data;
    }else{
        
    }
});
socket.on("disconnect", function (e) {
window.close()
});

socket.on("SHWO_PANEL_ADMIN", function (data) {
    switch (data.cmd) {
      case "SEND_ADMIN_HOSTCHAT":
        $(".usernmb").text(data.data + " : الاعضاء");
        $(".OtherLogs").show();
        break;
      case "SEND_ADMIN_SITE":
        console.log(data.data.urls)
        $("#hostname").empty();
       $.each(data.data.test,function(i,e){
        $("#hostname").append(`<option onclick="seodomin('`+e+`')" value="`+e+`">`+e+`</option>`);
       });
$("#hostname").val(data.data.urls);

$( "#hostname" ).on('change',function() {
socket.emit("SHWO_PANEL_ADMIN", { cmd: "SEND_ADMIN_SITE", limit: limitpage,url:$(this).val() });
});
        $(".SiteLogs").show();
        $("#sett_name").val(data.data.istite);
        $("#sett_title").val(data.data.title);
        $("#sett_description").val(data.data.description);
        $("#sett_keywords").val(data.data.keywords);
        $("#sett_scr").val(data.data.script);
        var picker = new jscolor.color($(".sbgt")[0], {});
        picker.fromString(data.data.colors.bgcolor);
        var picker1 = new jscolor.color($(".sbackground")[0], {});
        picker1.fromString(data.data.colors.hicolor);
        var picker2 = new jscolor.color($(".sbuttons")[0], {});
        picker2.fromString(data.data.colors.btcolor);
        break;
      case "SEND_ADMIN_EMOJI":
        $(".EmojiLogs").show();
        $(".p-emo").children().remove();
        $.each(data.data, function (i, e) {
          var ht = $(
            '<div style="display:inline-block;padding:2px;margin:2px;margin-top:2px;" class="border"><img style="max-width:24px;max-height:24px;"><input type="number" name=' +
              e.path +
              ' onchange="emoChange(this)" style="width:50px;margin:2px" value=' +
              e.type +
              ">ف" +
              '<a style="margin-left: 4px;padding:4px;" onclick="del_ico(this);" class="btn btn-danger fa fa-times">.</a>' +
              "</div>"
          );
          ht.find("img").attr("src", "/emo/" + e.path);
          ht.find("a").attr("pid", "/emo/" + e.path);
          $(".p-emo").append(ht);
        });
        break;
      case "SEND_ADMIN_REMOVE_ICO":
        $(btncl).parent().remove();
        btncl = "";
        break;
      case "SEND_ADMIN_POWER_EDIT":
        socket.emit("SHWO_PANEL_ADMIN", { cmd: "SEND_ADMIN_POWERS", limit: 1 });
        setTimeout(() => {
          $(".powerbox").val(data.data);
          powerchange();
        }, 500);
        break;
      case "SEND_ADMIN_SETTINGS":
        $(".p-sico").children().remove();
        $(".p-dro3").children().remove();
        $(".p-atar").children().remove();
        $(".p-back").children().remove();
        $.each(sico, function (i, e) {
          var ht = $(
            '<div style="display:inline-block;padding:2px;margin:2px;margin-top:2px;" class="border"><img style="max-width:24px;max-height:24px;"><a style="margin-left: 4px;padding:4px;" onclick="del_ico(this);" class="btn btn-danger fa fa-times">.</a></div>'
          );
          ht.find("img").attr("src", "/sico/" + e.path);
          ht.find("a").attr("pid", "/sico/" + e.path);
          $(".p-sico").append(ht);
        });
        $.each(atar, function (i, e) {
          var ht = $(
            '<div style="display:inline-block;padding:2px;margin:2px;margin-top:2px;" class="border"><img style="max-width:24px;max-height:24px;"><a style="margin-left: 4px;padding:4px;" onclick="del_ico(this);" class="btn btn-danger fa fa-times">.</a></div>'
          );
          ht.find("img").attr("src", "/atar/" + e.path);
          ht.find("a").attr("pid", "/atar/" + e.path);
          $(".p-atar").append(ht);
        });
        $.each(back, function (i, e) {
          var ht = $(
            '<div style="display:inline-block;padding:2px;margin:2px;margin-top:2px;" class="border"><img style="max-width:24px;max-height:24px;"><a style="margin-left: 4px;padding:4px;" onclick="del_ico(this);" class="btn btn-danger fa fa-times">.</a></div>'
          );
          ht.find("img").attr("src", "/back/" + e.path);
          ht.find("a").attr("pid", "/back/" + e.path);
          $(".p-back").append(ht);
        });
        $.each(dro3, function (i, e) {
          var ht = $(
            '<div style="display:inline-block;padding:2px;margin:2px;margin-top:2px;" class="border"><img style="max-width:24px;max-height:24px;"><a style="margin-left: 4px;padding:4px;" onclick="del_ico(this);" class="btn btn-danger fa fa-times">.</a></div>'
          );
          ht.find("img").attr("src", "/dro3/" + e.path);
          ht.find("a").attr("pid", "/dro3/" + e.path);
          $(".p-dro3").append(ht);
        });
        $(".SettingsLogs").show();
        $(".bars").prop("checked", !data.data.bars ? false : true);
        $(".gust").prop("checked", !data.data.gust ? false : true);
        $(".isbanner").prop("checked", !data.data.isbanner ? false : true);
        $(".reconnect").prop("checked", !data.data.reconnect ? false : true);
        $(".offline").prop("checked", !data.data.offline ? false : true);
        $(".register").prop("checked", !data.data.register ? false : true);
        $(".replay").prop("checked", !data.data.replay ? false : true);
        $(".replaybc").prop("checked", !data.data.replaybc ? false : true);
        $(".likebc").prop("checked", !data.data.likebc ? false : true);
        $(".vpn").prop("checked", !data.data.vpn ? false : true);
        $(".callmic").prop("checked", !data.data.callmic ? false : true);
        $(".callsot").prop("checked", !data.data.callsot ? false : true);
        $(".showtop").prop("checked", !data.data.showtop ? false : true);
        $(".showsto").prop("checked", !data.data.showsto ? false : true);
        $(".showyot").prop("checked", !data.data.showyot ? false : true);
        $(".lengthbc").val(data.data.lengthbc);
        $(".lengthpm").val(data.data.lengthpm);
        $(".lengthroom").val(data.data.lengthroom);
        $(".maxdaymsg").val(data.data.maxdaymsg);
        $(".maxlikealert").val(data.data.maxlikealert);
        $(".maxlikebc").val(data.data.maxlikebc);
        $(".maxlikecam").val(data.data.maxlikecam);
        $(".maxlikemic").val(data.data.maxlikemic);
        $(".maxlikestory").val(data.data.maxlikestory);
        $(".maxlikename").val(data.data.maxlikename);
        $(".maxlikepic").val(data.data.maxlikepic);
        $(".maxlikeyot").val(data.data.maxlikeyot);
        $(".maxlikecover").val(data.data.maxlikecover);
        $(".maxlikepm").val(data.data.maxlikepm);
        $(".maxlikeroom").val(data.data.maxlikeroom);
        $(".maxlikesendpicpm").val(data.data.maxlikesendpicpm);
        $(".maxlogin").val(data.data.maxlogin);
        $(".maxuploadfile").val(data.data.maxuploadfile);
        $(".maxrep").val(data.data.maxrep);
        $(".gustmin").val(data.data.gustmin);
        $(".registermin").val(data.data.registermin);
        $(".bctime").val(data.data.bctime);
        $(".chatfinish").text(
          new Date(data.data.datafinish).toDateString().replaceAll("/", "-")
        );
        $(".p-banner").attr("src", "/site/" + data.data.banner);
        break;
      case "SEND_ADMIN_REMOVE_BOTS":
        $("#gustsPage")
          .DataTable()
          .rows()
          .nodes()
          .each(function (a, b) {
            if ($(a).children().eq(9).text() == data.data) {
              $("#gustsPage").DataTable().rows(a).remove();
            }
          });
        $("#gustsPage").DataTable().rows().invalidate();
        $("#gustsPage").DataTable().draw();
        break;
      case "SEND_ADMIN_ADD_BOTS":
        $("#gustsPage").DataTable().row.add(data.data);
        $("#gustsPage").DataTable().rows().invalidate();
        $("#gustsPage").DataTable().draw();
        $("#nameb,#msgbot").val("");
        break;
      case "SEND_ADMIN_POWER_ADD":
        $(".powerbox").each(function (ii, e) {
          var h = $("<option></option>");
          h.attr("value", data.data.name);
          h.attr("class", data.data.name.replace(/\s/g, ""));
          h.text(
            "[" +
              (JSON.parse(data.data.powers).rank || 0) +
              "] " +
              data.data.name
          );
          $(e).append(h);
        });
        powersarr.push(data.data);
        $(".powerbox").val(data.data.name);
        powerchange();
        break;
      case "SEND_ADMIN_POWER_DEL":
        $(".powerbox option:selected").remove();
        const pw = powersarr.findIndex((x) => x.name == data.data);
        if (pw != -1) {
          powersarr.splice(pw, 1);
        }
        break;
      case "SEND_ADMIN_POWERS":
        $(".PowersLogs").show();
        $(".sico").children().remove();
        $.each(sico, function (i, e) {
          var ht = $(
            '<div style="display:inline-block;margin:8px" onclick="GetPowerSico(this,\'' +
              e.path +
              "')\"></div>"
          );
          ht.prepend(
            $(
              '<img style="padding:4px;width:auto;height:30px" src="/sico/' +
                e.path +
                '">'
            )
          );
          $(".sico").append(ht).append();
        });
        $(".powerbox").children().remove();
        powersarr = data.data;
        powersarr.sort(function (a, b) {
          return (
            (JSON.parse(b.powers).rank || 0) - (JSON.parse(a.powers).rank || 0)
          );
        });
        for (var i = 0; i < powersarr.length; i++) {
          defr = JSON.parse(powersarr[i].powers);
          $(".powerbox").each(function (ii, e) {
            if (defr.name != "Hide" && defr.name != "chatmaster") {
              var h = $("<option></option>");
              h.attr("value", defr.name);
              h.attr("class", defr.name.replace(/\s/g, ""));
              h.text("[" + (defr.rank || 0) + "] " + defr.name);
              $(e).append(h);
            }
          });
        }
        powerchange();
        break;
      case "SEND_ADMIN_ROOM_CHECK":
        $(".chekcromt").each(function () {
          $(this).removeAttr("disabled");
          $(this).removeClass("btn-primary");
          $(this).addClass("btn-info");
        });
        $(".chekcrom" + data.data).attr("disabled", "disabled");
        $(".chekcrom" + data.data).removeClass("btn-info");
        $(".chekcrom" + data.data).addClass("btn-primary");
        break;
      case "SEND_ADMIN_ROOM_DEL":
        
        $("#roomsPage")
          .DataTable()
          .rows()
          .nodes()
          .each(function (a, b) {
            if ($(a).children().eq(5).text() == data.data) {
              $("#roomsPage").DataTable().rows(a).remove();
            }
          });
        $("#roomsPage").DataTable().rows().invalidate();
        $("#roomsPage").DataTable().draw();
        break;
      case "SEND_ADMIN_ROOM_PASS":
        $(".passDelete" + data.data).attr("disabled", "disabled");
        break;
      case "SEND_ADMIN_ROOMS":
        $(".RoomsLogs").show();
        var d = new Date().getTime();
        tbl = $("#roomsPage").DataTable({
          data: data.data,
          columns: [
            {
              data: "pic",
              render: function (data, type, row) {
                return (
                  "<img style='width: 50px;height: 50px;border-radius: 30px;border: 1px solid darkgray' src=" +
                  data +
                  ">"
                );
              },
            },
            { data: "topic" },
            { data: "user" },
            {
              data: { needpass: "needpass", id: "id" },
              render: function (data, type, row) {
                return (
                  "<a " +
                  (data.needpass ? "" : "disabled") +
                  ' class="passDelete' +
                  data.id +
                  ' fr btn btn-danger fa fa-times" onclick="roomdelpass(\'' +
                  data.id +
                  "')\">حذف</a>"
                );
              },
            },
            {
              data: "id",
              render: function (datas, type, row) {
                return (
                  "<a " +
                  (data.room != datas ? "" : "disabled") +
                  ' class="fr btn btn-' +
                  (data.room != datas ? "info" : "primary") +
                  " chekcromt chekcrom" +
                  datas +
                  ' fa fa-check" onclick="roomsver(\'' +
                  datas +
                  '\')"><span style="display:none">' +
                  datas +
                  "</span></a>"
                );
              },
            },
            {
              data: "id",
              render: function (data, type, row) {
                return (
                  '<a class="fr btn btn-danger fa fa-times" onclick="delRoom(\'' +
                  data +
                  '\')"><span style="display:none">' +
                  data +
                  "</span></a>"
                );
              },
            },
          ],
          paging: false,
          order: [[5, "desc"]],
          info: false,
          colReorder: true,
        });
        break;
      case "SEND_ADMIN_ADD_FILTER":
        $("#filterPage").DataTable().row.add(data.data);
        $("#filterPage").DataTable().rows().invalidate();
        $("#filterPage").DataTable().draw();
        $(".fltrit").val("");
        break;
      case "SEND_ADMIN_DELETE_FILTER":
        $("#filterPage")
          .DataTable()
          .rows()
          .nodes()
          .each(function (a, b) {
            if ($(a).children().eq(2).text() == data.data) {
              $("#filterPage").DataTable().rows(a).remove();
            }
          });
        $("#filterPage").DataTable().rows().invalidate();
        $("#filterPage").DataTable().draw();
        break;
      case "SEND_ADMIN_FILTER":
        $(".FilterLogs").show();
        if (data.type.length > 0) {
          $("#fltred").html("");
          for (var i = data.type.length - 1; i != -1; i--) {
            $("#fltred").append(
              '<label class="fl label label-primary">الكلمه</label>' +
                data.type[i].v +
                '<br><div class="fl border" style="width:100%;">' +
                data.type[i].msg +
                "<br>user: " +
                data.type[i].topic.split("&").join("&amp;") +
                "<br>IP: " +
                data.type[i].ip +
                "</div><br>"
            );
          }
        }
        tbl = $("#filterPage").DataTable({
          data: data.data,
          columns: [
            { data: "type" },
            { data: "v" },
            {
              data: { id: "id", v: "v" },
              render: function (data, type, row) {
                return deleteFilter({ id: data.id, v: data.v });
              },
            },
          ],
          paging: false,
          order: [[2, "desc"]],
          info: false,
          colReorder: true,
        });
        break;
      case "SEND_ADMIN_DELETE_SUB":
        $("#subsPage")
          .DataTable()
          .rows()
          .nodes()
          .each(function (a, b) {
            if ($(a).children().eq(6).text() == data.data) {
              $("#subsPage").DataTable().rows(a).remove();
            }
          });
        $("#subsPage").DataTable().rows().invalidate();
        $("#subsPage").DataTable().draw();
        break;
      case "SEND_ADMIN_SUBS":
        $(".SubsLogs").show();
        var d = new Date().getTime().toFixed();
        tbl = $("#subsPage").DataTable({
          data: data.data,
          columns: [
            { data: "sub" },
            { data: "username" },
            { data: "topic" },
            {
              data: { timestart: "timestart", timefinish: "timefinish" },
              render: function (data, type, row) {
                return new Date(data.timefinish - d)
                  .getTime()
                  .time().replaceAll("0-","")
              },
            },
            {
              data: "timeis",
              render: function (data, type, row) {
                return data != 0 ? data : "مؤبد";
              },
            },
            {
              data: "timestart",
              render: function (data, type, row) {
                return data != 0 ? new Date(d - data).getTime().time() : "مؤبد";
              },
            },
            {
              data: "id",
              render: function (data, type, row) {
                return deleteSub(data);
              },
            },
          ],
          paging: false,
          order: [[0, "desc"]],
          info: false,
          colReorder: true,
        });
        break;
      case "SEND_ADMIN_ADD_SHORT":
        $("#shortPage").DataTable().row.add(data.data);
        $("#shortPage").DataTable().rows().invalidate();
        $("#shortPage").DataTable().draw();
        $(".shrtname").val("");
        $(".shrtvalue").val("");
        break;
      case "SEND_ADMIN_DELETE_SHORT":
        $("#shortPage")
          .DataTable()
          .rows()
          .nodes()
          .each(function (a, b) {
            if ($(a).children().eq(2).text() == data.data) {
              $("#shortPage").DataTable().rows(a).remove();
            }
          });
        $("#shortPage").DataTable().rows().invalidate();
        $("#shortPage").DataTable().draw();
        break;
      case "SEND_ADMIN_SHORT":
        $(".ShortLogs").show();
        tbl = $("#shortPage").DataTable({
          data: data.data,
          columns: [
            { data: "msg" },
            { data: "reponse" },
            {
              data: "id",
              render: function (data, type, row) {
                return delShort(data);
              },
            },
          ],
          paging: false,
          order: [[2, "desc"]],
          info: false,
          colReorder: true,
        });
        break;
      case "SEND_ADMIN_DELETE_MESSAGE":
        $("#messagePage")
          .DataTable()
          .rows()
          .nodes()
          .each(function (a, b) {
            if ($(a).children().eq(3).text() == data.data) {
              $("#messagePage").DataTable().rows(a).remove();
            }
          });
        $("#messagePage").DataTable().rows().invalidate();
        $("#messagePage").DataTable().draw();
        break;
      case "SEND_ADMIN_ADD_MESSAGE":
        $("#messagePage").DataTable().row.add(data.data);
        $("#messagePage").DataTable().rows().invalidate();
        $("#messagePage").DataTable().draw();
        break;
      case "SEND_ADMIN_MESSAGES":
        $(".MessageLogs").show();
        tbl = $("#messagePage").DataTable({
          data: data.data,
          columns: [
            {
              data: "category",
              render: function (data, type, row) {
                return data == "w" ? "ترحيب" : "يومية";
              },
            },
            { data: "adresse" },
            { data: "msg" },
            {
              data: "id",
              render: function (data, type, row) {
                return delMessage(data);
              },
            },
          ],
          paging: false,
          order: [[3, "desc"]],
          info: false,
          colReorder: true,
        });
        break;
      case "SEND_ADMIN_BANS":
        $(".BansLogs").show();
        var d = new Date().getTime();
        tbl = $("#bansPage").DataTable({
          data: data.data,
          columns: [
            { data: "name_band" },
            { data: "type" },
            { data: "reponse" },
            { data: "device" },
            { data: "ip" },
            { data: "username" },
            {
              data: "country",
              render: function (data, type, row) {
                return isflag(data);
              },
            },
            {
              data: "date",
              render: function (data, type, row) {
                return data
                  ? new Date(d - new Date(data).getTime()).getTime().time()
                  : "مؤبد";
              },
            },
            {
              data: "created",
              render: function (data, type, row) {
                return new Date(d - new Date(data).getTime()).getTime().time();
              },
            },
            {
              data: "id",
              render: function (data, type, row) {
                return removeBand(data);
              },
            },
          ],
          paging: false,
          order: [[8, "desc"]],
          info: false,
          colReorder: true,
        });
        break;
      case "SEND_ADMIN_BANS_SYSTEM":
        var browsers = {};
        var systems = {};
        var system = $("#system").is(":checked");
        $("#system7").click(function () {
          if ($(this).is(":checked"))
            $("#system input.fa").prop("checked", false);
        });
        $("#system input.fa").click(function () {
          var ech = false;
          $("#system input.fa").each(function (e, x) {
            if ($(x).is(":checked")) ech = true;
          });
          $("#system7").prop("checked", ech ? false : true);
        });
        function getSystems() {
          $("#system input").each(function (e, x) {
            var idThis = $(this).attr("id");
            systems[idThis] = $(this).is(":checked");
          });
          return systems;
        }
        $("#btnSystem").click(function () {
          socket.emit("SHWO_PANEL_ADMIN", {
            cmd: "SEND_ADMIN_SYSTEM_BAND",
            system: getSystems(),
            limit: 1,
          });
        });
        const brs = JSON.parse(data.data[0].browsers);
        const sys = JSON.parse(data.data[0].systems);
        for (i in brs) $("#" + i).prop("checked", brs[i] ? true : false);
        for (i in sys) $("#" + i).prop("checked", sys[i] ? true : false);
        $("#browser9").click(function () {
          if ($(this).is(":checked"))
            $("#browser input.fa").prop("checked", false);
        });
        $("#browser input.fa").click(function () {
          var ech = false;
          $("#browser input.fa").each(function (e, x) {
            if ($(x).is(":checked")) ech = true;
          });
          $("#browser9").prop("checked", ech ? false : true);
        });
        function getBrowsers() {
          $("#browser input").each(function (e, x) {
            var idThis = $(this).attr("id");
            browsers[idThis] = $(this).is(":checked");
          });
          return browsers;
        }
        $("#btnbrowser").click(function () {
          socket.emit("SHWO_PANEL_ADMIN", {
            cmd: "SEND_ADMIN_BROWSER_BAND",
            browser: getBrowsers(),
            limit: 1,
          });
        });
        break;
      case "SEND_ADMIN_INFO_ACCOUNT":
        edituser = data.data;
        $("#edituser").modal("show");
        $(".tltp").text(data.data.user);
        var ht = $("#edituser");
        pws = pws.sort(function (a, b) {
          return b.rank - a.rank;
        });
        var pb = ht.find(".userpowera");
        pb.empty();
        pb.append("<option></option>");
        for (var i = 0; i < pws.length; i++) {
          var hh = $("<option></option>");
          if (
            pws[i].rank > jstp.rank &&
            pws[i] != "Hide" &&
            pws[i] != "chatmaster"
          ) {
            hh = $("<option style='display:none'></option>");
          }
          hh.attr("value", pws[i].name);
          if (pws[i].name == data.data.power) {
            hh.css("color", "blue");
            hh.attr("selected", "true");
          }
          hh.text("[" + pws[i].rank + "] " + pws[i].name);
          pb.append(hh);
        }
        ht.find(".userdaysa").val(0);
        ht.find(".upowera")
          .off()
          .click(function () {
            var days = parseInt(ht.find(".userdays").val()) || 0;

            socket.emit("SHWO_PANEL_ADMIN", {
              cmd: "SEND_ADMIN_ADD_POWER",
              id: data.data.idreg,
              power: pb.val(),
              days: days,
              limit: 1
            });
          });
        if (data.data.verification) {
          $(".verification").prop("checked", true);
          $("div.s01").css("color", "green");
        } else {
          $(".verification").prop("checked", false);
        }
        if (data.data.ifedit) {
          $(".ifedit").prop("checked", true);
          $("div.s03").css("color", "green");
        } else {
          $(".ifedit").prop("checked", false);
        }
        if (data.data.loginG) {
          $(".loginG").prop("checked", true);
          $("div.s02").css("color", "green");
        } else {
          $(".loginG").prop("checked", false);
        }
        break;
      case "SEND_ADMIN_BANS_ADD":
        $("#bansPage").DataTable().row.add(data.data);
        $("#bansPage").DataTable().rows().invalidate();
        $("#bansPage").DataTable().draw();
        $(".baninput").val("");
        break;
      case "SEND_ADMIN_DELETE_BAND":
        $("#bansPage")
          .DataTable()
          .rows()
          .nodes()
          .each(function (a, b) {
            if ($(a).children().eq(9).text() == data.data) {
              $("#bansPage").DataTable().rows(a).remove();
            }
          });
        $("#bansPage").DataTable().rows().invalidate();
        $("#bansPage").DataTable().draw();
        break;
      case "SEND_ADMIN_DELETE_ACCOUNT":
        $("#usersPage")
          .DataTable()
          .rows()
          .nodes()
          .each(function (a, b) {
            if ($(a).children().eq(0).text() == data.data) {
              $("#usersPage").DataTable().rows(a).remove();
            }
          });
        $("#usersPage").DataTable().rows().invalidate();
        $("#usersPage").DataTable().draw();
        $("#edituser").modal("hide");
        break;
        case "SEND_ADMIN_GUST_length":
          $("#maxbots").text(data.data.all + "/" + data.data.max + " الرصيد");
          break;
      case "SEND_ADMIN_GUST":
        $("#counrybot").empty();
        $("#rankbot").empty();
        $("#rommbot").empty();
        $.each(uf, function (key, value) {
          $("#countrybot").append(
            $("<option></option>").attr("value", key).text(value)
          );
        });
        $("#rankbot").append($("<option></option>").attr("value", "").text(""));
        $.each(pws, function (i, e) {
          $("#rankbot").append(
            $("<option></option>").attr("value", e.name).text(e.name)
          );
        });
        $("#rommbot").append($("<option></option>").attr("value", "").text(""));
        $.each(C_L_R, function (i, e) {
          $("#rommbot").append(
            $("<option></option>").attr("value", e.id).text(e.topic)
          );
        });
        $(".GustsLogs").show();
        tbl = $("#gustsPage").DataTable({
          data: data.data,
          columns: [
            {
              data: "pic",
              render: function (data, type, row) {
                return (
                  "<img style='width:50px' src=" + data + ">"
                );
              },
            },
            { data: "topic" },
            { data: "power" },
            { data: "country" },
            {
              data: "room",
              render: function (data, type, row) {
                return getroomuse(data);
              },
            },
            { data: "ip" },
            { data: "msg" },
            {
              data: { id: "id", room: "room" },
              render: function (data, type, row) {
                return data.room
                  ? "<a class='btn btn-primary fa fa-comments' onclick='msgbots(\"" +
                      data.id +
                      "," +
                      getroomuse(data.room) +
                      "\");'></a>"
                  : "";
              },
            },
            {
              data: "id",
              render: function (data, type, row) {
                return (
                  "<a class='btn btn-info' style='margin-right:10px;font-size:11px !important' onclick='enterbot(\"" +
                  data +
                  "\");'>دخول/خروج</a>"
                );
              },
            },
            {
              data: "id",
              render: function (data, type, row) {
                return (
                  "<a class='btn btn-danger fa fa-trash' onclick='kickbot(\"" +
                  data +
                  "\");'><span style='display:none'>" +
                  data +
                  "</span></a>"
                );
              },
            },
          ],
          paging: false,
          info: false,
          colReorder: true,
        });
        break;
      case "SEND_ADMIN_STATS":
        $(".Panelstate").show();
        var d = new Date().getTime();
        tbl = $("#statePage").DataTable({
          data: data.data,
          columns: [
            { data: "state" },
            { data: "topic" },
            { data: "username" },
            { data: "room" },
            { data: "ip" },
            {
              data: "time",
              render: function (data, type, row) {
                return new Date(d - data).getTime().time();
              },
            },
          ],
          paging: false,
          order: [[5, "asc"]],
          info: false,
          colReorder: true,
        });
        break;
        case "SEND_ADMIN_report":
          $(".reportstate").show();
          
          var tbl = $("#reportpage").DataTable({
          
            data: data.data,
            columns: [
              { data: "v" },
              { data: "topic" },
              { data: "topic2" },
              { data: "msg" },
            
            ],
            paging: false,
            info: false,
            order: [[2, "asc"]],
            colReorder: true,
          });
          break;
      case "SEND_ADMIN_USERS":
        $("#usersPage").DataTable().destroy();
        $(".UsersLogs").show();
        var d = new Date().getTime();
        tbl = $("#usersPage").DataTable({
          data: data.data,
          columns: [
            { data: "username" },
            { data: "topic" },
            { data: "ip" },
            { data: "device" },
            { data: "power" },
            {
              data: "lastssen",
              render: function (data, type, row) {
                return data ? new Date(d - data).getTime().time() : "متصل الأن";
              },
            },
            {
              data: "joinuser",
              render: function (data, type, row) {
                return new Date(d - new Date(data).getTime()).getTime().time();
              },
            },
            {
              data: "idreg",
              render: function (data, type, row) {
                return getUserInfo(data);
              },
            },
          ],
          paging: false,
          info: false,
          colReorder: true,
          searching: false,
          order: [[6, "asc"]],
        });
        break;
      case "SEND_ADMIN_LOGS":
        
        $(".Panellogs").show();
        var d = new Date().getTime();
    
        var tbl = $("#logsPage").DataTable({
          
          data: data.data,
          columns: [
            { data: "state" },
            { data: "username" },
            { data: "topic" },
            { data: "ip" },
            {
              data: "country",
              render: function (data, type, row) {
                return isflag(data);
              },
            },
            { data: "device" },
            {
              data: "date",
              render: function (data, type, row) {
                return new Date(new Date().getTime() - data).getTime().time();
                
              },
            },
            { data: "isin" },
            {
              data: { isin: "isin", username: "username", ip: "ip" },
              render: function (data, type, row) {
                return data.isin == "band"
                  ? btncheck({ username: data.username, ip: data.ip })
                  : "";
              },
            },
          ],
          paging: false,
          info: false,
          order: [[6, "asc"]],
          colReorder: true,
        });
        break;
    }
  });
  $(".side-menu > li > a").on("click", function () {
    CloseAllFun();
    if ($(this).text() == "السجل") {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_LOGS",
        limit: limitpage,
      });
    } else if ($(this).text() == "الصلاحيات") {
      socket.emit("SHWO_PANEL_ADMIN", { cmd: "SEND_ADMIN_POWERS", limit: 1 });
    } else if ($(this).text() == "الحالات") {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_STATS",
        limit: limitpage,
      });
    } else if ($(this).text() == "Bots") {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_GUST",
        limit: limitpage,
      });
    } else if ($(this).text() == "الأعضاء") {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_USERS",
        value: null,
        limit: limitpage,
      });
    } else if ($(this).text() == "الإشتراكات") {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_SUBS",
        limit: limitpage,
      });
    } else if ($(this).text() == "الإضافات") {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_HOSTCHAT",
        limit: limitpage,
      });
    }else if ($(this).text() == "التبليغات") {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_report",
        limit: limitpage,
      });
    } else if ($(this).text() == "الموقع") {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_SITE",
        limit: limitpage,
        url: location.host
      });
    } else if ($(this).text() == "الإعدادات") {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_SETTINGS",
        limit: limitpage,
      });
    } else if ($(this).text() == "الغرف") {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_ROOMS",
        limit: limitpage,
      });
    } else if ($(this).text() == "فلتر") {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_FILTER",
        limit: limitpage,
      });
    } else if ($(this).text() == "الإختصارات") {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_SHORT",
        limit: limitpage,
      });
    } else if ($(this).text() == "الرسائل") {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_MESSAGES",
        limit: limitpage,
      });
    } else if ($(this).text() == "الرموز") {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_EMOJI",
        limit: limitpage,
      });
    } else if ($(this).text() == "الحظر") {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_BANS",
        limit: limitpage,
      });
    }
    $(clickop).removeClass("active");
    $(this).addClass("active");
    clickop = this;
  });
  $(".bandnow").click(() => {
    const band = $(".baninput").val();
    if (band.trim()) {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_BANS_ADD",
        band: band,
        limit: limitpage,
      });
    }
  });
  $(".verlog").click(function () {
    socket.emit("SHWO_PANEL_ADMIN", {
      cmd: "SEND_ADMIN_EDIT_ACCOUNT",
      user: Number(edituser.idreg),
      limit: 1,
      loginG: $(".loginG").is(":checked"),
      verification: $(".verification").is(":checked"),
      ifedit: $(".ifedit").is(":checked"),
    });
    $("#edituser").modal("hide");
  });
  $(".delus").click(function () {
    socket.emit("SHWO_PANEL_ADMIN", {
      cmd: "SEND_ADMIN_DELETE_ACCOUNT",
      user: Number(edituser.idreg),
      limit: 1,
    });
  });
  $(".usersetpwd").click(function () {
    const pass = $(".userpwd").val();
    if (pass.trim()) {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_PASS",
        user: Number(edituser.idreg),
        limit: 1,
        pass: pass,
      });
      $(".userpwd").val("");
      $("#edituser").modal("hide");
    }
  });
  $("#searchusers").on("change", function () {
    socket.emit("SHWO_PANEL_ADMIN", {
      cmd: "SEND_ADMIN_USERS",
      value: this.value,
      limit: 5,
    });
  });
}
function CloseAllFun() {
    $("#fltred").html("");
    $(
      ".Panelstate,.Panellogs,.UsersLogs,.BotsLogs,.BansLogs,.MessageLogs,.ShortLogs,.OtherLogs,.SiteLogs,.SubsLogs,.FilterLogs,.RoomsLogs,.PowersLogs,.GustsLogs,.SettingsLogs,.reportstate,.EmojiLogs"
    ).hide();
    $("#logsPage").dataTable().fnDestroy();
    $("#statePage").dataTable().fnDestroy();
     $("#reportpage").dataTable().fnDestroy();
    $("#usersPage").dataTable().fnDestroy();
    $("#gustsPage").dataTable().fnDestroy();
    $("#botsPage").dataTable().fnDestroy();
    $("#bansPage").dataTable().fnDestroy();
    $("#messagePage").dataTable().fnDestroy();
    $("#shortPage").dataTable().fnDestroy();
    $("#subsPage").dataTable().fnDestroy();
    $("#filterPage").dataTable().fnDestroy();
    $("#roomsPage").dataTable().fnDestroy();
    limitpage = 50;
  }
  function SEND_EVENT_EMIT(e, t) {
    onlines();
    clearTimeout(isbust);
    puys();
    socket.emit("SEND_EVENT_EMIT_SERVER", { cmd: e, data: t });
  }
  function nextPage(data) {
    if (data) {
      if (data == "log") {
        $("#logsPage").DataTable().destroy();
        socket.emit("SHWO_PANEL_ADMIN", {
          cmd: "SEND_ADMIN_LOGS",
          limit: (limitpage += maxlimit),
        });
      } else if (data == "state") {
        $("#statePage").DataTable().destroy();
        socket.emit("SHWO_PANEL_ADMIN", {
          cmd: "SEND_ADMIN_STATS",
          limit: (limitpage += maxlimit),
        });
      }else if (data == "report") {
        $("#reportpage").DataTable().destroy();
        socket.emit("SHWO_PANEL_ADMIN", {
          cmd: "SEND_ADMIN_report",
          limit: (limitpage += maxlimit),
        });
      } else if (data == "short") {
        $("#shortPage").DataTable().destroy();
        socket.emit("SHWO_PANEL_ADMIN", {
          cmd: "SEND_ADMIN_SHORT",
          limit: (limitpage += maxlimit),
        });
      } else if (data == "subs") {
        $("#subsPage").DataTable().destroy();
        socket.emit("SHWO_PANEL_ADMIN", {
          cmd: "SEND_ADMIN_SUBS",
          limit: (limitpage += maxlimit),
        });
      } else if (data == "rooms") {
        $("#roomsPage").DataTable().destroy();
        socket.emit("SHWO_PANEL_ADMIN", {
          cmd: "SEND_ADMIN_ROOMS",
          limit: (limitpage += maxlimit),
        });
      } else if (data == "filter") {
        $("#filterPage").DataTable().destroy();
        socket.emit("SHWO_PANEL_ADMIN", {
          cmd: "SEND_ADMIN_FILTER",
          limit: (limitpage += maxlimit),
        });
      } else if (data == "message") {
        $("#messagePage").DataTable().destroy();
        socket.emit("SHWO_PANEL_ADMIN", {
          cmd: "SEND_ADMIN_MESSAGES",
          limit: (limitpage += maxlimit),
        });
      } else if (data == "users") {
        $("#usersPage").DataTable().destroy();
        socket.emit("SHWO_PANEL_ADMIN", {
          cmd: "SEND_ADMIN_USERS",
          value: null,
          limit: (limitpage += maxlimit),
        });
      } else if (data == "gusts") {
        $("#gustsPage").DataTable().destroy();
        socket.emit("SHWO_PANEL_ADMIN", {
          cmd: "SEND_ADMIN_GUST",
          limit: (limitpage += maxlimit),
        });
      } else if (data == "bands") {
        $("#bansPage").DataTable().destroy();
        socket.emit("SHWO_PANEL_ADMIN", {
          cmd: "SEND_ADMIN_BANS",
          limit: (limitpage += maxlimit),
        });
      }
    }
  }
  function msgsit(type, t, m) {
    socket.emit("SHWO_PANEL_ADMIN", {
      cmd: "SEND_ADMIN_ADD_MESSAGE",
      type: type,
      msg: m,
      t: t,
      limit: 1,
    });
    $("#msgs>input,#msgs>textarea").val("");
  }


  function checkuser(data) {
    socket.emit("SHWO_PANEL_ADMIN", {
      cmd: "SEND_ADMIN_CHECK",
      user: data.split(",")[0],
      ip: data.split(",")[1],
      limit: 1,
    });
  }
  function delfltr(data) {
    socket.emit("SHWO_PANEL_ADMIN", {
      cmd: "SEND_ADMIN_DELETE_FILTER",
      id: Number(data.split(",")[0]),
      v: data.split(",")[1],
      limit: 1,
    });
  }
  function delband(data) {
    socket.emit("SHWO_PANEL_ADMIN", {
      cmd: "SEND_ADMIN_DELETE_BAND",
      id: Number(data),
      limit: 1,
    });
  }
  function delsub(data) {
    socket.emit("SHWO_PANEL_ADMIN", {
      cmd: "SEND_ADMIN_DELETE_SUB",
      id: Number(data),
      limit: 1,
    });
  }
  function delshot(data) {
    socket.emit("SHWO_PANEL_ADMIN", {
      cmd: "SEND_ADMIN_DELETE_SHORT",
      id: Number(data),
      limit: 1,
    });
  }
  function delmsgm(data) {
    socket.emit("SHWO_PANEL_ADMIN", {
      cmd: "SEND_ADMIN_DELETE_MESSAGE",
      id: Number(data),
      limit: 1,
    });
  }
  function useredit(data) {
    $(".userpwd").val("");
    socket.emit("SHWO_PANEL_ADMIN", {
      cmd: "SEND_ADMIN_INFO_ACCOUNT",
      user: Number(data),
      limit: 1,
    });
  }
  function fltrit(path, v) {
    if (path.trim() && v.trim()) {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_ADD_FILTER",
        path: path.trim(),
        v: v.trim(),
        limit: 1,
      });
    }
  }
  function shrtadd() {
    const msg = $(".shrtname").val();
    const reponse = $(".shrtvalue").val();
    if (reponse.trim() && msg.trim()) {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_ADD_SHORT",
        msg: msg,
        reponse: reponse,
        limit: 1,
      });
    }
  }
  function roomdelpass(data) {
    var dfs = $(".passDelete" + data);
    if (dfs.attr("disabled")) {
      return alert("الغرفة لا تحتوي على كلمة مرور");
    }
    socket.emit("SHWO_PANEL_ADMIN", {
      cmd: "SEND_ADMIN_ROOM_PASS",
      id: data,
      limit: 1,
    });
  }
  function roomsver(data) {
    var dfs = $(".chekcrom" + data);
    if (dfs.attr("disabled")) {
      return alert("الغرفة تم تحديدها بالفعل");
    }
    socket.emit("SHWO_PANEL_ADMIN", {
      cmd: "SEND_ADMIN_ROOM_CHECK",
      id: data,
      limit: 1,
    });
  }
  function delRoom(data) {
    socket.emit("SHWO_PANEL_ADMIN", {
      cmd: "SEND_ADMIN_ROOM_DEL",
      id: data,
      limit: 1,
    });
  }
  function puys() {
    isbust = setTimeout(function () {
      socket.emit("SEND_EVENT_EMIT_STATE", 1);
      puys();
    }, 18e4);
  }
  function onlines() {
    socket.emit("SEND_EVENT_EMIT_STATE", 0);
  }  
  function powers_save(p) {
    socket.emit("SHWO_PANEL_ADMIN", {
      cmd: "SEND_ADMIN_POWER_ADD",
      power: JSON.stringify(p),
      limit: 1,
    });
  }
  function powers_delete(p) {
    socket.emit("SHWO_PANEL_ADMIN", {
      cmd: "SEND_ADMIN_POWER_DEL",
      power: p.name,
      limit: 1,
    });
  }
  var powersarr = [];
  function powerchange(data) {
    var k = $(".powerbox option:selected").val();
    var power = null;
    for (var i = 0; i < powersarr.length; i++) {
      pws = JSON.parse(powersarr[i].powers);
      if (pws.name == k) {
        power = pws;
        break;
      }
    }
    if (power != null) {
      var names = [
        ["rank", "الترتيب"],
        ["name", "إسم المجموعه"],
        ["ico", "الإيقونه"],
        ["kick", "الطرد"],
        ["delbc", "حذف الحائط"],
        ["alert", "التنبيهات"],
        ["mynick", "تغير النك"],
        ["unick", "تغير النكات"],
        ["ban", "الباند"],
        ["publicmsg", "الإعلانات"],
        ["forcepm", "فتح الخاص"],
        ["loveu", "نقل من الغرفة"],
        ["roomowner", "إداره الغرف"],
        ["createroom", "انشاء الغرف"],
        ["rooms", "اقصى حد للغرف الثابته"],
        ["edituser", "إداره العضويات"],
        ["meiut", "إسكات العضو"],
        ["ulike", "تعديل لايكات العضو"],
        ["flter", "الفلتر"],
        ["subs", "الاشتراكات"],
        ["shrt", "الاختصارات"],
        ["msgs", "رسائل الدردشة"],
        ["bootedit", "فلتر المراقبة"],
        ["setpower", "تعديل الصلاحيات"],
        ["upgrades", "الهدايا"],
        ["history", "كشف النكات"],
        ["cp", "لوحه التحكم"],
        ["grupes", "الغرف الممتلئة و المغلقة"],
        ["delpic", "حذف صورة العضو"],
        ["delmsg", "حذف الرسائل العامة"],
        ["stealth", "مخفي"],
        ["ureport", "إعطاء بنر"],
        ["report", "التبليغات "],
        ["owner", "إداره الموقع"],
      ];
      var ht = $("<div class='json' style='width:260px;'></div>");
      ht.append(jsonhtml(power, names, powers_save));
      $("#powers .json").remove();
      $("#powers").append(ht);
      $("#powers .delp")
        .off()
        .click(function () {
          powers_delete(power);
        });
    }
  }
  function SaveSettings() {
    const lists = {
      lengthbc: Number($(".lengthbc").val()),
      lengthpm: Number($(".lengthpm").val()),
      lengthroom: Number($(".lengthroom").val()),
      maxdaymsg: Number($(".maxdaymsg").val()),
      maxlikealert: Number($(".maxlikealert").val()),
      maxlikebc: Number($(".maxlikebc").val()),
      maxlikecam: Number($(".maxlikecam").val()),
      maxlikemic: Number($(".maxlikemic").val()),
      maxlikestory: Number($(".maxlikestory").val()),
      maxlikename: Number($(".maxlikename").val()),
      maxlikepic: Number($(".maxlikepic").val()),
      maxlikeyot: Number($(".maxlikeyot").val()),
      maxlikecover: Number($(".maxlikecover").val()),
      maxek: Number($(".maxek").val()) || 3,
      maxlikepm: Number($(".maxlikepm").val()),
      maxlikeroom: Number($(".maxlikeroom").val()),
      maxlikesendpicpm: Number($(".maxlikesendpicpm").val()),
      maxlogin: Number($(".maxlogin").val()),
      maxuploadfile: Number($(".maxuploadfile").val()),
      maxrep: Number($(".maxrep").val()),
      gustmin: Number($(".gustmin").val()),
      registermin: Number($(".registermin").val()),
      bctime: Number($(".bctime").val()),
      callmic: $(".callmic").is(":checked"),
      callsot: $(".callsot").is(":checked"),
      showtop: $(".showtop").is(":checked"),
      showsto: $(".showsto").is(":checked"),
      showyot: $(".showyot").is(":checked"),
      bars: $(".bars").is(":checked"),
      gust: $(".gust").is(":checked"),
      isbanner: $(".isbanner").is(":checked"),
      reconnect: $(".reconnect").is(":checked"),
      register: $(".register").is(":checked"),
      offline: $(".offline").is(":checked"),
      replay: $(".replay").is(":checked"),
      replaybc: $(".replaybc").is(":checked"),
      likebc: $(".likebc").is(":checked"),
      vpn: $(".vpn").is(":checked"),
    };
    socket.emit("SHWO_PANEL_ADMIN", {
      cmd: "SEND_ADMIN_EDIT_SETTINGS",
      data: lists,
      limit: 1,
    });
  }
  function jsonhtml(j, names, onsave) {
    j = {
      rank: Number(j.rank),
      name: String(j.name),
      ico: String(j.ico),
      kick: Boolean(j.kick),
      publicmsg: Boolean(j.publicmsg),
      rooms: Boolean(j.rooms),
      upgrades: Boolean(j.upgrades),
      delbc: Boolean(j.delbc),
      alert: Boolean(j.alert),
      mynick: Boolean(j.mynick),
      unick: Boolean(j.unick),
      ban: Boolean(j.ban),
      forcepm: Boolean(j.forcepm),
      roomowner: Boolean(j.roomowner),
      createroom: Boolean(j.createroom),
      edituser: Boolean(j.edituser),
      setpower: Boolean(j.setpower),
      history: Boolean(j.history),
      cp: Boolean(j.cp),
      stealth: Boolean(j.stealth),
      groups: Boolean(j.groups),
      ureport: Boolean(j.ureport),
      report: Boolean(j.report),
      owner: Boolean(j.owner),
      msgs: Boolean(j.msgs),
      bootedit: Boolean(j.bootedit),
      shrt: Boolean(j.shrt),
      subs: Boolean(j.subs),
      flter: Boolean(j.flter),
      ulike: Boolean(j.ulike),
      grupes: Boolean(j.grupes),
      delmsg: Boolean(j.delmsg),
      delpic: Boolean(j.delpic),
      meiut: Boolean(j.meiut),
      loveu: Boolean(j.loveu),
    };
    var html = $(
      '<div style="width:100%;height:100%;padding:5px;" class="break"></div>'
    );
    var okeys = Object.keys(j);
    $.each(okeys, function (i, key) {
      var name = null;
      if (names != null) {
        $.each(names, function (i, e) {
          if (e[0] == key) {
            name = e[1];
          } else okeys.splice(okeys.indexOf(e[0]), 1);
          okeys.splice(i, 0, e[0]);
        });
      }
      if (name == null) {
        return;
      }
      switch (typeof j[key]) {
        case "string":
          html.append('<label class="label label-primary">' + name + "</label>");
          html.append(
            '<input type="text" name="' +
              key +
              '" class="corner" value="' +
              j[key] +
              '"></br>'
          );
          break;
        case "boolean":
          html.append('<label class="label label-primary">' + name + "</label>");
          var checked = "";
          if (j[key]) {
            checked = "checked";
          }
          html.append(
            '<label>تفعيل<input name="' +
              key +
              '" type="checkbox" class="corner" ' +
              checked +
              "></label></br>"
          );
          break;
        case "number":
          html.append('<label class="label label-primary">' + name + "</label>");
          html.append(
            '<input name="' +
              key +
              '" type="number" style="width:60px;" class="corner" value="' +
              j[key] +
              '"></br>'
          );
          break;
      }
    });
    html.append('<button class="btn btn-primary fr fa fa-edit">حفظ</button>');
    html.find("button").click(function () {
      onsave(htmljson(html));
    });
    return html;
  }
  function enterbot(data) {
    if (data) {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_ENTER_BOTS",
        id: data,
        limit: 1,
      });
    }
  }
  function kickbot(data) {
    if (data) {
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_REMOVE_BOTS",
        id: data,
        limit: 1,
      });
    }
  }
  function msgbots(data) {
    if (data) {
      const msg = prompt(
        "الرجاء كتابة رسالتك للغرفة (" + data.split(",")[1] + ")"
      );
      if (msg != null) {
        socket.emit("SHWO_PANEL_ADMIN", {
          cmd: "SEND_ADMIN_MSG_BOTS",
          id: data.split(",")[0],
          msg: msg,
          limit: 1,
        });
      }
    }
  }
  function emoChange(data) {
    var path = $(data).attr("name");
    var type = $(data).val();
    socket.emit("SHWO_PANEL_ADMIN", {
      cmd: "SEND_ADMIN_EMO_UP",
      path: path,
      type: Number(type),
      limit: 1,
    });
  }
  var btncl = "";
function StatsChat(data) {
  socket.emit("SHWO_PANEL_ADMIN", {
    cmd: "SEND_ADMIN_HOST_EDIT",
    data: data,
    limit: 1,
  });
}
function del_ico(btn) {
  btncl = btn;
  socket.emit("SHWO_PANEL_ADMIN", {
    cmd: "SEND_ADMIN_REMOVE_ICO",
    data: $(btn).attr("pid"),
    limit: 1,
  });
}
$(".target").change(function () {
  alert("Handler for .change() called.");
});
function SaveTNae() {
  var settdescription = $("#sett_description").val();
  var settscr = $("#sett_scr").val();
  var settkeywords = $("#sett_keywords").val();
  var name = $("#sett_name").val();
  var title = $("#sett_title").val();
  var bg = $(".sbgt").val().replace("#", "");
  var urls = $("#hostname").val();
  var buttons = $(".sbuttons").val().replace("#", "");
  var background = $(".sbackground").val().replace("#", "");
  var d = {
    url:urls,
    bg: bg,
    buttons: buttons,
    background: background,
    name: name,
    title: title,
    settdescription: settdescription,
    settscr: settscr,
    settkeywords: settkeywords,
  };
  socket.emit("SHWO_PANEL_ADMIN", {
    cmd: "SEND_ADMIN_SAVE_SITE",
    data: d,
    limit: 1,
  });
}
function addbot() {
    const nameb = $("#nameb").val() || "";
    const rankbot = $("#rankbot").val() || "";
    const countrybot = $("#countrybot").val() || "";
    const statsbots = $("#statsbots").val() || 0;
    const likebot = $("#likebot").val() || 0;
    const urlpic =
      $(".spicbot").attr("src") || "/site/" + location.host + "pic.png";
    const rommbot = $("#rommbot").val() || "";
    const botmsgc = $(".botmsgc").val() || "";
    const botnamec = $(".botnamec").val() || "";
    const botnamebc = $(".botnamebc").val() || "";
    const timestart = $("#timestart").val() || 2;
    const timestop = $("#timestop").val() || 2;
    const autostart = $("#rdel").is(":checked");
    const msgbot = $("#msgbot").val() || "";
    if (nameb.trim() && countrybot) {
      const isbot = {
        nameb: nameb,
        rankbot: rankbot,
        statsbots: Number(statsbots),
        likebot: Number(likebot),
        countrybot: countrybot,
        urlpic: urlpic,
        rommbot: rommbot,
        botmsgc: botmsgc,
        botnamec: botnamec,
        timestop:timestop,
        timestart:timestart,
        autostart:autostart,
        botnamebc: botnamebc,
        msgbot: msgbot,
      };
      socket.emit("SHWO_PANEL_ADMIN", {
        cmd: "SEND_ADMIN_ADD_BOTS",
        data: isbot,
        limit: 1,
      });
    }
  }
  function btncheck(data) {
    if (data) {
      return (
        "<a onclick=\"checkuser('" +
        data.username +
        "," +
        data.ip +
        '\')"" class="btn btn-success fa fa-check"></a>'
      );
    }
  }
  function getUserInfo(data) {
    if (data) {
      return (
        "<a onclick=\"useredit('" +
        data +
        '\')" class="btn btn-primary fa fa-gear"></a>'
      );
    }
  }
  function removeBand(data) {
    if (data) {
      return (
        "<a onclick=\"delband('" +
        data +
        '\')" class="btn btn-danger fa fa-close"><span style="display:none">' +
        data +
        "</span></a>"
      );
    }
  }
  function delMessage(data) {
    if (data) {
      return (
        "<a onclick=\"delmsgm('" +
        data +
        '\')" class="btn btn-danger fa fa-close"><span style="display:none">' +
        data +
        "</span></a>"
      );
    }
  }
  function deleteFilter(data) {
    if (data) {
      return (
        "<a onclick=\"delfltr('" +
        data.id +
        "," +
        data.v +
        '\')" class="btn btn-danger fa fa-close"><span style="display:none">' +
        data.id +
        "</span></a>"
      );
    }
  }
  function delShort(data) {
    if (data) {
      return (
        "<a onclick=\"delshot('" +
        data +
        '\')" class="btn btn-danger fa fa-close"><span style="display:none">' +
        data +
        "</span></a>"
      );
    }
  }
  function deleteSub(data) {
    if (data) {
      return (
        "<a onclick=\"delsub('" +
        data +
        '\')" class="btn btn-danger fa fa-close"><span style="display:none">' +
        data +
        "</span></a>"
      );
    }
  }
  
  var maxlimit = 50;
  var limitpage = 50;
  var tbl;
  
  function isflag(data) {
    if (data) {
      return (
        "<img src='/flag/" +
        data.toLowerCase().replace("il", "ps") +
        ".png' style='width: 25px;margin: 5px;border: 1px solid darkgray;'> " +
        data +
        " "
      );
    }
  }
  function htmljson(html) {
    html = $(html);
    var json = {};
    $.each(html.find("input"), function (i, e) {
      switch ($(e).attr("type")) {
        case "text":
          json[$(e).attr("name")] = $(e).val();
          break;
        case "checkbox":
          json[$(e).attr("name")] = $(e).prop("checked");
          break;
        case "number":
          json[$(e).attr("name")] = parseInt($(e).val(), 10);
          break;
      }
    });
    return json;
  }
  var evi;
  function GetPowerSico(ev, e) {
    if (evi) {
      $(evi).removeClass("activepower");
    }
    evi = ev;
    $(ev).addClass("activepower");
    $("input[name='ico']").val(e);
  }
  function sendfilea(id, folder) {
    pickedfile = null;
    var e = $("<div></div>").first();
    e.append("<input type='file'  accept='image/*' style='display:none;'/>");
    e.children("input").trigger("click");
    var xx;
    $(e)
      .children("input")
      .on("change", function () {
        var sp = $(
          "<div class='mm msg ' style='width:200px;'><a class='fn '></a><button style='color:red;border:1px solid red;min-width:40px;' class=' cancl'>X</button></div>"
        );
        sp.insertAfter($(id));
        $(sp)
          .find(".cancl")
          .click(function () {
            $(sp).remove();
            xx.abort();
          });
        var formData = new FormData();
        formData.append("photo", $(e).children("input").prop("files")[0]);
        xx = $.ajax({
          xhr: function () {
            var xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener(
              "progress",
              function (evt) {
                if (evt.lengthComputable) {
                  var percentComplete = evt.loaded / evt.total;
                  $(sp.find(".fn")).text(
                    "%" +
                      parseInt(percentComplete * 100) +
                      " | " +
                      $(e).children("input").val().split("\\").pop()
                  );
                }
              },
              false
            );
            return xhr;
          },
          timeout: 0,
          url: "/uploadURM?token=" + MY_T + "&state=" + folder,
          type: "POST",
          data: formData,
          cache: false,
          processData: false,
          contentType: false,
          success: function (data) {
            const d = JSON.parse(data);
            if (d.msg.includes("room")) {
              $(".p-room").attr(
                "src",
                "/site/" + location.host + d.msg.replace("room/", "")
              );
            } else if (d.msg.includes("pic")) {
              $(".p-user").attr(
                "src",
                "/site/" + location.host + d.msg.replace("pic/", "")
              );
            } else if (d.msg.includes("cover")) {
              $(".p-cover").attr(
                "src",
                "/site/" + location.host + d.msg.replace("cover/", "")
              );
            } else if (d.msg.includes("banner")) {
              $(".p-banner").attr(
                "src",
                "/site/" + location.host + d.msg.replace("banner/", "")
              );
            } else if (d.msg.includes("bacmic")) {
              $(".p-bacmic").attr(
                "src",
                "/site/" + location.host + d.msg.replace("bacmic/", "")
              );
            } else if (d.msg.includes("msgpic")) {
              $(".p-msgpic").attr(
                "src",
                "/site/" + d.msg.replace("msgpic/", "")
              );
            } else if (d.msg.includes("mic")) {
            $(".p-mic").attr(
              "src",
              "/site/" + location.host + d.msg.replace("mic/", "")
            );
          } else if (d.msg.includes("logo")) {
              $(".p-logo").attr(
                "src",
                "/site/" + location.host + d.msg.replace("logo/", "")
              );
            } else if (d.msg.includes("emo")) {
              var ht = $(
                '<div style="display:inline-block;padding:2px;margin:2px;margin-top:2px;" class="border"><img style="max-width:24px;max-height:24px;"><input type="number" name=' +
                  d.msg.split("@")[0] +
                  ' onchange="emoChange(this)" style="width:50px;margin:2px" value=' +
                  d.msg.split("@")[1] +
                  ">ف" +
                  '<a style="margin-left: 4px;padding:4px;" onclick="del_ico(this);" class="btn btn-danger fa fa-times">.</a>' +
                  "</div>"
              );
              ht.find("img").attr("src", "/" + d.msg.split("@")[0]);
              ht.find("a").attr("pid", "/" + d.msg.split("@")[0]);
              $(".p-emo").append(ht);
            } else if (d.msg.includes("dro3")) {
              var ht = $(
                '<div style="display:inline-block;padding:2px;margin:2px;margin-top:2px;" class="border"><img style="max-width:24px;max-height:24px;"><a style="margin-left: 4px;padding:4px;" onclick="del_ico(this);" class="btn btn-danger fa fa-times">.</a></div>'
              );
              ht.find("img").attr("src", "/" + d.msg);
              ht.find("a").attr("pid", d.msg);
              $(".p-dro3").append(ht);
            } else if (d.msg.includes("sico")) {
              var ht = $(
                '<div style="display:inline-block;padding:2px;margin:2px;margin-top:2px;" class="border"><img style="max-width:24px;max-height:24px;"><a style="margin-left: 4px;padding:4px;" onclick="del_ico(this);" class="btn btn-danger fa fa-times">.</a></div>'
              );
              ht.find("img").attr("src", "/" + d.msg);
              ht.find("a").attr("pid", d.msg);
              $(".p-sico").append(ht);
            }else if (d.msg.includes("atar")) {
              var ht = $(
                '<div style="display:inline-block;padding:2px;margin:2px;margin-top:2px;" class="border"><img style="max-width:24px;max-height:24px;"><a style="margin-left: 4px;padding:4px;" onclick="del_ico(this);" class="btn btn-danger fa fa-times">.</a></div>'
              );
              ht.find("img").attr("src", "/" + d.msg);
              ht.find("a").attr("pid", d.msg);
              $(".p-atar").append(ht);
            }else if (d.msg.includes("back")) {
              var ht = $(
                '<div style="display:inline-block;padding:2px;margin:2px;margin-top:2px;" class="border"><img style="max-width:24px;max-height:24px;"><a style="margin-left: 4px;padding:4px;" onclick="del_ico(this);" class="btn btn-danger fa fa-times">.</a></div>'
              );
              ht.find("img").attr("src", "/" + d.msg);
              ht.find("a").attr("pid", d.msg);
              $(".p-back").append(ht);
            }
            $(sp).remove();
            $(e).remove();
          },
          error: function () {
            $(sp).remove();
          },
        });
      });
  }
  
  function S_PIC(nb) {
    var e = $(
      "<input  accept='image/*' type='file' style='display:none;'/>"
    ).first();
    e.trigger("click");
    var xx;
    $(e).on("change", function () {
      if (nb == "user") {
        $(".spic").attr("src", "imgs/ajax-loader.gif");
      }
      var formData = new FormData();
      formData.append("photo", $(e).prop("files")[0]);
      xx = $.ajax({
        xhr: function () {
          var xhr = new window.XMLHttpRequest();
          xhr.upload.addEventListener(
            "progress",
            function (evt) {
              if (evt.lengthComputable) {
                var percentComplete = evt.loaded / evt.total;
              }
            },
            false
          );
          return xhr;
        },
        timeout: 0,
        url: "uppic?nf=" + nb,
        type: "POST",
        data: formData,
        datatype: "json",
        cache: false,
        processData: false,
        contentType: false,
        success: function (data) {
          setTimeout(() => {
            if (data.split("@")[1] == "user") {
              $(".spic").attr("src", data.split("@")[0]);
              $(".spic").css(
                "background-image",
                "url(" + data.split("@")[0] + ")"
              );
              SEND_EVENT_EMIT("SEND_EVENT_EMIT_PIC", { pic: data.split("@")[0] });
            } else if (data.split("@")[1] == "bot") {
              $(".spicbot").attr("src", data.split("@")[0]);
            } else {
              $(".picroom").attr("src", data.split("@")[0]);
            }
          }, 1e3);
        },
        error: function (e) {
          $(".spic").attr("src", "");
          alert(e.responseJSON.message);
        },
      });
    });
  }
  function getroomuse(data) {
    if (data) {
      const nameroom = C_L_R.findIndex((x) => x.id == data);
      if (nameroom != -1) {
        return C_L_R[nameroom].topic;
      } else {
        return C_L_R[0].topic;
      }
    } else {
      C_L_R[0].topic;
    }
  }
  Number.prototype.time = function () {
    var t = this;
    var d = 0;
    var h = 0;
    var m = 0;
    var s = 0;
    var ret = "";
    d = parseInt(t / 864e5);
    t = t - parseInt(864e5 * d);
    h = parseInt(t / 36e5);
    t = t - parseInt(36e5 * h);
    m = parseInt(t / 6e4);
    t = t - parseInt(6e4 * m);
    s = parseInt(t / 1e3);
    if (h > 9) {
      ret += h + ":";
    } else {
      ret += "0" + h + ":";
    }
    if (m > 9) {
      ret += m + ":";
    } else {
      ret += "0" + m + ":";
    }
    if (s > 9) {
      ret += s;
    } else {
      ret += "0" + s;
    }
    return ret;
  };
  function RefreshPWS() {
    $.get("GET_ALL_USER_ONLINE", function (d) {
      if (typeof d == "string") {
        d = JSON.parse(d);
      }
      var data = d;
      pws = data.powers;

    });
  }
  function PowerRef(jstp) {
    if (jstp.cp) {
      $(".cp").show();
      $(".logsad").show();
      $(".statead").show();
    } else {
      $(".cp").hide();
      $(".logsad").hide();
      $(".statead").hide();
    }
    if (jstp.groups) {
      $(".addGruMsg").show();
    } else {
      $(".addGruMsg").hide();
    }
    if (jstp.edituser) {
      $(".userad").show();
    } else {
      $(".userad").hide();
    }
    if (jstp.ban) {
      $(".bandad").show();
    } else {
      $(".bandad").hide();
    }
    if (jstp.setpower) {
      $(".powerad").show();
    } else {
      $(".powerad").hide();
    }
    if (jstp.flter) {
      $(".filterad,.report").show();
    } else {
      $(".filterad,.report").hide();
    }
    if (jstp.rooms) {
      $(".roomsad").show();
    } else {
      $(".roomsad").hide();
    }
    if (jstp.msgs) {
      $(".msgsad").show();
    } else {
      $(".msgsad").hide();
    }
    if (jstp.shrt) {
      $(".shrtad").show();
    } else {
      $(".shrtad").hide();
    }
    if (jstp.subs) {
      $(".subsad").show();
    } else {
      $(".subsad").hide();
    }
    if (jstp.owner) {
      $(".ownad").show();
    } else {
      $(".ownad").hide();
    }
    if (
      jstp.name == "gochat" ||
      jstp.name == "Hide" ||
      jstp.name == "chatmaster"
    ) {
      $(".owneredit").show();
      $(".emo").show();
      $(".otherad").show();
      $(".ownad1").show();
    } else {
      $(".owneredit").hide();
      $(".emo").hide();
      $(".otherad").hide();
      $(".ownad1").hide();
    }
    if (jstp.report) {
      $(".report").show();
    } else {
      $(".report").hide();
    }
    if (jstp.publicmsg) {
      $(".pmsg").show();
    } else {
      $(".pmsg").hide();
    }
  }
  uf = {
    kw: "الكويت",
    et: "إثيوبيا",
    az: "أذربيجان",
    am: "أرمينيا",
    aw: "أروبا",
    er: "إريتريا",
    es: "أسبانيا",
    au: "أستراليا",
    ee: "إستونيا",
    il: "فلسطين",
    af: "أفغانستان",
    ec: "إكوادور",
    ar: "الأرجنتين",
    jo: "الأردن",
    ae: "الإمارات العربية المتحدة",
    al: "ألبانيا",
    bh: "مملكة البحرين",
    br: "البرازيل",
    pt: "البرتغال",
    ba: "البوسنة والهرسك",
    ga: "الجابون",
    dz: "الجزائر",
    dk: "الدانمارك",
    cv: "الرأس الأخضر",
    ps: "فلسطين",
    sv: "السلفادور",
    sn: "السنغال",
    sd: "السودان",
    se: "السويد",
    so: "الصومال",
    cn: "الصين",
    iq: "العراق",
    ph: "الفلبين",
    cm: "الكاميرون",
    cg: "الكونغو",
    cd: "جمهورية الكونغو الديمقراطية",
    de: "ألمانيا",
    hu: "المجر",
    ma: "المغرب",
    mx: "المكسيك",
    sa: "المملكة العربية السعودية",
    uk: "المملكة المتحدة",
    gb: "المملكة المتحدة",
    no: "النرويج",
    at: "النمسا",
    ne: "النيجر",
    in: "الهند",
    us: "الولايات المتحدة",
    jp: "اليابان",
    ye: "اليمن",
    gr: "اليونان",
    ag: "أنتيغوا وبربودا",
    id: "إندونيسيا",
    ao: "أنغولا",
    ai: "أنغويلا",
    uy: "أوروجواي",
    uz: "أوزبكستان",
    ug: "أوغندا",
    ua: "أوكرانيا",
    ir: "إيران",
    ie: "أيرلندا",
    is: "أيسلندا",
    it: "إيطاليا",
    pg: "بابوا-غينيا الجديدة",
    py: "باراجواي",
    bb: "باربادوس",
    pk: "باكستان",
    pw: "بالاو",
    bm: "برمودا",
    bn: "بروناي",
    be: "بلجيكا",
    bg: "بلغاريا",
    bd: "بنجلاديش",
    pa: "بنما",
    bj: "بنين",
    bt: "بوتان",
    bw: "بوتسوانا",
    pr: "بورتو ريكو",
    bf: "بوركينا فاسو",
    bi: "بوروندي",
    pl: "بولندا",
    bo: "بوليفيا",
    pf: "بولينزيا الفرنسية",
    pe: "بيرو",
    by: "بيلاروس",
    bz: "بيليز",
    th: "تايلاند",
    tw: "تايوان",
    tm: "تركمانستان",
    tr: "تركيا",
    tt: "ترينيداد وتوباجو",
    td: "تشاد",
    cl: "تشيلي",
    tz: "تنزانيا",
    tg: "توجو",
    tv: "توفالو",
    tk: "توكيلاو",
    to: "تونجا",
    tn: "تونس",
    tp: "تيمور الشرقية",
    jm: "جامايكا",
    gm: "جامبيا",
    gl: "جرينلاند",
    pn: "جزر البتكارين",
    bs: "جزر البهاما",
    km: "جزر القمر",
    cf: "أفريقيا الوسطى",
    cz: "جمهورية التشيك",
    do: "جمهورية الدومينيكان",
    za: "جنوب أفريقيا",
    gt: "جواتيمالا",
    gp: "جواديلوب",
    gu: "جوام",
    ge: "جورجيا",
    gs: "جورجيا الجنوبية",
    gy: "جيانا",
    gf: "جيانا الفرنسية",
    dj: "جيبوتي",
    je: "جيرسي",
    gg: "جيرنزي",
    va: "دولة الفاتيكان",
    dm: "دومينيكا",
    rw: "رواندا",
    ru: "روسيا",
    ro: "رومانيا",
    re: "ريونيون",
    zm: "زامبيا",
    zw: "زيمبابوي",
    ws: "ساموا",
    sm: "سان مارينو",
    sk: "سلوفاكيا",
    si: "سلوفينيا",
    sg: "سنغافورة",
    sz: "سوازيلاند",
    sy: "سوريا",
    sr: "سورينام",
    ch: "سويسرا",
    sl: "سيراليون",
    lk: "سيريلانكا",
    sc: "سيشل",
    rs: "صربيا",
    tj: "طاجيكستان",
    om: "عمان",
    gh: "غانا",
    gd: "غرينادا",
    gn: "غينيا",
    gq: "غينيا الاستوائية",
    gw: "غينيا بيساو",
    vu: "فانواتو",
    fr: "فرنسا",
    ve: "فنزويلا",
    fi: "فنلندا",
    vn: "فيتنام",
    cy: "قبرص",
    qa: "قطر",
    kg: "قيرقيزستان",
    kz: "كازاخستان",
    nc: "كاليدونيا الجديدة",
    kh: "كامبوديا",
    hr: "كرواتيا",
    ca: "كندا",
    cu: "كوبا",
    ci: "ساحل العاج",
    kr: "كوريا",
    kp: "كوريا الشمالية",
    cr: "كوستاريكا",
    co: "كولومبيا",
    ki: "كيريباتي",
    ke: "كينيا",
    lv: "لاتفيا",
    la: "لاوس",
    lb: "لبنان",
    li: "لشتنشتاين",
    lu: "لوكسمبورج",
    ly: "ليبيا",
    lr: "ليبيريا",
    lt: "ليتوانيا",
    ls: "ليسوتو",
    mq: "مارتينيك",
    mo: "ماكاو",
    fm: "ماكرونيزيا",
    mw: "مالاوي",
    mt: "مالطا",
    ml: "مالي",
    my: "ماليزيا",
    yt: "مايوت",
    mg: "مدغشقر",
    eg: "مصر",
    mk: "مقدونيا، يوغوسلافيا",
    mn: "منغوليا",
    mr: "موريتانيا",
    mu: "موريشيوس",
    mz: "موزمبيق",
    md: "مولدوفا",
    mc: "موناكو",
    ms: "مونتسيرات",
    me: "مونتينيغرو",
    mm: "ميانمار",
    na: "ناميبيا",
    nr: "ناورو",
    np: "نيبال",
    ng: "نيجيريا",
    ni: "نيكاراجوا",
    nu: "نيوا",
    nz: "نيوزيلندا",
    ht: "هايتي",
    hn: "هندوراس",
    nl: "هولندا",
    hk: "هونغ كونغ",
    wf: "واليس وفوتونا",
  };
