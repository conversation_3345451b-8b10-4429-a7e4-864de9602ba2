// إضافة وظيفة قفل المايك
function initMicLockSystem() {
    // التحقق من وجود المتغير socket قبل استخدامه
    if (typeof socket === 'undefined' || socket === null) {
        console.log('تأجيل تهيئة نظام قفل المايك حتى يتم تهيئة الاتصال');
        setTimeout(initMicLockSystem, 1000); // إعادة المحاولة بعد ثانية
        return; // الخروج من الدالة إذا كان socket غير متاح
    }

    // إضافة زر قفل المايك إلى قائمة خيارات المايك
    function addLockButton(micId) {
        const micElement = $(`.broadcasters .prod[data='${micId}']`);
        if (!micElement.length) return;
        
        const showpfMenu = micElement.find('#showpf');
        if (!showpfMenu.length) return;
        
        // التحقق من وجود الزر قبل إضافته
        if (showpfMenu.find('.mic-lock').length === 0) {
            showpfMenu.append(`
                <i class="mic-lock fa fa-lock hand nosel" style="margin: 2px;padding: 4px;" title="قفل المايك"></i>
            `);
        }
    }

    // تحديث حالة زر القفل
    function updateLockButton(micId, isLocked) {
        const micElement = $(`.broadcasters .prod[data='${micId}']`);
        if (!micElement.length) return;
        
        const lockButton = micElement.find('.mic-lock');
        if (!lockButton.length) return;
        
        if (isLocked) {
            lockButton.removeClass('fa-unlock').addClass('fa-lock');
            lockButton.attr('title', 'فتح المايك');
        } else {
            lockButton.removeClass('fa-lock').addClass('fa-unlock');
            lockButton.attr('title', 'قفل المايك');
        }
    }

    // معالجة النقر على زر القفل
    $(document).on('click', '.mic-lock', function() {
        const micElement = $(this).closest('.prod');
        if (!micElement.length) return;
        
        const micId = micElement.attr('data');
        const isLocked = $(this).hasClass('fa-lock');

        // إرسال حالة القفل إلى السيرفر
        socket.emit('mic_lock', {
            cmd: isLocked ? 'unlock' : 'lock',
            mic_id: micId,
            topic: M_ROOM
        });
    });

    // استقبال تحديثات حالة القفل من السيرفر
    if (socket.hasOwnProperty('on')) {
        socket.on('mic_lock_update', function(data) {
            if (!data || !data.mic_id) return;
            updateLockButton(data.mic_id, data.is_locked);
        });

        // إضافة أزرار القفل عند تحميل المايكات
        socket.on('broadcasting', function(data) {
            if (data && data.cmd === 'all') {
                // إضافة أزرار القفل لجميع المايكات
                setTimeout(function() {
                    for (let i = 1; i <= 6; i++) {
                        addLockButton(i);
                    }
                }, 1000); // تأخير لضمان تحميل العناصر
            }
        });
    }
}

// تفعيل نظام قفل المايك عند تحميل الصفحة
$(document).ready(function() {
    // تأخير تهيئة نظام قفل المايك للتأكد من تهيئة socket أولاً
    setTimeout(function() {
        initMicLockSystem();
    }, 2000); // انتظار ثانيتين للتأكد من تهيئة socket
});