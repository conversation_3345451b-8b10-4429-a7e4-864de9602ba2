<div style="position: relative; display: inline-block;" class="<%=name%>-container">
<!-- this is the button to use to show color popup -->
<input class="<%=name%>" type="hidden" name="<%=name%>" value="#000" />
<button onclick="toggleColorPicker('.<%=name%>')" class="cpick border btn <%=name%>" style="
    outline: none;
    width: 70px;
    margin: 0px 4px;
    height: 25px;
    position: absolute;
    
    top: 1px;
    left: 0;
" v="#000"></button>
</div>